:root {
    --accent-color: oklch(0.72 0.1158 211.76);
    --accent-color-2: oklch(from var(--accent-color) l c calc(h - 90));
    --dull-factor: 8;
    --page-bg: oklch(from var(--accent-color) 1 calc(c/var(--dull-factor)) h);
    --layer-1-bg: oklch(from var(--accent-color) 0.8 calc(c/var(--dull-factor)) h);
    --text-color: black;
    --text-color--inverse: white;
    --text-min-vis-color: oklch(from var(--text-color) 0.5 c h);
}

@layer base {

    html,
    body {
        background: var(--page-bg);
        color: var(--text-color);
    }

    a {
        color: var(--accent-color);
        transition: color 0.3s ease;
    }

    a .link_host {
        color: oklch(from var(--accent-color) 1 c h);
        text-decoration: underline;
    }

    a:hover {
        color: oklch(from var(--accent-color) calc(l + 0.1) c h);
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --page-bg: oklch(from var(--accent-color) 0.1 calc(c/var(--dull-factor)) h);
        --layer-1-bg: oklch(from var(--accent-color) 0.2 calc(c/var(--dull-factor)) h);
        --text-color: white;
        --text-color--inverse: black;
    }
}