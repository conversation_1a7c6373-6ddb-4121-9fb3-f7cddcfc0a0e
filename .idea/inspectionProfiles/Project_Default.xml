<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="CallableParameterUseCaseInTypeContextInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="CompactCanBeUsedInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="ComparisonOperandsOrderInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="CssFloatPxLength" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="CssInvalidPropertyValue" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CssNoGenericFontName" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="CssNonIntegerLengthInPixels" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="CssUnknownProperty" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myCustomPropertiesEnabled" value="true" />
      <option name="myIgnoreVendorSpecificProperties" value="false" />
      <option name="myCustomPropertiesList">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="content-visibility" />
            <item index="1" class="java.lang.String" itemvalue="scroll-width" />
            <item index="2" class="java.lang.String" itemvalue="stroke-color" />
            <item index="3" class="java.lang.String" itemvalue="transform" />
            <item index="4" class="java.lang.String" itemvalue="animation" />
            <item index="5" class="java.lang.String" itemvalue="view-transition-name" />
            <item index="6" class="java.lang.String" itemvalue="initial-value" />
            <item index="7" class="java.lang.String" itemvalue="inherits" />
            <item index="8" class="java.lang.String" itemvalue="syntax" />
            <item index="9" class="java.lang.String" itemvalue="leading-trim" />
            <item index="10" class="java.lang.String" itemvalue="text-edge" />
            <item index="11" class="java.lang.String" itemvalue="animation-timeline" />
            <item index="12" class="java.lang.String" itemvalue="interpolate-size" />
            <item index="13" class="java.lang.String" itemvalue="d" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="CssUnknownTarget" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DuplicatedCode" enabled="false" level="WEAK WARNING" enabled_by_default="false">
      <Languages>
        <language isEnabled="false" name="Style Sheets" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="EmptyDirectory" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ForgottenDebugOutputInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="configuration">
        <list>
          <option value="\Codeception\Util\Debug::debug" />
          <option value="\Codeception\Util\Debug::pause" />
          <option value="\Doctrine\Common\Util\Debug::dump" />
          <option value="\Doctrine\Common\Util\Debug::export" />
          <option value="\Illuminate\Support\Debug\Dumper::dump" />
          <option value="\Symfony\Component\Debug\Debug::enable" />
          <option value="\Symfony\Component\Debug\DebugClassLoader::enable" />
          <option value="\Symfony\Component\Debug\ErrorHandler::register" />
          <option value="\Symfony\Component\Debug\ExceptionHandler::register" />
          <option value="\TYPO3\CMS\Core\Utility\DebugUtility::debug" />
          <option value="\Zend\Debug\Debug::dump" />
          <option value="\Zend\Di\Display\Console::export" />
          <option value="dd" />
          <option value="debug_print_backtrace" />
          <option value="debug_zval_dump" />
          <option value="dpm" />
          <option value="dpq" />
          <option value="dsm" />
          <option value="dump" />
          <option value="dvm" />
          <option value="error_log" />
          <option value="kpr" />
          <option value="phpinfo" />
          <option value="print_r" />
          <option value="var_dump" />
          <option value="var_export" />
          <option value="wp_die" />
          <option value="xdebug_break" />
          <option value="xdebug_call_class" />
          <option value="xdebug_call_file" />
          <option value="xdebug_call_function" />
          <option value="xdebug_call_line" />
          <option value="xdebug_code_coverage_started" />
          <option value="xdebug_debug_zval" />
          <option value="xdebug_debug_zval_stdout" />
          <option value="xdebug_dump_superglobals" />
          <option value="xdebug_enable" />
          <option value="xdebug_get_code_coverage" />
          <option value="xdebug_get_collected_errors" />
          <option value="xdebug_get_declared_vars" />
          <option value="xdebug_get_function_stack" />
          <option value="xdebug_get_headers" />
          <option value="xdebug_get_monitored_functions" />
          <option value="xdebug_get_profiler_filename" />
          <option value="xdebug_get_stack_depth" />
          <option value="xdebug_get_tracefile_name" />
          <option value="xdebug_is_enabled" />
          <option value="xdebug_memory_usage" />
          <option value="xdebug_peak_memory_usage" />
          <option value="xdebug_print_function_stack" />
          <option value="xdebug_start_code_coverage" />
          <option value="xdebug_start_error_collection" />
          <option value="xdebug_start_function_monitor" />
          <option value="xdebug_start_trace" />
          <option value="xdebug_stop_code_coverage" />
          <option value="xdebug_stop_error_collection" />
          <option value="xdebug_stop_function_monitor" />
          <option value="xdebug_stop_trace" />
          <option value="xdebug_time_index" />
          <option value="xdebug_var_dump" />
        </list>
      </option>
      <option name="migratedIntoUserSpace" value="true" />
    </inspection_tool>
    <inspection_tool class="FunctionErrorSilencedInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="GrazieInspection" enabled="false" level="TYPO" enabled_by_default="false" />
    <inspection_tool class="HtmlDeprecatedAttribute" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="HtmlFormInputWithoutLabel" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="HtmlUnknownAnchorTarget" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="17">
            <item index="0" class="java.lang.String" itemvalue="frameborder" />
            <item index="1" class="java.lang.String" itemvalue="allowfullscreen" />
            <item index="2" class="java.lang.String" itemvalue="xlink:href" />
            <item index="3" class="java.lang.String" itemvalue="xhtml:link" />
            <item index="4" class="java.lang.String" itemvalue="\\" />
            <item index="5" class="java.lang.String" itemvalue="xsi:schemalocation" />
            <item index="6" class="java.lang.String" itemvalue="amp" />
            <item index="7" class="java.lang.String" itemvalue="amp-boilerplate" />
            <item index="8" class="java.lang.String" itemvalue="amp-custom" />
            <item index="9" class="java.lang.String" itemvalue="height" />
            <item index="10" class="java.lang.String" itemvalue="[text]" />
            <item index="11" class="java.lang.String" itemvalue="on" />
            <item index="12" class="java.lang.String" itemvalue="placeholder" />
            <item index="13" class="java.lang.String" itemvalue="canhide" />
            <item index="14" class="java.lang.String" itemvalue="border" />
            <item index="15" class="java.lang.String" itemvalue="autocomplete" />
            <item index="16" class="java.lang.String" itemvalue="popover" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="18">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="sg-spoiler" />
            <item index="7" class="java.lang.String" itemvalue="slot" />
            <item index="8" class="java.lang.String" itemvalue="sg-tags-input" />
            <item index="9" class="java.lang.String" itemvalue="sitemapindex" />
            <item index="10" class="java.lang.String" itemvalue="lite-youtube" />
            <item index="11" class="java.lang.String" itemvalue="amp-accordion" />
            <item index="12" class="java.lang.String" itemvalue="pjsdiv" />
            <item index="13" class="java.lang.String" itemvalue="spoiler" />
            <item index="14" class="java.lang.String" itemvalue="amp-addthis" />
            <item index="15" class="java.lang.String" itemvalue="styled-checkbox" />
            <item index="16" class="java.lang.String" itemvalue="sg-search-highlight" />
            <item index="17" class="java.lang.String" itemvalue="number-input" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownTarget" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="HttpUrlsUsage" enabled="false" level="WEAK WARNING" enabled_by_default="false">
      <option name="ignoredUrls">
        <list>
          <option value="http://localhost" />
          <option value="http://127.0.0.1" />
          <option value="http://0.0.0.0" />
          <option value="http://www.w3.org/" />
          <option value="http://json-schema.org/draft" />
          <option value="http://java.sun.com/" />
          <option value="http://xmlns.jcp.org/" />
          <option value="http://javafx.com/javafx/" />
          <option value="http://javafx.com/fxml" />
          <option value="http://maven.apache.org/xsd/" />
          <option value="http://maven.apache.org/POM/" />
          <option value="http://www.springframework.org/schema/" />
          <option value="http://www.springframework.org/tags" />
          <option value="http://www.springframework.org/security/tags" />
          <option value="http://www.thymeleaf.org" />
          <option value="http://www.jboss.org/j2ee/schema/" />
          <option value="http://www.jboss.com/xml/ns/" />
          <option value="http://www.ibm.com/webservices/xsd" />
          <option value="http://activemq.apache.org/schema/" />
          <option value="http://schema.cloudfoundry.org/spring/" />
          <option value="http://schemas.xmlsoap.org/" />
          <option value="http://cxf.apache.org/schemas/" />
          <option value="http://primefaces.org/ui" />
          <option value="http://tiles.apache.org/" />
          <option value="http://www.sitemaps.org" />
          <option value="http://stopforum.ru" />
          <option value="http://forum.stopgame.ru" />
          <option value="http://my.stopgame.ru" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="IncorrectFormatting" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="IsEmptyFunctionUsageInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="IsNullFunctionUsageInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="JSUnusedGlobalSymbols" enabled="true" level="INFORMATION" enabled_by_default="true" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MagicMethodsValidityInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="MessDetectorValidationInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false">
      <option name="CODESIZE" value="true" />
      <option name="DESIGN" value="true" />
    </inspection_tool>
    <inspection_tool class="MissingParentCallInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MissingTranslationsInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="NativeMemberUsageInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="NestedAssignmentsUsageInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="NestedNotOperatorsInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="NestedTernaryOperatorInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="NonAsciiCharacters" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="NullPointerExceptionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="NullableTypeFormatInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="nullableTypeFormat" value="SHORT" />
    </inspection_tool>
    <inspection_tool class="ParameterDefaultsNullInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpAccessStaticViaInstanceInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpArrayFillCanBeConvertedToLoopInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpArrayFilterCanBeConvertedToLoopInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpArrayMapCanBeConvertedToLoopInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpArrayShapeCanBeAddedInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpAssignmentInConditionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpAssignmentReplaceableWithOperatorAssignmentInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpAssignmentReplaceableWithPrefixExpressionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpCSValidationInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false">
      <option name="CODING_STANDARD" value="Custom" />
      <option name="CUSTOM_RULESET_PATH" value="$PROJECT_DIR$/../../yii2-coding-standards/Yii2/ruleset.xml" />
      <option name="EXTENSIONS" value="php,inc" />
    </inspection_tool>
    <inspection_tool class="PhpClassConstantCanBeFinalInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpClosureCanBeConvertedToShortArrowFunctionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpCombineMultipleIssetCallsIntoOneInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpCompoundNamespaceDepthInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpDangerousArrayInitializationInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PhpDisabledExtensionStubsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpDivisionByZeroInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpDocSignatureIsNotCompleteInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpEchoOpenTagInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpExpressionWithoutClarifyingParenthesesInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PhpFeatureEnvyLocalInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpFieldCanBePromotedInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpFullyQualifiedNameUsageInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="IGNORE_GLOBAL_NAMESPACE" value="true" />
    </inspection_tool>
    <inspection_tool class="PhpGetClassCanBeReplacedWithClassNameLiteralInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpIfCanBeMergedWithSequentialConditionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpIfCanBeReplacedWithMatchExpressionInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpImplicitOctalLiteralUsageInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PhpIncludeInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpLongTypeFormInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpLoopCanBeConvertedToArrayFillInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpLoopCanBeConvertedToArrayFilterInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpLoopCanBeConvertedToArrayMapInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpMatchExpressionCanBeReplacedWithTernaryInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PhpMethodMayBeStaticInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpMethodOrClassCallIsNotCaseSensitiveInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpMissingDocCommentInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="CHECK_FUNCTION" value="false" />
      <option name="CHECK_CLASS" value="false" />
      <option name="CHECK_METHOD" value="false" />
    </inspection_tool>
    <inspection_tool class="PhpMissingVisibilityInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpModifierOrderInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpMultipleClassDeclarationsInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpNModifierCanBeReplacedWithNonCapturingGroupInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpNewClassMissingParameterListInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpNonCanonicalElementsOrderInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpObjectShapeCanBeAddedInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpOverridingMethodVisibilityInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpPluralMixedCanBeReplacedWithArrayInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpPowCallCanBeReplacedWithOperatorInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpPromotedFieldUsageInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpPureAttributeCanBeAddedInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpRedundantDocCommentInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpSeparateElseIfInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpShortOpenEchoTagInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpSingleStatementWithBracesInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpStanGlobal" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpStatementWithoutBracesInspection" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="PhpStaticAsDynamicMethodCallInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpSwitchCaseWithoutDefaultBranchInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpTraditionalSyntaxArrayLiteralInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpTraitsUseListInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpUndefinedCallbackInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpUnitMissingTargetForTestInspection" enabled="false" level="INFORMATION" enabled_by_default="false" />
    <inspection_tool class="PhpUnnecessaryDoubleQuotesInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpUnnecessaryElseBranchInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpUnnecessaryParenthesesInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" editorAttributes="INFO_ATTRIBUTES" />
    <inspection_tool class="PhpUsageOfSilenceOperatorInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpVarUsageInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PostCssCustomProperties" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="RegExpRedundantEscape" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="ReturnTernaryReplacementInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="ReturnTypeCanBeDeclaredInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="SecurityAdvisoriesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="optionConfiguration">
        <list>
          <option value="barryvdh/laravel-debugbar" />
          <option value="behat/behat" />
          <option value="brianium/paratest" />
          <option value="codeception/codeception" />
          <option value="codedungeon/phpunit-result-printer" />
          <option value="composer/composer" />
          <option value="doctrine/coding-standard" />
          <option value="filp/whoops" />
          <option value="friendsofphp/php-cs-fixer" />
          <option value="humbug/humbug" />
          <option value="infection/infection" />
          <option value="jakub-onderka/php-parallel-lint" />
          <option value="johnkary/phpunit-speedtrap" />
          <option value="kalessil/production-dependencies-guard" />
          <option value="mikey179/vfsStream" />
          <option value="mockery/mockery" />
          <option value="mybuilder/phpunit-accelerator" />
          <option value="orchestra/testbench" />
          <option value="pdepend/pdepend" />
          <option value="phan/phan" />
          <option value="phing/phing" />
          <option value="phpcompatibility/php-compatibility" />
          <option value="phpmd/phpmd" />
          <option value="phpro/grumphp" />
          <option value="phpspec/phpspec" />
          <option value="phpspec/prophecy" />
          <option value="phpstan/phpstan" />
          <option value="phpunit/phpunit" />
          <option value="povils/phpmnd" />
          <option value="roave/security-advisories" />
          <option value="satooshi/php-coveralls" />
          <option value="sebastian/phpcpd" />
          <option value="slevomat/coding-standard" />
          <option value="spatie/phpunit-watcher" />
          <option value="squizlabs/php_codesniffer" />
          <option value="sstalle/php7cc" />
          <option value="symfony/debug" />
          <option value="symfony/maker-bundle" />
          <option value="symfony/phpunit-bridge" />
          <option value="symfony/var-dumper" />
          <option value="vimeo/psalm" />
          <option value="wimg/php-compatibility" />
          <option value="wp-coding-standards/wpcs" />
          <option value="yiisoft/yii2-coding-standards" />
          <option value="yiisoft/yii2-debug" />
          <option value="yiisoft/yii2-gii" />
          <option value="zendframework/zend-coding-standard" />
          <option value="zendframework/zend-debug" />
          <option value="zendframework/zend-test" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="SensitiveParameterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="ShortEchoTagCanBeUsedInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="SpellCheckingInspection" enabled="false" level="TYPO" enabled_by_default="false">
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
    <inspection_tool class="SqlResolveInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="suppressForPossibleStringLiterals" value="true" />
    </inspection_tool>
    <inspection_tool class="StringCurlyInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="SuspiciousBinaryOperationInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="TodoComment" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="TranslationsCorrectnessInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="TypeUnsafeArraySearchInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="UnNecessaryDoubleQuotesInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="UnknownInspectionInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="UnnecessaryCastingInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="UnnecessarySemicolonInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="UnqualifiedReferenceInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="REPORT_ALL_FUNCTIONS" value="true" />
      <option name="REPORT_CONSTANTS" value="true" />
    </inspection_tool>
    <inspection_tool class="UntrustedInclusionInspection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedTranslationsInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
  </profile>
</component>