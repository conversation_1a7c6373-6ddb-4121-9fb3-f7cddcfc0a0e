<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JsonSchemaMappingsProjectConfiguration">
    <state>
      <map>
        <entry key="package">
          <value>
            <SchemaInfo>
              <option name="name" value="package" />
              <option name="relativePathToSchema" value="http://json.schemastore.org/package" />
              <option name="applicationDefined" value="true" />
              <option name="patterns">
                <list>
                  <Item>
                    <option name="path" value="package.json" />
                  </Item>
                </list>
              </option>
            </SchemaInfo>
          </value>
        </entry>
      </map>
    </state>
  </component>
</project>