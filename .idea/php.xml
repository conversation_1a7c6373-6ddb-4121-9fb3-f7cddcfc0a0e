<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="codeSize" value="true" />
    <option name="design" value="true" />
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="codingStandard" value="Custom" />
    <option name="customRuleset" value="$PROJECT_DIR$/../../yii2-coding-standards/Yii2/ruleset.xml" />
    <option name="extensions" value="php,inc" />
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="3a50a0e8-c5d4-4adc-8951-cd0b6965b4fc" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpStan">
    <PhpStan_settings>
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="3a50a0e8-c5d4-4adc-8951-cd0b6965b4fc" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="3a50a0e8-c5d4-4adc-8951-cd0b6965b4fc" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>