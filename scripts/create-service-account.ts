#!/usr/bin/env node

import { createServiceAccount } from './cli-auth';

async function main() {
  console.log('🔧 Создание сервисного аккаунта FireShark\n');

  try {
    console.log('📝 Создание сервисного аккаунта...');

    // Создание сервисного аккаунта
    const serviceAccount = await createServiceAccount();

    console.log('✅ Сервисный аккаунт успешно создан!');
    console.log(`👤 ID: ${serviceAccount.id}`);
    console.log(`🏷️  Username: ${serviceAccount.username}`);
    console.log(`📛 Отображаемое имя: ${serviceAccount.showName}`);
    console.log(`🔐 Ключи ActivityPub: ${serviceAccount.publicKey ? '✅ Сгенерированы' : '❌ Не сгенерированы'}`);
    console.log(`📝 URI, inbox и outbox будут созданы динамически при первом использовании`);
    console.log(`🔒 Пароль: отсутствует (вход невозможен)`);
    
  } catch (error: any) {
    console.error('❌ Ошибка при создании сервисного аккаунта:', error.message);
    process.exit(1);
  }
}

main().catch((error) => {
  console.error('❌ Неожиданная ошибка:', error);
  process.exit(1);
});
