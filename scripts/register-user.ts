#!/usr/bin/env node

import { createLocalUser } from './cli-auth';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function questionHidden(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();

    let password = '';

    const onData = function(char: Buffer) {
      const charStr = char.toString();

      switch (charStr) {
        case '\n':
        case '\r':
        case '\u0004': // Ctrl+D
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdin.removeListener('data', onData);
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003': // Ctrl+C
          process.stdout.write('\n');
          process.exit();
        case '\u007f': // Backspace
        case '\b':
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          // Проверяем, что это печатный символ
          if (charStr >= ' ' && charStr <= '~') {
            password += charStr;
            // Стираем введенный символ и заменяем звездочкой
            process.stdout.write('\b*');
          }
          break;
      }
    };

    process.stdin.on('data', onData);
  });
}

async function main() {
  console.log('🔥 Регистрация нового пользователя FireShark\n');

  try {
    // Запрос username
    const username = await question('Username (только латиница, цифры, подчёркивания и дефисы): ');
    
    if (!username.trim()) {
      console.error('❌ Username не может быть пустым');
      process.exit(1);
    }

    // Запрос showName
    const showName = await question('Отображаемое имя: ');
    
    if (!showName.trim()) {
      console.error('❌ Отображаемое имя не может быть пустым');
      process.exit(1);
    }

    // Запрос пароля
    const password = await questionHidden('Пароль (минимум 8 символов): ');
    
    if (!password) {
      console.error('❌ Пароль не может быть пустым');
      process.exit(1);
    }

    // Подтверждение пароля
    const confirmPassword = await questionHidden('Подтвердите пароль: ');
    
    if (password !== confirmPassword) {
      console.error('❌ Пароли не совпадают');
      process.exit(1);
    }

    console.log('\n📝 Создание пользователя...');

    // Создание пользователя
    const newUser = await createLocalUser(username.trim(), showName.trim(), password);

    console.log('✅ Пользователь успешно создан!');
    console.log(`👤 ID: ${newUser.id}`);
    console.log(`🏷️  Username: ${newUser.username}`);
    console.log(`📛 Отображаемое имя: ${newUser.showName}`);
    console.log(`🔐 Ключи ActivityPub: ${newUser.publicKey ? '✅ Сгенерированы' : '❌ Не сгенерированы'}`);
    console.log(`📝 URI, inbox и outbox будут созданы динамически при первом использовании`);
    
  } catch (error: any) {
    console.error('❌ Ошибка при создании пользователя:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main().catch((error) => {
  console.error('❌ Неожиданная ошибка:', error);
  process.exit(1);
});
