import bcrypt from 'bcrypt';
import { db } from './cli-db';
import { user } from '../src/lib/server/db/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { generateActorKeyPair, getKeyGenerationConfig } from '../src/lib/activitypub/utils/crypto';
import { getFederationConfig } from '../src/lib/activitypub/config/federation';

const SALT_ROUNDS = 12;

/**
 * Валидация username
 */
export function validateUsername(username: string): boolean {
  const usernameRegex = /^[a-zA-Z0-9_-]+$/;
  return usernameRegex.test(username) && username.length >= 1 && username.length <= 50;
}

/**
 * Валидация пароля
 */
export function validatePassword(password: string): boolean {
  return password.length >= 8 && password.length <= 128;
}

/**
 * Создание локального пользователя
 */
export async function createLocalUser(username: string, showName: string, password: string) {
  // Валидация
  if (!validateUsername(username)) {
    throw new Error('Неверный формат username. Используйте только латинские буквы, цифры, подчёркивания и дефисы.');
  }

  if (!validatePassword(password)) {
    throw new Error('Пароль должен содержать от 8 до 128 символов.');
  }

  if (!showName.trim()) {
    throw new Error('Отображаемое имя не может быть пустым.');
  }

  // Проверка на существование локального пользователя (domain = null)
  const existingUser = await db
    .select()
    .from(user)
    .where(and(
      eq(user.username, username),
      isNull(user.domain)
    ))
    .limit(1);

  if (existingUser.length > 0) {
    throw new Error(`Локальный пользователь с username "${username}" уже существует.`);
  }

  // Генерация ключей ActivityPub
  console.log('🔐 Генерация ключей ActivityPub...');
  const keyGenerationConfig = getKeyGenerationConfig();
  // Используем временный URI для генерации ключей, реальный URI будет создан динамически
  const tempUri = `temp://local-user/${username}`;
  const keyPair = await generateActorKeyPair(tempUri, keyGenerationConfig);

  // Хеширование пароля
  const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);

  // Создание пользователя
  const newUsers = await db
    .insert(user)
    .values({
      username,
      showName: showName.trim(),
      password: hashedPassword,
      // ActivityPub поля для локального пользователя
      domain: null, // null для локальных пользователей
      uri: null, // будет создан динамически
      url: null, // будет создан динамически
      inbox: null, // будет создан динамически
      outbox: null, // будет создан динамически
      // Ключи ActivityPub
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
      // Остальные поля
      avatar: null,
      banner: null,
      summary: null,
      isCat: false,
    })
    .returning();

  console.log('🔐 Ключи ActivityPub успешно сгенерированы и сохранены');

  return newUsers[0];
}

/**
 * Создание сервисного аккаунта
 */
export async function createServiceAccount() {
  const username = 'service';
  const showName = 'FireShark Service Account';

  // Проверка на существование сервисного аккаунта
  const existingUser = await db
    .select()
    .from(user)
    .where(and(
      eq(user.username, username),
      isNull(user.domain)
    ))
    .limit(1);

  if (existingUser.length > 0) {
    throw new Error(`Сервисный аккаунт уже существует.`);
  }

  // Генерация ключей ActivityPub
  console.log('🔐 Генерация ключей ActivityPub для сервисного аккаунта...');
  const keyGenerationConfig = getKeyGenerationConfig();
  // Используем временный URI для генерации ключей, реальный URI будет создан динамически
  const tempUri = `temp://service-account/${username}`;
  const keyPair = await generateActorKeyPair(tempUri, keyGenerationConfig);

  // Создание сервисного аккаунта (без пароля)
  const newUsers = await db
    .insert(user)
    .values({
      username,
      showName: showName.trim(),
      password: null, // нет пароля - вход невозможен
      // ActivityPub поля для сервисного аккаунта
      domain: null, // null для локальных пользователей
      uri: null, // будет создан динамически
      url: null, // будет создан динамически
      inbox: null, // будет создан динамически
      outbox: null, // будет создан динамически
      // Ключи ActivityPub
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
      // Остальные поля
      avatar: null,
      banner: null,
      summary: 'Сервисный аккаунт для подписи федеративных запросов',
      isCat: false,
    })
    .returning();

  console.log('🔐 Ключи ActivityPub для сервисного аккаунта успешно сгенерированы и сохранены');

  return newUsers[0];
}

/**
 * Аутентификация пользователя
 */
export async function authenticateUser(username: string, password: string) {
  const foundUser = await db
    .select()
    .from(user)
    .where(and(
      eq(user.username, username),
      isNull(user.domain) // только локальные пользователи
    ))
    .limit(1);

  if (foundUser.length === 0) {
    return null; // пользователь не найден
  }

  const userData = foundUser[0];

  if (!userData.password) {
    return null; // у пользователя нет пароля (возможно, это ActivityPub пользователь)
  }

  const isPasswordValid = await bcrypt.compare(password, userData.password);
  if (!isPasswordValid) {
    return null; // неверный пароль
  }

  // Возвращаем пользователя без пароля
  const { password: _, ...userWithoutPassword } = userData;
  return userWithoutPassword;
}
