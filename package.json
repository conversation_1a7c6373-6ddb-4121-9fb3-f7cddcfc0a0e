{"name": "fireshark", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "register-user": "TEST=true ./node_modules/.bin/vite-node scripts/register-user.ts", "create-service-account": "TEST=true ./node_modules/.bin/vite-node scripts/create-service-account.ts"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@types/node": "^22", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "drizzle-kit": "^0.30.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "jsdom": "^26.1.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tsx": "^4.20.3", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "vite-node": "^3.2.4", "vite-plugin-devtools-json": "^0.2.0", "vitest": "^3.2.4"}, "dependencies": {"@types/bcrypt": "^6.0.0", "bcrypt": "^6.0.0", "drizzle-orm": "^0.40.0", "fast-average-color": "^9.5.0", "fast-average-color-node": "^3.1.0", "image-size": "^2.0.2", "lucide-svelte": "^0.536.0", "postgres": "^3.4.5"}, "pnpm": {"onlyBuiltDependencies": ["bcrypt", "esbuild"]}}