# ActivityPub Undo Activity Implementation

## Overview

This document describes the implementation of ActivityPub Undo activity processing in the Fireshark project. The implementation follows the ActivityPub specification and Mastodon's documentation for handling Undo activities.

## Supported Undo Types

According to the ActivityPub specification and Mastodon documentation, the following activity types can be undone:

### 1. Undo Like
- **Purpose**: Remove a previously given like/favorite from a post
- **Effect**: 
  - Marks the reaction record as inactive in the database
  - Decrements the post's like count
  - Records the undo timestamp

### 2. Undo Announce (Boost/Share)
- **Purpose**: Remove a previously made announcement/boost/share of a post
- **Effect**:
  - Marks the reaction record as inactive in the database
  - Decrements the post's share count
  - Records the undo timestamp

### 3. Undo Follow
- **Purpose**: Unfollow a previously followed user
- **Effect**:
  - Removes the follow relationship from the database
  - No longer receives updates from the unfollowed user

### 4. Undo Block
- **Purpose**: Remove a previously applied block on a user
- **Effect**:
  - Marks the block activity as undone in the database
  - Allows interaction with the previously blocked user

## Implementation Details

### File Structure

```
src/lib/activitypub/
├── activities.ts          # Main activity processing logic
├── reactions.ts           # New file for Like/Announce processing
├── follows.ts            # Follow/Unfollow processing
├── blocks.ts             # Block/Unblock processing
└── tests/
    ├── activities.test.ts
    ├── reactions.test.ts
    └── activitypub-undo-helpers.ts
```

### Database Schema

The implementation uses the existing database schema with the following key tables:

#### `reaction` table
```sql
- id: UUID (primary key)
- userId: UUID (references user.id)
- userUri: VARCHAR (actor URI)
- postId: UUID (references post.id)
- postUri: VARCHAR (post URI)
- type: VARCHAR ('like' | 'announce' | 'dislike')
- activityUri: VARCHAR (original activity URI)
- undoActivityUri: VARCHAR (undo activity URI, nullable)
- isActive: BOOLEAN (false when undone)
- createdAt: TIMESTAMP
- undoneAt: TIMESTAMP (nullable)
```

#### `follow` table
```sql
- followerUri: VARCHAR
- followingUri: VARCHAR
- status: VARCHAR ('pending' | 'accepted' | 'rejected')
```

#### `activity` table
```sql
- type: VARCHAR
- status: VARCHAR ('completed' | 'undone')
- actorUri: VARCHAR
- objectUri: VARCHAR
```

### Core Functions

#### `processUndoActivity(activity: Undo)`
Main function that processes incoming Undo activities:

1. Validates the activity structure
2. Extracts the actor and target activity
3. Fetches the target activity if it's a URL reference
4. Dispatches to specific undo handlers based on activity type

#### Reaction Functions (`reactions.ts`)
- `processLike(activity: Like)` - Creates like reactions
- `processAnnounce(activity: Announce)` - Creates announce reactions
- `removeLike(actorUri: string, objectUri: string)` - Removes like reactions
- `removeAnnounce(actorUri: string, objectUri: string)` - Removes announce reactions

#### Follow Functions (`follows.ts`)
- `deleteFollow(followerUri: string, followingUri: string)` - Removes follow relationships

#### Block Functions (`blocks.ts`)
- `removeBlock(blockerUri: string, blockedUri: string)` - Marks blocks as undone

### Activity Processing Flow

```mermaid
graph TD
    A[Receive Undo Activity] --> B[Validate Structure]
    B --> C[Extract Actor & Object]
    C --> D{Object Type?}
    D -->|URL| E[Fetch Activity]
    D -->|Embedded| F[Use Direct Object]
    E --> G[Determine Activity Type]
    F --> G
    G --> H{Activity Type?}
    H -->|Like| I[processUndoLike]
    H -->|Announce| J[processUndoAnnounce]
    H -->|Follow| K[processUndoFollow]
    H -->|Block| L[processUndoBlock]
    H -->|Other| M[Log Warning]
    I --> N[Update Database]
    J --> N
    K --> N
    L --> N
    N --> O[Return Success]
```

### Error Handling

The implementation includes comprehensive error handling:

1. **Validation Errors**: Missing required fields (actor, object)
2. **Fetch Errors**: Failed to retrieve referenced activities
3. **Database Errors**: Failed database operations
4. **Not Found Errors**: Target activities/relationships don't exist
5. **Type Errors**: Unsupported activity types for undo

### Testing

Comprehensive test suite includes:

- Unit tests for each undo type
- Integration tests for the main processing flow
- Error handling tests
- Mock implementations for external dependencies

Test files:
- `activities.test.ts` - Main activity processing tests
- `reactions.test.ts` - Reaction-specific tests
- `activitypub-undo-helpers.ts` - Test helper functions

## Usage Examples

### Undo Like Activity
```json
{
  "@context": "https://www.w3.org/ns/activitystreams",
  "type": "Undo",
  "id": "https://example.com/activities/undo-like-123",
  "actor": "https://example.com/users/alice",
  "object": {
    "type": "Like",
    "id": "https://example.com/activities/like-456",
    "actor": "https://example.com/users/alice",
    "object": "https://example.com/posts/789"
  }
}
```

### Undo Follow Activity
```json
{
  "@context": "https://www.w3.org/ns/activitystreams",
  "type": "Undo",
  "id": "https://example.com/activities/undo-follow-123",
  "actor": "https://example.com/users/alice",
  "object": "https://example.com/activities/follow-456"
}
```

## Security Considerations

1. **Actor Verification**: Only the original actor can undo their own activities
2. **Activity Validation**: Verify that the activity being undone actually exists
3. **Authorization**: Ensure the actor has permission to perform the undo
4. **Idempotency**: Handle duplicate undo requests gracefully

## Performance Considerations

1. **Database Indexing**: Ensure proper indexes on frequently queried fields
2. **Soft Deletes**: Use `isActive` flags instead of hard deletes for audit trails
3. **Batch Operations**: Process multiple undos efficiently when possible
4. **Caching**: Cache frequently accessed activity data

## Future Enhancements

1. **Undo Create**: Support for undoing Create activities (using Delete instead)
2. **Undo Add/Remove**: Support for undoing collection modifications
3. **Audit Logging**: Enhanced logging for undo operations
4. **Rate Limiting**: Prevent abuse of undo operations
5. **Notification System**: Notify affected users of undo operations

## References

- [ActivityPub Specification](https://www.w3.org/TR/activitypub/)
- [Mastodon ActivityPub Documentation](https://docs.joinmastodon.org/spec/activitypub/)
- [ActivityStreams Vocabulary](https://www.w3.org/TR/activitystreams-vocabulary/)
