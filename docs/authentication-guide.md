# 🔐 Comprehensive Authentication Guide для FireShark

Это подробное руководство по использованию системы аутентификации во всех частях приложения FireShark.

## 📋 Содержание

1. [Обзор системы](#обзор-системы)
2. [Регистрация пользователей](#регистрация-пользователей)
3. [Проверка авторизации на сервере](#проверка-авторизации-на-сервере)
4. [Проверка авторизации на клиенте](#проверка-авторизации-на-клиенте)
5. [Примеры использования](#примеры-использования)
6. [Безопасность](#безопасность)

## 🏗️ Обзор системы

Система аутентификации FireShark поддерживает:
- **Локальных пользователей** с паролями (для администраторов и модераторов)
- **ActivityPub пользователей** без паролей (федеративные пользователи)
- **Сессии на основе cookies** с автоматическим истечением
- **Защиту маршрутов** на сервере и клиенте

### Основные компоненты:
- `src/lib/server/auth.ts` - основные функции аутентификации
- `src/lib/server/session.ts` - управление сессиями
- `src/lib/server/auth-guards.ts` - защита маршрутов на сервере
- `src/lib/stores/auth.ts` - состояние авторизации на клиенте

## 👤 Регистрация пользователей

### CLI скрипт
```bash
pnpm register-user
```

Скрипт запросит:
- **Username** (только латиница, цифры, подчёркивания, дефисы)
- **Show Name** (отображаемое имя)
- **Пароль** (минимум 8 символов)

### Программная регистрация
```typescript
import { createLocalUser } from '$lib/server/auth';

const user = await createLocalUser('admin', 'Администратор', 'securepassword123');
```

## 🛡️ Проверка авторизации на сервере

### В +page.server.ts и +layout.server.ts

#### Обязательная авторизация
```typescript
import { requireAuth } from '$lib/server/auth-guards';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ cookies }) => {
  const user = await requireAuth(cookies); // Перенаправит на /auth если не авторизован
  
  return {
    user
  };
};
```

#### Опциональная авторизация
```typescript
import { getOptionalUser } from '$lib/server/auth-guards';

export const load: PageServerLoad = async ({ cookies }) => {
  const user = await getOptionalUser(cookies); // null если не авторизован
  
  return {
    user,
    isAuthenticated: !!user
  };
};
```

#### Проверка доступа к ресурсу
```typescript
import { requireUserAccess } from '$lib/server/auth-guards';

export const load: PageServerLoad = async ({ cookies, params }) => {
  const user = await requireUserAccess(cookies, params.userId); // 403 если не владелец
  
  return { user };
};
```

### В API endpoints (+server.ts)

#### Защищённый API endpoint
```typescript
import { requireAuthAPI } from '$lib/server/auth-guards';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ cookies, request }) => {
  const user = await requireAuthAPI(cookies); // 401 если не авторизован
  
  // Логика API
  return new Response(JSON.stringify({ success: true }));
};
```

#### Использование хелпера для защищённых endpoints
```typescript
import { createProtectedEndpoint } from '$lib/server/auth-guards';

export const { GET, POST, PUT, DELETE } = createProtectedEndpoint({
  GET: async ({ cookies, url }) => {
    // Пользователь уже проверен, можно использовать
    return new Response('Protected data');
  },
  
  POST: async ({ cookies, request }) => {
    // Создание ресурса
    return new Response('Created');
  }
});
```

#### API с опциональной авторизацией
```typescript
import { createOptionalAuthEndpoint } from '$lib/server/auth-guards';

export const { GET } = createOptionalAuthEndpoint({
  GET: async ({ user, url }) => {
    if (user) {
      // Авторизованный пользователь
      return new Response(`Hello, ${user.showName}`);
    } else {
      // Гость
      return new Response('Hello, guest');
    }
  }
});
```

### Универсальный middleware
```typescript
import { withAuth, withOptionalAuth } from '$lib/server/auth-guards';

// Обязательная авторизация
export const POST: RequestHandler = async (event) => {
  return withAuth(event, async (user) => {
    // Логика с гарантированно авторизованным пользователем
    return new Response(`Hello, ${user.showName}`);
  }, { apiMode: true }); // apiMode: true для API (401), false для страниц (redirect)
};

// Опциональная авторизация
export const GET: RequestHandler = async (event) => {
  return withOptionalAuth(event, async (user) => {
    if (user) {
      return new Response(`Hello, ${user.showName}`);
    }
    return new Response('Hello, guest');
  });
};
```

## 🎨 Проверка авторизации на клиенте

### В Svelte компонентах

#### Использование store
```svelte
<script lang="ts">
  import { currentUser, logout } from '$lib/stores/auth';
</script>

{#if $currentUser}
  <div>
    Добро пожаловать, {$currentUser.showName}!
    <button onclick={logout}>Выйти</button>
  </div>
{:else}
  <a href="/auth">Войти</a>
{/if}
```

#### Условный рендеринг
```svelte
<script lang="ts">
  import { isAuthenticated, withAuth, isOwner } from '$lib/stores/auth';
</script>

{#if isAuthenticated()}
  <p>Вы авторизованы</p>
{/if}

{#if withAuth(user => user.username === 'admin')}
  <button>Админ панель</button>
{/if}

{#if isOwner(postUserId)}
  <button>Редактировать пост</button>
{/if}
```

### В +page.ts (клиентская навигация)
```typescript
import { redirect } from '@sveltejs/kit';
import { browser } from '$app/environment';
import { getUser } from '$lib/stores/auth';
import type { PageLoad } from './$types';

export const load: PageLoad = async ({ parent }) => {
  const { user } = await parent();
  
  // Проверка на клиенте (после гидратации)
  if (browser && !user) {
    throw redirect(302, '/auth');
  }
  
  return {};
};
```

## 📚 Примеры использования

### Защищённая страница профиля
```typescript
// src/routes/profile/+page.server.ts
import { requireAuth } from '$lib/server/auth-guards';

export const load: PageServerLoad = async ({ cookies }) => {
  const user = await requireAuth(cookies);
  return { user };
};
```

### API для создания поста
```typescript
// src/routes/api/posts/+server.ts
import { requireAuthAPI } from '$lib/server/auth-guards';

export const POST: RequestHandler = async ({ cookies, request }) => {
  const user = await requireAuthAPI(cookies);
  
  const { content } = await request.json();
  
  // Создание поста от имени пользователя
  const post = await createPost(user.id, content);
  
  return new Response(JSON.stringify(post));
};
```

### Компонент навигации
```svelte
<!-- src/lib/components/Navigation.svelte -->
<script lang="ts">
  import { currentUser, logout } from '$lib/stores/auth';
</script>

<nav>
  <a href="/">Главная</a>
  
  {#if $currentUser}
    <a href="/profile">Профиль</a>
    <a href="/settings">Настройки</a>
    <button onclick={logout}>Выйти</button>
  {:else}
    <a href="/auth">Войти</a>
  {/if}
</nav>
```

### Защищённый layout для админки
```typescript
// src/routes/admin/+layout.server.ts
import { requireAuth } from '$lib/server/auth-guards';
import { error } from '@sveltejs/kit';

export const load: LayoutServerLoad = async ({ cookies }) => {
  const user = await requireAuth(cookies);
  
  // Дополнительная проверка прав администратора
  if (user.username !== 'admin') {
    throw error(403, 'Доступ запрещён');
  }
  
  return { user };
};
```

### В методах вне фреймворка

Для использования в обычных TypeScript/JavaScript функциях:

```typescript
import { getCurrentUser } from '$lib/server/session';
import type { Cookies } from '@sveltejs/kit';

// Функция, которая требует авторизации
export async function someBusinessLogic(cookies: Cookies) {
  const user = await getCurrentUser(cookies);

  if (!user) {
    throw new Error('Требуется авторизация');
  }

  // Логика с авторизованным пользователем
  return processUserData(user);
}

// Функция с опциональной авторизацией
export async function optionalAuthLogic(cookies: Cookies) {
  const user = await getCurrentUser(cookies);

  if (user) {
    return getPersonalizedData(user);
  } else {
    return getPublicData();
  }
}
```

## 🔒 Безопасность

### Рекомендации:
1. **Всегда проверяйте авторизацию на сервере** - клиентские проверки легко обойти
2. **Используйте HTTPS в продакшене** - cookies передаются только по защищённому соединению
3. **Регулярно очищайте истёкшие сессии** - автоматически происходит каждый час
4. **Валидируйте входные данные** - используйте функции из `auth.ts`
5. **Логируйте попытки входа** - для мониторинга безопасности

### Настройки безопасности:
```typescript
// В src/lib/server/session.ts можно настроить:
const SESSION_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 дней
const SALT_ROUNDS = 12; // Сложность хеширования bcrypt
```

### Переменные окружения:
```env
# В .env для продакшена
NODE_ENV=production
DATABASE_URL=postgresql://...
```

## 🚀 Быстрый старт

1. **Создайте первого пользователя:**
   ```bash
   pnpm register-user
   ```

2. **Защитите страницу:**
   ```typescript
   // +page.server.ts
   import { requireAuth } from '$lib/server/auth-guards';
   export const load = async ({ cookies }) => {
     const user = await requireAuth(cookies);
     return { user };
   };
   ```

3. **Добавьте навигацию:**
   ```svelte
   <script>
     import { currentUser } from '$lib/stores/auth';
   </script>
   
   {#if $currentUser}
     Привет, {$currentUser.showName}!
   {/if}
   ```

Готово! Ваша система аутентификации настроена и готова к использованию.
