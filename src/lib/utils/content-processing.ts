import { extractHashtags, extractMentions, transformHtmlLinks, replaceEmojiWithLucideIcons, type HashtagMatch } from '$lib/activitypub/utils/content';
// Импортируем компонент для регистрации веб-компонента
import '$lib/components/design_system/Hashtag.svelte';
import '$lib/components/design_system/Mention.svelte';
import '$lib/components/design_system/LucideIcon.svelte';

export function createContentWithHashtags(content: string): string {
  const hashtags = extractHashtags(content);
  
  if (hashtags.length === 0) {
    return content;
  }

  let processedContent = content;
  
  // Сортируем хештеги по убыванию позиции, чтобы не сбить индексы
  const sortedHashtags = [...hashtags].sort((a, b) => b.startIndex - a.startIndex);
  
  sortedHashtags.forEach(hashtag => {
    const webComponent = `<epos-hashtag tag="${hashtag.tag}"></epos-hashtag>`;
    
    processedContent = processedContent.slice(0, hashtag.startIndex) + 
                     webComponent + 
                     processedContent.slice(hashtag.endIndex);
  });

  return processedContent;
}

export function createContentWithMentions(content: string, domain: string | null = null): string {
  const mentions = extractMentions(content);

  if (mentions.length === 0) {
    return content;
  }

  let processedContent = content;

  // Сортируем упоминания по убыванию позиции, чтобы не сбить индексы
  const sortedMentions = [...mentions].sort((a, b) => b.startIndex - a.startIndex);

  sortedMentions.forEach(mention => {
    const domainStr = mention.domain ? `domain="${mention.domain}"` : domain ? `domain="${domain}"` : '';
    const webComponent = `<epos-mention username="${mention.username}" ${domainStr}></epos-mention>`;

    processedContent = processedContent.slice(0, mention.startIndex) + 
                     webComponent + 
                     processedContent.slice(mention.endIndex);
  });

  return processedContent;
}

export function processContentForClient(content: string, domain: string | null = null): string {
  let processedContent = transformHtmlLinks(content);
  processedContent = replaceEmojiWithLucideIcons(processedContent);
  processedContent = createContentWithHashtags(processedContent);
  processedContent = createContentWithMentions(processedContent, domain);
  return processedContent;
}

export function stealImage(src: string): string {
  return `/api/steal-image?src=${encodeURIComponent(src)}`;
}

