import { redirect, error } from '@sveltejs/kit';
import type { Cookies, RequestEvent } from '@sveltejs/kit';
import { getCurrentUser } from './session';

/**
 * Проверка авторизации с перенаправлением на страницу входа
 * Используется в +page.server.ts и +layout.server.ts
 */
export async function requireAuth(cookies: Cookies, redirectTo: string = '/auth') {
  const user = await getCurrentUser(cookies);
  
  if (!user) {
    throw redirect(302, redirectTo);
  }
  
  return user;
}

/**
 * Проверка авторизации с возвратом ошибки 401
 * Используется в API endpoints (+server.ts)
 */
export async function requireAuthAPI(cookies: Cookies) {
  const user = await getCurrentUser(cookies);
  
  if (!user) {
    throw error(401, { message: 'Требуется авторизация' });
  }
  
  return user;
}

/**
 * Проверка авторизации с возвратом ошибки 403 для конкретного пользователя
 * Используется когда нужно проверить, что текущий пользователь имеет доступ к ресурсу
 */
export async function requireUserAccess(cookies: Cookies, requiredUserId: string) {
  const user = await getCurrentUser(cookies);
  
  if (!user) {
    throw error(401, { message: 'Требуется авторизация' });
  }
  
  if (user.id !== requiredUserId) {
    throw error(403, { message: 'Доступ запрещён' });
  }
  
  return user;
}

/**
 * Получение пользователя без обязательной авторизации
 * Используется когда авторизация опциональна
 */
export async function getOptionalUser(cookies: Cookies) {
  try {
    return await getCurrentUser(cookies);
  } catch {
    return null;
  }
}

/**
 * Middleware для проверки авторизации в RequestEvent
 * Универсальная функция для любых контекстов SvelteKit
 */
export async function withAuth<T>(
  event: RequestEvent,
  handler: (user: NonNullable<Awaited<ReturnType<typeof getCurrentUser>>>) => T | Promise<T>,
  options: {
    redirectTo?: string;
    apiMode?: boolean;
  } = {}
): Promise<T> {
  const { redirectTo = '/auth', apiMode = false } = options;
  
  const user = await getCurrentUser(event.cookies);
  
  if (!user) {
    if (apiMode) {
      throw error(401, { message: 'Требуется авторизация' });
    } else {
      throw redirect(302, redirectTo);
    }
  }
  
  return handler(user);
}

/**
 * Middleware для опциональной авторизации в RequestEvent
 */
export async function withOptionalAuth<T>(
  event: RequestEvent,
  handler: (user: Awaited<ReturnType<typeof getCurrentUser>>) => T | Promise<T>
): Promise<T> {
  const user = await getCurrentUser(event.cookies);
  return handler(user);
}

/**
 * Проверка авторизации для защищённых маршрутов
 * Используется в +layout.server.ts для защиты целых разделов
 */
export async function protectRoute(cookies: Cookies, redirectTo: string = '/auth') {
  return requireAuth(cookies, redirectTo);
}

/**
 * Проверка, что пользователь НЕ авторизован (для страниц входа/регистрации)
 */
export async function requireGuest(cookies: Cookies, redirectTo: string = '/') {
  const user = await getCurrentUser(cookies);
  
  if (user) {
    throw redirect(302, redirectTo);
  }
}

/**
 * Хелпер для создания защищённых API endpoints
 */
export function createProtectedEndpoint<T extends Record<string, any>>(
  handlers: T
): T {
  const protectedHandlers = {} as T;
  
  for (const [method, handler] of Object.entries(handlers)) {
    protectedHandlers[method as keyof T] = (async (event: RequestEvent) => {
      await requireAuthAPI(event.cookies);
      return handler(event);
    }) as T[keyof T];
  }
  
  return protectedHandlers;
}

/**
 * Хелпер для создания API endpoints с опциональной авторизацией
 */
export function createOptionalAuthEndpoint<T extends Record<string, any>>(
  handlers: T
): T {
  const optionalAuthHandlers = {} as T;
  
  for (const [method, handler] of Object.entries(handlers)) {
    optionalAuthHandlers[method as keyof T] = (async (event: RequestEvent) => {
      const user = await getOptionalUser(event.cookies);
      return handler({ ...event, user });
    }) as T[keyof T];
  }
  
  return optionalAuthHandlers;
}
