import bcrypt from 'bcrypt';
import { db } from './db';
import { user } from './db/schema';
import { eq, and, isNull } from 'drizzle-orm';
import { PUBLIC_DOMAIN } from '$env/static/public';

const SALT_ROUNDS = 12;

/**
 * Валидация username - только латиница, цифры, подчёркивания и дефисы
 */
export function validateUsername(username: string): { valid: boolean; error?: string } {
  if (!username) {
    return { valid: false, error: 'Username не может быть пустым' };
  }

  if (username.length < 3) {
    return { valid: false, error: 'Username должен содержать минимум 3 символа' };
  }

  if (username.length > 30) {
    return { valid: false, error: 'Username не может быть длиннее 30 символов' };
  }

  // Только латиница, цифры, подчёркивания и дефисы
  const usernameRegex = /^[a-zA-Z0-9_-]+$/;
  if (!usernameRegex.test(username)) {
    return { valid: false, error: 'Username может содержать только латинские буквы, цифры, подчёркивания и дефисы' };
  }

  return { valid: true };
}

/**
 * Валидация пароля
 */
export function validatePassword(password: string): { valid: boolean; error?: string } {
  if (!password) {
    return { valid: false, error: 'Пароль не может быть пустым' };
  }

  if (password.length < 8) {
    return { valid: false, error: 'Пароль должен содержать минимум 8 символов' };
  }

  if (password.length > 128) {
    return { valid: false, error: 'Пароль не может быть длиннее 128 символов' };
  }

  return { valid: true };
}

/**
 * Хеширование пароля
 */
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, SALT_ROUNDS);
}

/**
 * Проверка пароля
 */
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

/**
 * Проверка существования пользователя по username
 */
export async function checkUserExists(username: string): Promise<boolean> {
  const existingUser = await db.select()
    .from(user)
    .where(and(
      eq(user.username, username),
      isNull(user.domain) // только локальные пользователи
    ))
    .limit(1);

  return existingUser.length > 0;
}

/**
 * Создание нового локального пользователя
 */
export async function createLocalUser(username: string, showName: string, password: string) {
  // Валидация
  const usernameValidation = validateUsername(username);
  if (!usernameValidation.valid) {
    throw new Error(usernameValidation.error);
  }

  const passwordValidation = validatePassword(password);
  if (!passwordValidation.valid) {
    throw new Error(passwordValidation.error);
  }

  if (!showName || showName.trim().length === 0) {
    throw new Error('Show name не может быть пустым');
  }

  if (showName.length > 100) {
    throw new Error('Show name не может быть длиннее 100 символов');
  }

  // Проверка существования пользователя
  if (await checkUserExists(username)) {
    throw new Error(`Пользователь с username "${username}" уже существует`);
  }

  // Хеширование пароля
  const hashedPassword = await hashPassword(password);

  // Создание пользователя
  const newUser = await db.insert(user).values({
    username,
    domain: null, // локальный пользователь
    showName: showName.trim(),
    password: hashedPassword,
    // Для локальных пользователей эти поля не обязательны
    uri: null,
    url: null,
    inbox: null,
    outbox: null,
  }).returning();

  return newUser[0];
}

/**
 * Аутентификация пользователя
 */
export async function authenticateUser(username: string, password: string) {
  const foundUser = await db.select()
    .from(user)
    .where(and(
      eq(user.username, username),
      isNull(user.domain) // только локальные пользователи
    ))
    .limit(1);

  if (foundUser.length === 0) {
    return null; // пользователь не найден
  }

  const userData = foundUser[0];
  
  if (!userData.password) {
    return null; // у пользователя нет пароля (возможно, это ActivityPub пользователь)
  }

  const isPasswordValid = await verifyPassword(password, userData.password);
  if (!isPasswordValid) {
    return null; // неверный пароль
  }

  // Возвращаем пользователя без пароля
  const { password: _, ...userWithoutPassword } = userData;
  return userWithoutPassword;
}

/**
 * Получение пользователя по ID
 */
export async function getUserById(userId: string) {
  const foundUser = await db.select()
    .from(user)
    .where(and(
      eq(user.id, userId),
      isNull(user.domain) // только локальные пользователи
    ))
    .limit(1);

  if (foundUser.length === 0) {
    return null;
  }

  const userData = foundUser[0];

  // Дополнительная проверка, что у пользователя есть пароль (локальный пользователь)
  if (!userData.password) {
    return null;
  }

  const { password: _, ...userWithoutPassword } = userData;
  return userWithoutPassword;
}
