import { dev } from '$app/environment';
import type { Cookies } from '@sveltejs/kit';
import { getUserById } from './auth';

const SESSION_COOKIE_NAME = 'fireshark_session';
const SESSION_DURATION = 30 * 24 * 60 * 60 * 1000; // 30 дней в миллисекундах

// Простое хранилище сессий в памяти (в продакшене лучше использовать Redis или базу данных)
const sessions = new Map<string, { userId: string; createdAt: number; lastAccess: number }>();

/**
 * Генерация случайного ID сессии
 */
function generateSessionId(): string {
  return crypto.randomUUID();
}

/**
 * Создание новой сессии
 */
export function createSession(userId: string): string {
  const sessionId = generateSessionId();
  const now = Date.now();
  
  sessions.set(sessionId, {
    userId,
    createdAt: now,
    lastAccess: now
  });

  return sessionId;
}

/**
 * Получение сессии по ID
 */
export function getSession(sessionId: string) {
  const session = sessions.get(sessionId);
  
  if (!session) {
    return null;
  }

  // Проверка истечения сессии
  if (Date.now() - session.createdAt > SESSION_DURATION) {
    sessions.delete(sessionId);
    return null;
  }

  // Обновление времени последнего доступа
  session.lastAccess = Date.now();
  
  return session;
}

/**
 * Удаление сессии
 */
export function deleteSession(sessionId: string): void {
  sessions.delete(sessionId);
}

/**
 * Очистка истёкших сессий
 */
export function cleanupExpiredSessions(): void {
  const now = Date.now();
  
  for (const [sessionId, session] of sessions.entries()) {
    if (now - session.createdAt > SESSION_DURATION) {
      sessions.delete(sessionId);
    }
  }
}

/**
 * Установка cookie сессии
 */
export function setSessionCookie(cookies: Cookies, sessionId: string): void {
  cookies.set(SESSION_COOKIE_NAME, sessionId, {
    path: '/',
    httpOnly: true,
    secure: !dev,
    sameSite: 'lax',
    maxAge: SESSION_DURATION / 1000 // в секундах
  });
}

/**
 * Удаление cookie сессии
 */
export function clearSessionCookie(cookies: Cookies): void {
  cookies.delete(SESSION_COOKIE_NAME, {
    path: '/'
  });
}

/**
 * Получение ID сессии из cookies
 */
export function getSessionIdFromCookies(cookies: Cookies): string | null {
  return cookies.get(SESSION_COOKIE_NAME) || null;
}

/**
 * Получение текущего пользователя по cookies
 */
export async function getCurrentUser(cookies: Cookies) {
  const sessionId = getSessionIdFromCookies(cookies);
  
  if (!sessionId) {
    return null;
  }

  const session = getSession(sessionId);
  
  if (!session) {
    // Удаляем недействительную cookie
    clearSessionCookie(cookies);
    return null;
  }

  try {
    const user = await getUserById(session.userId);
    return user;
  } catch (error) {
    // В случае ошибки удаляем сессию
    deleteSession(sessionId);
    clearSessionCookie(cookies);
    return null;
  }
}

/**
 * Создание сессии для пользователя и установка cookie
 */
export function loginUser(cookies: Cookies, userId: string): string {
  const sessionId = createSession(userId);
  setSessionCookie(cookies, sessionId);
  return sessionId;
}

/**
 * Выход пользователя из системы
 */
export function logoutUser(cookies: Cookies): void {
  const sessionId = getSessionIdFromCookies(cookies);
  
  if (sessionId) {
    deleteSession(sessionId);
  }
  
  clearSessionCookie(cookies);
}

// Периодическая очистка истёкших сессий (каждый час)
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupExpiredSessions, 60 * 60 * 1000);
}
