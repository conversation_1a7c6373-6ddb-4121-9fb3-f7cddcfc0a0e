<script>
  let { variant, children, ...props } = $props();
</script>

<button class={`button ${variant}`} {...props}>
  {@render children()}
</button>

<style>
  @property --color-stop-1 {
    syntax: '<percentage>';
    inherits: false;
    initial-value: 0%;
  }

  .button {
    --icon-size: 1em;
    display: flex;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 16px;
    cursor: pointer;
    transition:
      background-color 0.2s ease,
      outline 0.5s ease;
    outline-offset: 2px;
    outline: 3px solid transparent;
    align-items: center;
  }

  .button :global(svg) {
    width: var(--icon-size);
    height: var(--icon-size);
  }

  .button:hover {
    outline: 3px solid oklch(from var(--accent-color) l c h / 0.5);
  }

  .button:focus-visible {
    outline: 3px solid var(--accent-color);
  }

  .button.inline {
    display: inline-flex
  }

  .button.icon {
    --button-size: 40px;
    padding: 0;
    width: var(--button-size);
    height: var(--button-size);
    justify-content: center;
  }

  .button.primary {
    background-image: linear-gradient(
      to bottom right in oklch,
      var(--accent-color-2) 0%,
      var(--accent-color) var(--color-stop-1),
      var(--accent-color-2) 100%
    );
    color: var(--text-color--inverse);
    transition:
      --color-stop-1 1s ease-in-out,
      outline 0.5s ease;
  }

  .button.primary:hover {
    --color-stop-1: 100%;
  }

  .button.rounded {
    border-radius: 999px;
  }

  .button.outline {
    background-color: transparent;
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
  }

  .button.invisible {
    background-color: transparent;
    color: var(--text-color);
    border: none;
    border-radius: 4px;
  }
</style>
