<svelte:options customElement={{
  tag: 'epos-hashtag',
  shadow: "none",
  props: {
    tag: { type: 'String', reflect: true }
  }
}}/>

<script lang="ts">
import { Hash } from 'lucide-svelte';

  let {
    tag
  }: {
    tag: string;
  } = $props();
</script>

<style>
  .hashtag {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    color: var(--text-color);
    text-decoration: none;
    background-color: oklch(from var(--accent-color) l c h / 0.2);
    border-radius: 8px;
    padding: 0 4px;
    transition: background-color 0.3s ease;
  }

  .hashtag :global(svg) {
    width: 1em;
    height: 1em;
  }

  .hashtag:hover {
    background-color: oklch(from var(--accent-color) l c h / 0.4);
  }
</style>

<a href="/tags/{tag.toLowerCase()}" class="hashtag">
  <Hash/>{tag}
</a>