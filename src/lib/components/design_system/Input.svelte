<script lang="ts">
    let { ...props } = $props();
</script>

<style>
    .input {
        border: none;
        border-radius: 8px;
        background-color: var(--page-bg) !important;
        padding: 8px;
        font-size: 16px;
        color: var(--text-color);
        outline: 3px solid transparent;
        outline-offset: 2px;
        transition: outline 0.5s ease;
    }

    .input:autofill {
        -webkit-text-fill-color: var(--text-color);
        background-color: var(--page-bg) !important;
        border: 1px solid var(--accent-color);
        --webkit-box-shadow: 0 0 0 1000px var(--page-bg) inset;
        font-family: "Noto Sans", sans-serif !important;
        transition: background-color 5000s ease-in-out 0s;
        font-size: 16px;
    }

    .input:hover {
        outline: 3px solid oklch(from var(--accent-color) l c h / 0.5);
    }

    .input::placeholder{
        color: var(--text-min-vis-color);
    }

    .input:focus-visible {
        outline: 3px solid var(--accent-color);
    }
</style>

<input
    class="input"
    {...props}
>
