<script lang="ts">
  import { Ellipsis, type Icon as IconType } from 'lucide-svelte';
  import Button from './Button.svelte';

  export type DropdownAction = { label: string; icon: typeof IconType; action: () => void } | 'divider';

  let {
    actions
  }: {
    actions: Array<DropdownAction>;
  } = $props();

  let dropdown: HTMLDivElement;
  let button: Button;
  let isOpen = false;
  let dividerCount = 0;

  function toggleDropdown() {
    if (!isOpen) {
      openDropdown();
    }
  }

  function popoverOutsideClickHandler(event: MouseEvent) {
    if (dropdown && !dropdown.contains(event.target as Node)) {
      console.log('Clicked outside');
      closeDropdown();
    }
  }

  function openDropdown() {
    isOpen = true;
    dropdown.showPopover();
    setTimeout(() => {
      document.addEventListener('click', popoverOutsideClickHandler);
      console.log('Listener added');
    }, 100);
  }

  function closeDropdown() {
    isOpen = false;
    dropdown.hidePopover();
    document.removeEventListener('click', popoverOutsideClickHandler);
  }

  function generateKey(action: DropdownAction) {
    if (action === 'divider') {
      dividerCount++;
      return 'divider_' + dividerCount;
    }
    return action.label;
  }

  function doAction(action: () => void): () => void {
    return () => {
      closeDropdown();
      action();
    };
  }
</script>

<Button variant="icon invisible" bind:this={button} type="button" onclick={toggleDropdown} style="--button-size: 20px">
  <Ellipsis />
</Button>

<div class="dropdown" bind:this={dropdown} popover="manual">
  {#each actions as action (generateKey(action))}
    {#if action === 'divider'}
      <hr class="divider" />
    {:else}
      {@const Icon = action.icon}
      <button type="button" onclick={doAction(action.action)}>
        <Icon />
        <span class="label">{action.label}</span>
      </button>
    {/if}
  {/each}
</div>

<style>
</style>
