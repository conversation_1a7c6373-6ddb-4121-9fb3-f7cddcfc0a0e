<script lang="ts">
  let {
    username,
    domain
  }: {
    username: string;
    domain?: string | null;
  } = $props();
</script>

<span class="user-handle">
  <span class="username">@{username}</span>{#if domain}<span class="domain">@{domain}</span>{/if}
</span>

<style>
  .user-handle {
    color: var(--text-min-vis-color);
  }

  .user-handle .username {
    color: var(--text-color);
    font-weight: 700;
  }

  .user-handle .domain {
    font-size: 0.9rem;
  }
</style>
