<script lang="ts">
  import type { HTMLAttributes } from 'svelte/elements';
  import { stealImage } from '$lib/utils/content-processing';

  let {
    user,
    needsLink = false,
    ...props
  }: {
    user: {
      username: string;
      domain: string | null;
      avatar: string | null;
      isCat?: boolean;
      earColor?: string
    },
    needsLink?: boolean;
  } & HTMLAttributes<HTMLAnchorElement> = $props();

  let src = $derived(user.avatar);
  let catEarColor = $state(user.earColor);
  let url = $derived.by(() => {
    let url = `/users/${user.username}`;
    if (user.domain) {
      url += `@${user.domain}`;
    }
    return url;
  });

  $effect.pre(() => {
    if (catEarColor === '#AAA' && user.isCat && src) {
      getCatEarColor(src)
        .then((result) => catEarColor = result)
    }
  })

  async function getCatEarColor(src: string) {
    try {
      if (typeof window !== 'undefined') {
        const FastAverageColor = (await import('fast-average-color')).FastAverageColor;
        const fac = new FastAverageColor()
        const color = await fac.getColorAsync(src)
        return color.hex
      }
      const getAverageColor = (await import('fast-average-color-node')).getAverageColor;
      const color = await getAverageColor(src);
      return color.hex;
    } catch {
      console.log('Failed to get average color, stealing image')
      return getCatEarColor(stealImage(src))
    }
  }

  function showAvatarLitebox() {
    // showLitebox(user.avatar)
    console.log('TODO: showLitebox(user.avatar)')
  }
</script>

{#snippet UserAvatar()}
  {#if user.isCat}
    <div class="cat-ears"></div>
  {/if}
  <img src={src} alt="User avatar" />
  {#if user.isCat}
    <div class="cat-whiskers">
      <div class="cat-whiskers__left">
        <div class="cat-whiskers__left__top"></div>
        <div class="cat-whiskers__left__middle"></div>
        <div class="cat-whiskers__left__bottom"></div>
      </div>
      <div class="cat-whiskers__right">
        <div class="cat-whiskers__right__top"></div>
        <div class="cat-whiskers__right__middle"></div>
        <div class="cat-whiskers__right__bottom"></div>
      </div>
    </div>
  {/if}
{/snippet}

{#if needsLink}
  <a href={url} class='avatar' style={`--cat-ear-color: ${catEarColor}`}>
    {@render UserAvatar()}
  </a>
{:else}
  <button class='avatar' style={`--cat-ear-color: ${catEarColor}`} onclick={showAvatarLitebox}>
    {@render UserAvatar()}
  </button>
{/if}

<style>
  @property --cat-ear-color {
    syntax: '<color>';
    inherits: true;
    initial-value: #AAA;
  }

  .avatar {
    display: block;
    border-radius: 999px;
    position: relative;
    transition: --cat-ear-color 0.3s linear;
    border: none;
    cursor: pointer;
    background: transparent;
    padding: 0;
  }

  .avatar img {
    position: relative;
    border-radius: 999px;
    overflow: hidden;
    display: block;
    z-index: 1;
    max-width: 100%;
    aspect-ratio: 1;
    background: var(--page-bg);
  }

  .cat-ears {
    --ear-rotation: 20deg;
    position: absolute;
    top: -15px;
    left: 0;
    width: 100%;
  }

  .cat-ears::before,
  .cat-ears::after {
    content: '';
    position: absolute;
    top: 0;
    width: 40px;
    height: 50px;
    background-color: var(--cat-ear-color);
    border: 3px solid oklch(from var(--cat-ear-color) calc(l - 0.2) c h);
    transition: rotate 0.3s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  }

  .cat-ears::before {
    left: 0;
    border-top-left-radius: 30%;
    border-top-right-radius: 100%;
    border-bottom-left-radius: 30px;
    rotate: calc(-1 * var(--ear-rotation));
  }

  .cat-ears::after {
    right: 0;
    border-top-left-radius: 100%;
    border-top-right-radius: 30%;
    border-bottom-right-radius: 30px;
    rotate: var(--ear-rotation);
  }

  .avatar:hover .cat-ears {
    --ear-rotation: 30deg;
  }

  .cat-whiskers {
    --whisker-color: #AAA;
    --whiskers-rotation: -10deg;
    position: absolute;
    top: 50%;
    width: 100%;
    height: 100%;
    z-index: 2;
    pointer-events: none;
  }

  .avatar:hover .cat-whiskers {
    --whiskers-rotation: 0deg;
  }

  .cat-whiskers__left,
  .cat-whiskers__right {
    position: absolute;
    top: 60%;
    transform: translateY(-50%);
    width: 100%;
    height: 100%;
    transition: rotate 0.3s cubic-bezier(0.68, -0.6, 0.32, 1.6);
  }

  .cat-whiskers__left {
    left: -50%;
    rotate: var(--whiskers-rotation);
    transform-origin: 75% -45%;
  }

  .cat-whiskers__right {
    right: -50%;
    rotate: calc(-1 * var(--whiskers-rotation));
    transform-origin: 25% -45%;
  }

  .cat-whiskers__left__top,
  .cat-whiskers__left__middle,
  .cat-whiskers__left__bottom,
  .cat-whiskers__right__top,
  .cat-whiskers__right__middle,
  .cat-whiskers__right__bottom {
    position: absolute;
    background-color: transparent;
    border-top-left-radius: 100%;
    border-top-right-radius: 100%;
    border-top: 3px solid var(--whisker-color);
    width: 50%;
    height: 20%;
    left: 25%;
  }

  .cat-whiskers__left__top,
  .cat-whiskers__left__middle,
  .cat-whiskers__left__bottom {
    transform-origin: bottom right;
  }

  .cat-whiskers__right__top,
  .cat-whiskers__right__middle,
  .cat-whiskers__right__bottom {
    transform-origin: bottom left;
  }

  .cat-whiskers__left__bottom,
  .cat-whiskers__right__top {
    rotate: -20deg;
  }

  .cat-whiskers__left__top,
  .cat-whiskers__right__bottom {
    rotate: 20deg;
  }
</style>
