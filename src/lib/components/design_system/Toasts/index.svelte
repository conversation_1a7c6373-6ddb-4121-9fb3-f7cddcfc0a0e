<script context="module" lang="ts">
  export type ToastType = 'info' | 'success' | 'error' | 'warning';
  export interface ToastMessage {
    id: string,
    type: ToastType,
    message: string
  }
</script>
<script lang="ts">
  import Toast from './Toast.svelte';
  import { toastStore } from './store';
</script>

<style>
  .toasts-container {
    position: fixed;
    top: 16px;
    right: 16px;
    width: 300px;
    z-index: 1000;
  }
</style>

<div class="toasts-container">
  {#each $toastStore.toasts as toast (toast.id)}
    <Toast {toast} />
  {/each}
</div>