<script lang="ts">
  import { slide } from 'svelte/transition'
  import type { ToastMessage } from "./index.svelte";
  import { toastStore } from './store';
    import { onMount } from 'svelte';

  let {
    toast,
  }: {
    toast: ToastMessage
  } = $props();

  function closeToast() {
    $toastStore.removeToast(toast.id);
  }

  onMount(() => {
    setTimeout(closeToast, 3000);
  });
</script>

<style>
  .toast {
    border: none;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    outline-offset: 2px;
    outline: 2px solid transparent;
    transition: outline 0.5s ease;
  }

  .toast:hover {
    outline: 2px solid var(--accent-color);
  }

  .toast.info {
    background-color: var(--layer-1-bg);
    color: var(--text-color);
  }

  .toast.success {
    background-color: var(--accent-color-2);
    color: var(--text-color);
  }

  .toast.error {
    background-color: red;
    color: white;
  }

  .toast.warning {
    background-color: yellow;
    color: black;
  }
</style>

<button type="button" class="toast {toast.type}" onclick={closeToast} transition:slide>
  {toast.message}
</button>