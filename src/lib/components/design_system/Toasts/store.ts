import { writable } from 'svelte/store';
import type { ToastMessage, ToastType } from './index.svelte';

export const toastStore = writable({
  toasts: [] as ToastMessage[],
  showToast: (type: ToastType, message: string) => {
    const id = Math.random().toString(36).substring(2, 9);
    toastStore.update(state => {
      state.toasts.push({ id, type, message });
      return state;
    });
  },
  removeToast: (id: string) => {
    toastStore.update(state => {
      state.toasts = state.toasts.filter(toast => toast.id !== id);
      return state;
    });
  }
});