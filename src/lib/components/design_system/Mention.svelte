<svelte:options customElement={{
  tag: 'epos-mention',
  shadow: "none",
  props: {
    username: { type: 'String', reflect: true },
    domain: { type: 'String', reflect: true }
  }
}}/>

<script lang="ts">
  import UserHandle from "./UserHandle.svelte";

  let {
    username,
    domain
  }: {
    username: string;
    domain?: string;
  } = $props();

  let url = $state(`/users/${username}`);
  if (domain) {
    url += `@${domain}`;
  }
</script>

<style>
  .mention {
    display: inline-flex;
    align-items: center;
    color: var(--text-color);
    text-decoration: none;
    background-color: oklch(from var(--accent-color) l c h / 0.2);
    border-radius: 8px;
    padding: 0 4px;
    transition: background-color 0.3s ease;
  }

  .mention :global(.domain) {
    transition: color 0.3s ease;
  }

  .mention:hover {
    background-color: oklch(from var(--accent-color) l c h / 0.4);
  }

  .mention:hover :global(.domain) {
    color: var(--text-color);
  }
</style>

<a href={url} class="mention">
  <UserHandle {username} {domain} />
</a>