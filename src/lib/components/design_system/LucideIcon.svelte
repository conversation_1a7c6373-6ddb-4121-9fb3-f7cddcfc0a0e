<svelte:options customElement={{
  tag: 'epos-lucide-icon',
  shadow: "none",
  props: {
    icon: { type: 'String', reflect: true },
    title: { type: 'String', reflect: true },
    size: { type: 'String', reflect: true }
  }
}}/>

<script lang="ts">
  // Import all commonly used Lucide icons
  import * as Icons from 'lucide-svelte';

  let {
    icon,
    title = '',
    size = '1em'
  }: {
    icon: string;
    title?: string;
    size?: string;
  } = $props();

  const IconComponent = Icons[icon];
</script>

<style>
  .lucide-icon {
    display: inline-block;
    vertical-align: middle;
  }

  .lucide-icon :global(svg) {
    width: var(--icon-size, 1em);
    height: var(--icon-size, 1em);
  }
</style>

<span class="lucide-icon" style="--icon-size: {size}" {title}>
  <IconComponent />
</span>
