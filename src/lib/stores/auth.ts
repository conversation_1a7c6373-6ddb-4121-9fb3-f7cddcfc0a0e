import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';

export type User = {
  id: string;
  username: string;
  showName: string;
  domain: string | null;
  avatar: string | null;
  banner: string | null;
  summary: string | null;
  isCat: boolean;
};

// Store для текущего пользователя
export const currentUser = writable<User | null>(null);

// Store для состояния загрузки авторизации
export const authLoading = writable<boolean>(false);

/**
 * Инициализация пользователя из данных сервера
 */
export function initializeUser(user: User | null) {
  currentUser.set(user);
}

/**
 * Выход из системы
 */
export async function logout() {
  if (!browser) return;
  
  authLoading.set(true);
  
  try {
    const response = await fetch('/logout', {
      method: 'POST',
      credentials: 'include'
    });
    
    if (response.ok) {
      currentUser.set(null);
      goto('/auth');
    }
  } catch (error) {
    console.error('Ошибка при выходе:', error);
  } finally {
    authLoading.set(false);
  }
}

/**
 * Проверка авторизации пользователя
 * Для использования в компонентах лучше использовать реактивный подход: $derived($currentUser !== null)
 */
export function isAuthenticated(): boolean {
  let user: User | null = null;
  const unsubscribe = currentUser.subscribe(u => user = u);
  unsubscribe(); // Важно отписаться сразу
  return user !== null;
}

/**
 * Получение текущего пользователя
 */
export function getUser(): User | null {
  let user: User | null = null;
  currentUser.subscribe(u => user = u)();
  return user;
}

/**
 * Проверка, является ли пользователь владельцем ресурса
 */
export function isOwner(resourceUserId: string): boolean {
  const user = getUser();
  return user?.id === resourceUserId;
}

/**
 * Хелпер для условного рендеринга авторизованного контента
 */
export function withAuth<T>(callback: (user: User) => T): T | null {
  const user = getUser();
  return user ? callback(user) : null;
}
