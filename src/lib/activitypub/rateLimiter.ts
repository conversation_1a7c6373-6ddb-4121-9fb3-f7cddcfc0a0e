interface QueuedRequest {
  url: string;
  options?: RequestInit;
  resolve: (value: Response) => void;
  priority: number;
  timestamp: number;
}

interface RateLimitState {
  nextAllowedTime: number;
  queue: QueuedRequest[];
  processing: boolean;
  metrics: RateLimitMetrics;
}

interface RateLimitMetrics {
  totalRequests: number;
  queuedRequests: number;
  rateLimitHits: number;
  averageWaitTime: number;
  lastRequestTime: number;
}

interface DomainConfig {
  maxRequestsPerSecond: number;
  burstLimit: number;
  priority: number;
  timeout: number;
}

const rateLimitStates = new Map<string, RateLimitState>();

/**
 * Default domain configurations
 */
const DEFAULT_DOMAIN_CONFIG: DomainConfig = {
  maxRequestsPerSecond: 1,
  burstLimit: 5,
  priority: 1,
  timeout: 30000
};

/**
 * Domain-specific configurations
 */
const domainConfigs = new Map<string, DomainConfig>([
  // High-priority domains (faster rate limits)
  ['mastodon.social', { maxRequestsPerSecond: 2, burstLimit: 10, priority: 3, timeout: 15000 }],
  ['pixelfed.social', { maxRequestsPerSecond: 2, burstLimit: 8, priority: 3, timeout: 15000 }],
  ['lemmy.world', { maxRequestsPerSecond: 1.5, burstLimit: 6, priority: 2, timeout: 20000 }],

  // Medium-priority domains
  ['fosstodon.org', { maxRequestsPerSecond: 1.5, burstLimit: 6, priority: 2, timeout: 20000 }],
  ['mstdn.social', { maxRequestsPerSecond: 1.5, burstLimit: 6, priority: 2, timeout: 20000 }],

  // Conservative defaults for unknown domains
  ['*', DEFAULT_DOMAIN_CONFIG]
]);

/**
 * Request priorities
 */
export enum RequestPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * Global rate limiter metrics
 */
export interface GlobalMetrics {
  totalRequests: number;
  totalRateLimitHits: number;
  averageResponseTime: number;
  activeConnections: number;
  domainsTracked: number;
}

function getHostname(url: string): string {
  try {
    return new URL(url).hostname;
  } catch {
    return 'unknown';
  }
}

/**
 * Get domain configuration with fallback
 */
function getDomainConfig(hostname: string): DomainConfig {
  return domainConfigs.get(hostname) || domainConfigs.get('*') || DEFAULT_DOMAIN_CONFIG;
}

/**
 * Initialize rate limit state for a domain
 */
function initializeRateLimitState(hostname: string): RateLimitState {
  return {
    nextAllowedTime: 0,
    queue: [],
    processing: false,
    metrics: {
      totalRequests: 0,
      queuedRequests: 0,
      rateLimitHits: 0,
      averageWaitTime: 0,
      lastRequestTime: 0
    }
  };
}

/**
 * Update metrics for a domain
 */
function updateMetrics(state: RateLimitState, waitTime: number, wasRateLimited: boolean): void {
  state.metrics.totalRequests++;
  state.metrics.lastRequestTime = Date.now();

  if (wasRateLimited) {
    state.metrics.rateLimitHits++;
  }

  // Update average wait time
  const currentAvg = state.metrics.averageWaitTime;
  state.metrics.averageWaitTime = (currentAvg + waitTime) / 2;
}

/**
 * Log rate limit events
 */
function logRateLimit(hostname: string, event: string, details?: Record<string, any>): void {
  const timestamp = new Date().toISOString();
  console.log(`[RateLimit] ${timestamp} ${hostname}: ${event}`, details || '');
}

function parseRetryAfter(retryAfter: string): number {
  // First check if it looks like an ISO date (contains 'T' or '-')
  if (retryAfter.includes('T') || retryAfter.includes('-')) {
    const date = new Date(retryAfter);
    if (!isNaN(date.getTime())) {
      return date.getTime();
    }
  }

  // Try parsing as seconds
  const seconds = parseInt(retryAfter, 10);
  if (!isNaN(seconds) && seconds.toString() === retryAfter.trim()) {
    return Date.now() + (seconds * 1000);
  }

  // Try parsing as HTTP date (fallback)
  const date = new Date(retryAfter);
  if (!isNaN(date.getTime())) {
    return date.getTime();
  }

  // Default to 60 seconds if parsing fails
  console.warn(`Failed to parse Retry-After header: ${retryAfter}, using default 60s`);
  return Date.now() + 60000;
}

function handleRateLimit(response: Response, hostname: string, state: RateLimitState): boolean {
  if (response.status !== 429) return false;

  const retryAfter = response.headers.get('Retry-After') ?? response.headers.get('x-ratelimit-reset');
  if (retryAfter) {
    state.nextAllowedTime = parseRetryAfter(retryAfter);
    logRateLimit(hostname, 'Rate limited with Retry-After header', {
      retryAfter,
      nextAllowedTime: new Date(state.nextAllowedTime).toISOString()
    });
  } else {
    state.nextAllowedTime = Date.now() + 60000; // Default 60s
    logRateLimit(hostname, 'Rate limited without Retry-After header', {
      defaultDelay: '60s'
    });
  }

  updateMetrics(state, 0, true);
  return true;
}

function handleRateLimitException(error: unknown, hostname: string, state: RateLimitState): boolean {
  if (!(error instanceof Error) || !error.message.includes('429')) return false;

  console.warn(`Rate limit exception for ${hostname}, using default retry delay`);
  state.nextAllowedTime = Date.now() + 60000;
  return true;
}

function queueRequest(
  url: string,
  options: RequestInit | undefined,
  state: RateLimitState,
  priority: RequestPriority = RequestPriority.NORMAL
): Promise<Response> {
  return new Promise((resolve) => {
    const request: QueuedRequest = {
      url,
      options,
      resolve,
      priority,
      timestamp: Date.now()
    };

    // Insert request based on priority
    const insertIndex = state.queue.findIndex(req => req.priority < priority);
    if (insertIndex === -1) {
      state.queue.push(request);
    } else {
      state.queue.splice(insertIndex, 0, request);
    }

    state.metrics.queuedRequests++;
    logRateLimit(getHostname(url), 'Request queued', {
      priority,
      queueLength: state.queue.length
    });
  });
}

function queueAndProcess(
  url: string,
  options: RequestInit | undefined,
  hostname: string,
  state: RateLimitState,
  priority: RequestPriority = RequestPriority.NORMAL
): Promise<Response> {
  const promise = queueRequest(url, options, state, priority);
  if (!state.processing) {
    // noinspection JSIgnoredPromiseFromCall
    processQueue(hostname);
  }
  return promise;
}

async function fetchWithRateLimit(
  url: string,
  options: RequestInit | undefined,
  hostname: string,
  state: RateLimitState,
  onRateLimit: () => void
): Promise<Response | null> {
  try {
    const response = await fetch(url, options);

    if (handleRateLimit(response, hostname, state)) {
      onRateLimit();
      return null; // Signal that rate limit was hit
    }

    if (!response.ok) {
      console.trace(`Failed to fetch ${url}`, response.statusText);
    }

    return response;
  } catch (error) {
    if (handleRateLimitException(error, hostname, state)) {
      onRateLimit();
      return null; // Signal that rate limit was hit
    }

    throw error;
  }
}

async function processQueue(hostname: string): Promise<void> {
  const state = rateLimitStates.get(hostname);
  if (!state || state.processing) return;

  state.processing = true;

  while (state.queue.length > 0) {
    const now = Date.now();
    if (now < state.nextAllowedTime) {
      const delay = state.nextAllowedTime - now;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    const request = state.queue.shift();
    if (!request) break;

    const response = await fetchWithRateLimit(
      request.url,
      request.options,
      hostname,
      state,
      () => state.queue.unshift(request) // Re-queue at front
    );

    if (response) {
      request.resolve(response);
    }
  }

  state.processing = false;
}

export async function rateLimitedFetch(
  url: string,
  options?: RequestInit,
  priority: RequestPriority = RequestPriority.NORMAL
): Promise<Response> {
  const hostname = getHostname(url);
  const startTime = Date.now();

  if (!rateLimitStates.has(hostname)) {
    rateLimitStates.set(hostname, initializeRateLimitState(hostname));
  }

  const state = rateLimitStates.get(hostname)!;
  const config = getDomainConfig(hostname);
  const now = Date.now();

  logRateLimit(hostname, 'Request initiated', {
    url,
    priority,
    queueLength: state.queue.length
  });

  if (now >= state.nextAllowedTime && state.queue.length === 0 && !state.processing) {
    let shouldQueue = false;

    const response = await fetchWithRateLimit(
      url,
      options,
      hostname,
      state,
      () => {
        shouldQueue = true;
      } // Mark for queueing
    );

    if (response) {
      const waitTime = Date.now() - startTime;
      updateMetrics(state, waitTime, false);
      logRateLimit(hostname, 'Request completed directly', {
        waitTime,
        status: response.status
      });
      return response;
    }

    if (shouldQueue) {
      return queueAndProcess(url, options, hostname, state, priority);
    }
  }

  return queueAndProcess(url, options, hostname, state, priority);
}

/**
 * Get metrics for a specific domain
 */
export function getDomainMetrics(hostname: string): RateLimitMetrics | null {
  const state = rateLimitStates.get(hostname);
  return state ? { ...state.metrics } : null;
}

/**
 * Get global rate limiter metrics
 */
export function getGlobalMetrics(): GlobalMetrics {
  let totalRequests = 0;
  let totalRateLimitHits = 0;
  let totalResponseTime = 0;
  let activeConnections = 0;

  for (const [hostname, state] of rateLimitStates) {
    totalRequests += state.metrics.totalRequests;
    totalRateLimitHits += state.metrics.rateLimitHits;
    totalResponseTime += state.metrics.averageWaitTime;

    if (state.processing || state.queue.length > 0) {
      activeConnections++;
    }
  }

  return {
    totalRequests,
    totalRateLimitHits,
    averageResponseTime: rateLimitStates.size > 0 ? totalResponseTime / rateLimitStates.size : 0,
    activeConnections,
    domainsTracked: rateLimitStates.size
  };
}

/**
 * Reset metrics for a domain or all domains
 */
export function resetMetrics(hostname?: string): void {
  if (hostname) {
    const state = rateLimitStates.get(hostname);
    if (state) {
      state.metrics = {
        totalRequests: 0,
        queuedRequests: 0,
        rateLimitHits: 0,
        averageWaitTime: 0,
        lastRequestTime: 0
      };
      logRateLimit(hostname, 'Metrics reset');
    }
  } else {
    for (const [hostname, state] of rateLimitStates) {
      state.metrics = {
        totalRequests: 0,
        queuedRequests: 0,
        rateLimitHits: 0,
        averageWaitTime: 0,
        lastRequestTime: 0
      };
    }
    console.log('[RateLimit] All metrics reset');
  }
}

/**
 * Configure rate limits for a specific domain
 */
export function configureDomain(hostname: string, config: Partial<DomainConfig>): void {
  const currentConfig = getDomainConfig(hostname);
  const newConfig = { ...currentConfig, ...config };
  domainConfigs.set(hostname, newConfig);

  logRateLimit(hostname, 'Domain configuration updated', newConfig);
}

/**
 * Get current domain configuration
 */
export function getDomainConfiguration(hostname: string): DomainConfig {
  return { ...getDomainConfig(hostname) };
}
