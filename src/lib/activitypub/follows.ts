import type { Follow, Accept } from '$lib/activitypub/types';
import { follow as followTable, user as userTable } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { eq, and } from 'drizzle-orm';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';
import { processActivityBase } from '$lib/activitypub/utils/activities';

/**
 * Process a Follow activity and create a follow record
 */
export async function processFollow(activity: Follow): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object')
    }),
    processActivity: async (activity, actors) => {
      const followerUri = actors.actorUri;
      const followingUri = actors.objectUri;
      const follower = actors.actor;

      // Check if the target user exists locally
      const followingUser = await db.query.user.findFirst({
        where: eq(userTable.uri, followingUri)
      });

      if (!followingUser) {
        console.warn(`Target user not found locally: ${followingUri}`);
        return false;
      }

      // Check if follow relationship already exists
      const existingFollow = await db.query.follow.findFirst({
        where: and(
          eq(followTable.followerUri, followerUri),
          eq(followTable.followingUri, followingUri)
        )
      });

      if (existingFollow) {
        // Update existing follow if it was previously rejected or if we have new activity URI
        if (existingFollow.status === 'rejected' || !existingFollow.followActivityUri) {
          await db.update(followTable)
            .set({
              status: 'pending',
              followActivityUri: activity.id?.toString(),
              requestedAt: activity.published ? new Date(activity.published) : new Date(),
              rejectedAt: null
            })
            .where(eq(followTable.id, existingFollow.id));

          console.log(`Updated existing follow request: ${followerUri} -> ${followingUri}`);
          return true;
        } else {
          console.log(`Follow relationship already exists: ${followerUri} -> ${followingUri}`);
          return true;
        }
      }

      // Create new follow record
      const followRecord: typeof followTable.$inferInsert = {
        followerId: follower.id,
        followerUri,
        followingId: followingUser.id,
        followingUri,
        status: followingUser.manuallyApprovedFollowers ? 'pending' : 'accepted',
        followActivityUri: activity.id?.toString(),
        requestedAt: activity.published ? new Date(activity.published) : new Date(),
        acceptedAt: followingUser.manuallyApprovedFollowers ? null : new Date(),
        isLocal: false // This is an incoming follow from a remote actor
      };

      const result = await db.insert(followTable).values(followRecord).returning();

      if (result.length > 0) {
        const status = followRecord.status;
        console.log(`Created ${status} follow: ${followerUri} -> ${followingUri}`);

        // TODO: If auto-accepted, we should send an Accept activity back
        // TODO: Create notification for the target user

        return true;
      } else {
        console.warn(`Failed to create follow record: ${followerUri} -> ${followingUri}`);
        return false;
      }
    },
    activityName: 'Follow'
  });
}

/**
 * Get follow record by follower and following URIs
 */
export async function getFollow(followerUri: string, followingUri: string) {
  return await db.query.follow.findFirst({
    where: and(
      eq(followTable.followerUri, followerUri),
      eq(followTable.followingUri, followingUri)
    )
  });
}

/**
 * Update follow status
 */
export async function updateFollowStatus(
  followId: string, 
  status: 'pending' | 'accepted' | 'rejected',
  acceptActivityUri?: string
): Promise<boolean> {
  try {
    const updateData: Partial<typeof followTable.$inferInsert> = {
      status
    };

    if (status === 'accepted') {
      updateData.acceptedAt = new Date();
      updateData.rejectedAt = null;
      if (acceptActivityUri) {
        updateData.acceptActivityUri = acceptActivityUri;
      }
    } else if (status === 'rejected') {
      updateData.rejectedAt = new Date();
      updateData.acceptedAt = null;
    }

    const result = await db.update(followTable)
      .set(updateData)
      .where(eq(followTable.id, followId))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error('Error updating follow status:', error);
    throw error;
  }
}

/**
 * Delete a follow record (for Undo Follow activities)
 */
export async function deleteFollow(followerUri: string, followingUri: string): Promise<boolean> {
  try {
    const result = await db.delete(followTable)
      .where(and(
        eq(followTable.followerUri, followerUri),
        eq(followTable.followingUri, followingUri)
      ))
      .returning();

    if (result.length > 0) {
      console.log(`Deleted follow: ${followerUri} -> ${followingUri}`);
      return true;
    } else {
      console.warn(`Follow not found for deletion: ${followerUri} -> ${followingUri}`);
      return false;
    }
  } catch (error) {
    console.error('Error deleting follow:', error);
    throw error;
  }
}

/**
 * Process an Accept activity (typically for accepting follow requests)
 */
export async function processAccept(activity: Accept): Promise<boolean> {
  try {
    if (!activity.actor || !activity.object) {
      console.warn('Accept activity missing actor or object');
      return false;
    }

    const acceptorUri = getUrlFromAPObjectRequired(activity.actor, 'actor');

    // The object should be a Follow activity URI
    const followActivityUri = getUrlFromAPObjectRequired(activity.object, 'object');

    // Find the follow record by the follow activity URI
    const followRecord = await db.query.follow.findFirst({
      where: eq(followTable.followActivityUri, followActivityUri)
    });

    if (!followRecord) {
      console.warn(`Follow record not found for activity: ${followActivityUri}`);
      return false;
    }

    // Verify that the acceptor is the one being followed
    if (followRecord.followingUri !== acceptorUri) {
      console.warn(`Accept activity actor mismatch: expected ${followRecord.followingUri}, got ${acceptorUri}`);
      return false;
    }

    // Update the follow status to accepted
    const success = await updateFollowStatus(
      followRecord.id,
      'accepted',
      activity.id?.toString()
    );

    if (success) {
      console.log(`Accepted follow: ${followRecord.followerUri} -> ${followRecord.followingUri}`);

      // TODO: Create notification for the follower
      // TODO: Update follower/following counts

      return true;
    } else {
      console.warn(`Failed to update follow status for: ${followActivityUri}`);
      return false;
    }
  } catch (error) {
    console.error('Error processing accept activity:', error);
    throw error;
  }
}
