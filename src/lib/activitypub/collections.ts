import type { Add, Remove } from '$lib/activitypub/types';
import { activity as activityTable, user as userTable, post as postTable } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { eq, and } from 'drizzle-orm';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';
import { processActivityBase } from '$lib/activitypub/utils/activities';

/**
 * Process an Add activity
 */
export async function processAdd(activity: Add): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object', 'target'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object'),
      targetUri: getUrlFromAPObjectRequired(activity.target, 'target')
    }),
    processActivity: async (activity, actors) => {
      const actorUri = actors.actorUri;
      const objectUri = actors.objectUri;
      const targetUri = actors.targetUri;
      const actor = actors.actor;

      // Check if this is a featured collection (pinned posts)
      if (targetUri.endsWith('/featured') || targetUri.includes('/featured')) {
        return await addToFeaturedCollection(actorUri, objectUri, activity);
      }

      // For other collections, just log and store the activity
      console.log(`Add to collection: ${objectUri} -> ${targetUri} by ${actorUri}`);

      // Check if add activity already exists
      const existingActivity = await db.query.activity.findFirst({
        where: and(
          eq(activityTable.type, 'Add'),
          eq(activityTable.actorUri, actorUri),
          eq(activityTable.objectUri, objectUri),
          eq(activityTable.targetUri, targetUri)
        )
      });

      if (existingActivity) {
        console.log(`Add already exists: ${objectUri} -> ${targetUri}`);
        return true;
      }

      // Create activity record
      const activityRecord: typeof activityTable.$inferInsert = {
        uri: activity.id?.toString() || `${actorUri}/adds/${Date.now()}`,
        type: 'Add',
        activityPubObject: activity,
        actorId: actor.id,
        actorUri: actorUri,
        objectUri: objectUri,
        targetUri: targetUri,
        direction: 'inbound',
        status: 'completed',
        published: activity.published ? new Date(activity.published) : new Date()
      };

      const result = await db.insert(activityTable).values(activityRecord).returning();

      if (result.length > 0) {
        console.log(`Created Add activity: ${objectUri} -> ${targetUri}`);
        return true;
      } else {
        console.warn(`Failed to create Add activity record`);
        return false;
      }
    },
    activityName: 'Add'
  });
}

/**
 * Add an object to a featured collection (pinned posts)
 */
async function addToFeaturedCollection(actorUri: string, objectUri: string, activity: Add): Promise<boolean> {
  try {
    // Check if the object is a post that exists locally
    const post = await db.query.post.findFirst({
      where: eq(postTable.uri, objectUri)
    });

    if (!post) {
      console.warn(`Post not found for featuring: ${objectUri}`);
      // Still record the activity even if we don't have the post locally
    }

    // Check if already featured
    const existingFeature = await db.query.activity.findFirst({
      where: and(
        eq(activityTable.type, 'Add'),
        eq(activityTable.actorUri, actorUri),
        eq(activityTable.objectUri, objectUri),
        eq(activityTable.status, 'completed')
      )
    });

    if (existingFeature) {
      console.log(`Post already featured: ${objectUri}`);
      return true;
    }

    // TODO: In a real implementation, you might want to:
    // 1. Update the post record to mark it as featured
    // 2. Update the user's featured collection
    // 3. Implement limits on featured posts
    
    console.log(`Featured post: ${objectUri} by ${actorUri}`);
    return true;
  } catch (error) {
    console.error('Error adding to featured collection:', error);
    throw error;
  }
}

/**
 * Process a Remove activity
 */
export async function processRemove(activity: Remove): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object', 'target'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object'),
      targetUri: getUrlFromAPObjectRequired(activity.target, 'target')
    }),
    processActivity: async (activity, actors) => {
      const actorUri = actors.actorUri;
      const objectUri = actors.objectUri;
      const targetUri = actors.targetUri;
      const actor = actors.actor;

      // Check if this is removing from a featured collection
      if (targetUri.endsWith('/featured') || targetUri.includes('/featured')) {
        return await removeFromFeaturedCollection(actorUri, objectUri, activity);
      }

      // For other collections, mark the corresponding Add activity as undone
      const result = await db.update(activityTable)
        .set({ status: 'undone' })
        .where(and(
          eq(activityTable.type, 'Add'),
          eq(activityTable.actorUri, actorUri),
          eq(activityTable.objectUri, objectUri),
          eq(activityTable.targetUri, targetUri),
          eq(activityTable.status, 'completed')
        ))
        .returning();

      if (result.length > 0) {
        console.log(`Removed from collection: ${objectUri} from ${targetUri}`);

        // Also create a Remove activity record
        const removeRecord: typeof activityTable.$inferInsert = {
          uri: activity.id?.toString() || `${actorUri}/removes/${Date.now()}`,
          type: 'Remove',
          activityPubObject: activity,
          actorId: actor.id,
          actorUri: actorUri,
          objectUri: objectUri,
          targetUri: targetUri,
          direction: 'inbound',
          status: 'completed',
          published: activity.published ? new Date(activity.published) : new Date()
        };

        await db.insert(activityTable).values(removeRecord);
        return true;
      } else {
        console.warn(`No Add activity found to remove: ${objectUri} from ${targetUri}`);
        return false;
      }
    },
    activityName: 'Remove'
  });
}

/**
 * Remove an object from a featured collection
 */
async function removeFromFeaturedCollection(actorUri: string, objectUri: string, activity: Remove): Promise<boolean> {
  try {
    // Mark the Add activity as undone
    const result = await db.update(activityTable)
      .set({ status: 'undone' })
      .where(and(
        eq(activityTable.type, 'Add'),
        eq(activityTable.actorUri, actorUri),
        eq(activityTable.objectUri, objectUri),
        eq(activityTable.status, 'completed')
      ))
      .returning();

    if (result.length > 0) {
      console.log(`Unfeatured post: ${objectUri} by ${actorUri}`);
      
      // TODO: In a real implementation, you might want to:
      // 1. Update the post record to unmark it as featured
      // 2. Update the user's featured collection
      
      return true;
    } else {
      console.warn(`No featured post found to remove: ${objectUri}`);
      return false;
    }
  } catch (error) {
    console.error('Error removing from featured collection:', error);
    throw error;
  }
}

/**
 * Get objects in a collection
 */
export async function getCollectionObjects(actorUri: string, targetUri: string): Promise<string[]> {
  try {
    const addActivities = await db.query.activity.findMany({
      where: and(
        eq(activityTable.type, 'Add'),
        eq(activityTable.actorUri, actorUri),
        eq(activityTable.targetUri, targetUri),
        eq(activityTable.status, 'completed')
      )
    });

    return addActivities
      .map(activity => activity.objectUri)
      .filter((uri): uri is string => uri !== null);
  } catch (error) {
    console.error('Error getting collection objects:', error);
    return [];
  }
}

/**
 * Check if an object is in a collection
 */
export async function isInCollection(actorUri: string, objectUri: string, targetUri: string): Promise<boolean> {
  try {
    const addActivity = await db.query.activity.findFirst({
      where: and(
        eq(activityTable.type, 'Add'),
        eq(activityTable.actorUri, actorUri),
        eq(activityTable.objectUri, objectUri),
        eq(activityTable.targetUri, targetUri),
        eq(activityTable.status, 'completed')
      )
    });

    return !!addActivity;
  } catch (error) {
    console.error('Error checking collection membership:', error);
    return false;
  }
}
