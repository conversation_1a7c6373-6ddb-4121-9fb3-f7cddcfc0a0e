import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processLike, processAnnounce, removeLike, removeAnnounce } from './reactions.js';
import { ActivityPubTestBase } from '../../test/activitypub-test-utils';

// Mock dependencies
vi.mock('$lib/server/db', () => ({
  db: {
    query: {
      post: {
        findFirst: vi.fn()
      },
      reaction: {
        findFirst: vi.fn()
      }
    },
    insert: vi.fn().mockReturnValue({
      values: vi.fn().mockReturnValue({
        returning: vi.fn()
      })
    }),
    update: vi.fn().mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn()
        })
      })
    })
  }
}));

vi.mock('./utils/links', () => ({
  getUrlFromAPObjectRequired: vi.fn()
}));

vi.mock('./utils/activities', () => ({
  processActivityBase: vi.fn()
}));

vi.mock('./actors', () => ({
  discoverActor: vi.fn()
}));

import { db } from '$lib/server/db';
import { getUrlFromAPObjectRequired } from './utils/links';
import { processActivityBase } from './utils/activities';

describe('Reactions Processing', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('processLike', () => {
    it('should process a Like activity successfully', async () => {
      const likeActivity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Like',
        id: 'https://example.com/activities/like-1',
        actor: 'https://example.com/users/testuser',
        object: 'https://example.com/posts/1',
        published: new Date().toISOString()
      };

      vi.mocked(processActivityBase).mockResolvedValue(true);

      const result = await processLike(likeActivity);

      expect(result).toBe(true);
      expect(processActivityBase).toHaveBeenCalledWith(
        likeActivity,
        expect.objectContaining({
          requiredFields: ['actor', 'object'],
          activityName: 'Like'
        })
      );
    });

    it('should handle missing required fields', async () => {
      const invalidActivity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Like',
        id: 'https://example.com/activities/like-1'
        // Missing actor and object
      };

      vi.mocked(processActivityBase).mockResolvedValue(false);

      const result = await processLike(invalidActivity as any);

      expect(result).toBe(false);
    });
  });

  describe('processAnnounce', () => {
    it('should process an Announce activity successfully', async () => {
      const announceActivity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Announce',
        id: 'https://example.com/activities/announce-1',
        actor: 'https://example.com/users/testuser',
        object: 'https://example.com/posts/1',
        published: new Date().toISOString()
      };

      vi.mocked(processActivityBase).mockResolvedValue(true);

      const result = await processAnnounce(announceActivity);

      expect(result).toBe(true);
      expect(processActivityBase).toHaveBeenCalledWith(
        announceActivity,
        expect.objectContaining({
          requiredFields: ['actor', 'object'],
          activityName: 'Announce'
        })
      );
    });
  });

  describe('removeLike', () => {
    it('should remove a like reaction successfully', async () => {
      const mockReaction = {
        id: 'reaction-1',
        userUri: 'https://example.com/users/testuser',
        postUri: 'https://example.com/posts/1',
        type: 'like',
        isActive: true
      };

      const mockPost = {
        id: 'post-1',
        uri: 'https://example.com/posts/1',
        likeCount: 5
      };

      vi.mocked(db.query.reaction.findFirst).mockResolvedValue(mockReaction);
      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([mockReaction])
          })
        })
      } as any);

      const result = await removeLike(
        'https://example.com/users/testuser',
        'https://example.com/posts/1'
      );

      expect(result).toBe(true);
      expect(db.query.reaction.findFirst).toHaveBeenCalled();
    });

    it('should return false when like not found', async () => {
      vi.mocked(db.query.reaction.findFirst).mockResolvedValue(null);

      const result = await removeLike(
        'https://example.com/users/testuser',
        'https://example.com/posts/1'
      );

      expect(result).toBe(false);
    });
  });

  describe('removeAnnounce', () => {
    it('should remove an announce reaction successfully', async () => {
      const mockReaction = {
        id: 'reaction-1',
        userUri: 'https://example.com/users/testuser',
        postUri: 'https://example.com/posts/1',
        type: 'announce',
        isActive: true
      };

      const mockPost = {
        id: 'post-1',
        uri: 'https://example.com/posts/1',
        shareCount: 3
      };

      vi.mocked(db.query.reaction.findFirst).mockResolvedValue(mockReaction);
      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([mockReaction])
          })
        })
      } as any);

      const result = await removeAnnounce(
        'https://example.com/users/testuser',
        'https://example.com/posts/1'
      );

      expect(result).toBe(true);
      expect(db.query.reaction.findFirst).toHaveBeenCalled();
    });

    it('should return false when announce not found', async () => {
      vi.mocked(db.query.reaction.findFirst).mockResolvedValue(null);

      const result = await removeAnnounce(
        'https://example.com/users/testuser',
        'https://example.com/posts/1'
      );

      expect(result).toBe(false);
    });
  });
});
