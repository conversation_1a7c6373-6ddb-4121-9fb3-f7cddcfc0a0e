import type { Like, Announce } from '$lib/activitypub/types';
import { reaction as reactionTable, post as postTable, user as userTable } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { eq, and } from 'drizzle-orm';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';
import { processActivityBase } from '$lib/activitypub/utils/activities';
import { discoverActor } from '$lib/activitypub/actors';

/**
 * Process a Like activity and create a reaction record
 */
export async function processLike(activity: Like): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object')
    }),
    processActivity: async (activity, actors) => {
      const actorUri = actors.actorUri;
      const objectUri = actors.objectUri;
      const actor = actors.actor;

      // Check if the target post exists locally
      const targetPost = await db.query.post.findFirst({
        where: eq(postTable.uri, objectUri)
      });

      if (!targetPost) {
        console.warn(`Target post not found locally: ${objectUri}`);
        return false;
      }

      // Check if reaction already exists
      const existingReaction = await db.query.reaction.findFirst({
        where: and(
          eq(reactionTable.userUri, actorUri),
          eq(reactionTable.postUri, objectUri),
          eq(reactionTable.type, 'like'),
          eq(reactionTable.isActive, true)
        )
      });

      if (existingReaction) {
        console.log(`Like already exists: ${actorUri} -> ${objectUri}`);
        return true;
      }

      // Create reaction record
      const reactionRecord: typeof reactionTable.$inferInsert = {
        userId: actor.id,
        userUri: actorUri,
        postId: targetPost.id,
        postUri: objectUri,
        type: 'like',
        activityUri: activity.id?.toString() || `${actorUri}/likes/${Date.now()}`,
        isActive: true,
        createdAt: new Date()
      };

      const result = await db.insert(reactionTable).values(reactionRecord).returning();

      if (result.length > 0) {
        // Update post like count
        await db.update(postTable)
          .set({ 
            likeCount: targetPost.likeCount + 1 
          })
          .where(eq(postTable.id, targetPost.id));

        console.log(`Created like: ${actorUri} -> ${objectUri}`);
        return true;
      } else {
        console.warn(`Failed to create like reaction`);
        return false;
      }
    },
    activityName: 'Like'
  });
}

/**
 * Process an Announce activity and create a reaction record
 */
export async function processAnnounce(activity: Announce): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object')
    }),
    processActivity: async (activity, actors) => {
      const actorUri = actors.actorUri;
      const objectUri = actors.objectUri;
      const actor = actors.actor;

      // Check if the target post exists locally
      const targetPost = await db.query.post.findFirst({
        where: eq(postTable.uri, objectUri)
      });

      if (!targetPost) {
        console.warn(`Target post not found locally: ${objectUri}`);
        return false;
      }

      // Check if reaction already exists
      const existingReaction = await db.query.reaction.findFirst({
        where: and(
          eq(reactionTable.userUri, actorUri),
          eq(reactionTable.postUri, objectUri),
          eq(reactionTable.type, 'announce'),
          eq(reactionTable.isActive, true)
        )
      });

      if (existingReaction) {
        console.log(`Announce already exists: ${actorUri} -> ${objectUri}`);
        return true;
      }

      // Create reaction record
      const reactionRecord: typeof reactionTable.$inferInsert = {
        userId: actor.id,
        userUri: actorUri,
        postId: targetPost.id,
        postUri: objectUri,
        type: 'announce',
        activityUri: activity.id?.toString() || `${actorUri}/announces/${Date.now()}`,
        isActive: true,
        createdAt: new Date()
      };

      const result = await db.insert(reactionTable).values(reactionRecord).returning();

      if (result.length > 0) {
        // Update post share count
        await db.update(postTable)
          .set({ 
            shareCount: targetPost.shareCount + 1 
          })
          .where(eq(postTable.id, targetPost.id));

        console.log(`Created announce: ${actorUri} -> ${objectUri}`);
        return true;
      } else {
        console.warn(`Failed to create announce reaction`);
        return false;
      }
    },
    activityName: 'Announce'
  });
}

/**
 * Remove a like reaction (for Undo Like activities)
 */
export async function removeLike(actorUri: string, objectUri: string): Promise<boolean> {
  try {
    // Find the active like reaction
    const existingReaction = await db.query.reaction.findFirst({
      where: and(
        eq(reactionTable.userUri, actorUri),
        eq(reactionTable.postUri, objectUri),
        eq(reactionTable.type, 'like'),
        eq(reactionTable.isActive, true)
      )
    });

    if (!existingReaction) {
      console.warn(`Like not found for removal: ${actorUri} -> ${objectUri}`);
      return false;
    }

    // Mark reaction as inactive
    const result = await db.update(reactionTable)
      .set({ 
        isActive: false,
        undoneAt: new Date()
      })
      .where(eq(reactionTable.id, existingReaction.id))
      .returning();

    if (result.length > 0) {
      // Update post like count
      const targetPost = await db.query.post.findFirst({
        where: eq(postTable.uri, objectUri)
      });

      if (targetPost && targetPost.likeCount > 0) {
        await db.update(postTable)
          .set({ 
            likeCount: targetPost.likeCount - 1 
          })
          .where(eq(postTable.id, targetPost.id));
      }

      console.log(`Removed like: ${actorUri} -> ${objectUri}`);
      return true;
    } else {
      console.warn(`Failed to remove like reaction`);
      return false;
    }
  } catch (error) {
    console.error('Error removing like:', error);
    throw error;
  }
}

/**
 * Remove an announce reaction (for Undo Announce activities)
 */
export async function removeAnnounce(actorUri: string, objectUri: string): Promise<boolean> {
  try {
    // Find the active announce reaction
    const existingReaction = await db.query.reaction.findFirst({
      where: and(
        eq(reactionTable.userUri, actorUri),
        eq(reactionTable.postUri, objectUri),
        eq(reactionTable.type, 'announce'),
        eq(reactionTable.isActive, true)
      )
    });

    if (!existingReaction) {
      console.warn(`Announce not found for removal: ${actorUri} -> ${objectUri}`);
      return false;
    }

    // Mark reaction as inactive
    const result = await db.update(reactionTable)
      .set({ 
        isActive: false,
        undoneAt: new Date()
      })
      .where(eq(reactionTable.id, existingReaction.id))
      .returning();

    if (result.length > 0) {
      // Update post share count
      const targetPost = await db.query.post.findFirst({
        where: eq(postTable.uri, objectUri)
      });

      if (targetPost && targetPost.shareCount > 0) {
        await db.update(postTable)
          .set({ 
            shareCount: targetPost.shareCount - 1 
          })
          .where(eq(postTable.id, targetPost.id));
      }

      console.log(`Removed announce: ${actorUri} -> ${objectUri}`);
      return true;
    } else {
      console.warn(`Failed to remove announce reaction`);
      return false;
    }
  } catch (error) {
    console.error('Error removing announce:', error);
    throw error;
  }
}
