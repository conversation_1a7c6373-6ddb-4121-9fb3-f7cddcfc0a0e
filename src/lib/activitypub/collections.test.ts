import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processAdd, processRemove, getCollectionObjects, isInCollection } from './collections';
import { createTestAddActivity, createTestRemoveActivity } from '../../test/activitypub-helpers';
import {
  ActivityPubTestBase,
  activityPubMocks,
  mockConfigurations,
  testDataFactories,
  mockDb,
  commonMockImplementations
} from '../../test/activitypub-test-utils';

// Mock dependencies using common utilities
vi.mock('./actors', () => ({
  discoverActor: vi.fn()
}));

vi.mock('$lib/activitypub/utils/links', () => ({
  getUrlFromAPObjectRequired: vi.fn(),
  getUrlFromAPObjectSafe: vi.fn()
}));

import { db } from '$lib/server/db';
import { discoverActor } from './actors';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';

describe('Collection Processing', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('processAdd', () => {
    it('should process Add activity successfully for featured collection', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/featured';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null); // No existing add

      // Act
      const result = await processAdd(activity);

      // Assert
      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/actor');
    });

    it('should process Add activity successfully for regular collection', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/collection');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/collection';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null); // No existing add

      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'new-add-id' }])
        })
      } as any);

      // Act
      const result = await processAdd(activity);

      // Assert
      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/actor');
      expect(db.insert).toHaveBeenCalled();
    });

    it('should return false if actor, object, or target is missing', async () => {
      // Arrange
      const activity = { type: 'Add', actor: 'https://example.com/users/actor' } as any;

      // Act
      const result = await processAdd(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if actor cannot be discovered', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/featured';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(null);

      // Act
      const result = await processAdd(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should return true if add already exists for regular collection', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/collection');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };
      const existingAdd = { id: 'existing-add-id' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/collection';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(existingAdd);

      // Act
      const result = await processAdd(activity);

      // Assert
      expect(result).toBe(true);
      expect(db.insert).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const activity = createTestAddActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation(() => {
        throw new Error('URL parsing failed');
      });

      // Act & Assert
      await expect(processAdd(activity)).rejects.toThrow('URL parsing failed');
    });
  });

  describe('processRemove', () => {
    it('should process Remove activity successfully for featured collection', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/featured';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);

      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{ id: 'updated-add-id' }])
          })
        })
      } as any);

      // Act
      const result = await processRemove(activity);

      // Assert
      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/actor');
      expect(db.update).toHaveBeenCalled();
    });

    it('should process Remove activity successfully for regular collection', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/collection');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/collection';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);

      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{ id: 'updated-add-id' }])
          })
        })
      } as any);

      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'new-remove-id' }])
        })
      } as any);

      // Act
      const result = await processRemove(activity);

      // Assert
      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/actor');
      expect(db.update).toHaveBeenCalled();
      expect(db.insert).toHaveBeenCalled();
    });

    it('should return false if actor, object, or target is missing', async () => {
      // Arrange
      const activity = { type: 'Remove', actor: 'https://example.com/users/actor' } as any;

      // Act
      const result = await processRemove(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if actor cannot be discovered', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/featured';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(null);

      // Act
      const result = await processRemove(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if no Add activity found to remove from featured collection', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');
      const mockActor = { id: 'actor-id', uri: 'https://example.com/users/actor' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/actor';
        if (field === 'object') return 'https://example.com/notes/1';
        if (field === 'target') return 'https://example.com/users/actor/featured';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockActor);

      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([]) // No rows updated
          })
        })
      } as any);

      // Act
      const result = await processRemove(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const activity = createTestRemoveActivity('https://example.com/notes/1', 'https://example.com/users/actor/featured');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation(() => {
        throw new Error('URL parsing failed');
      });

      // Act & Assert
      await expect(processRemove(activity)).rejects.toThrow('URL parsing failed');
    });
  });

  describe('getCollectionObjects', () => {
    it('should return list of objects in collection', async () => {
      // Arrange
      const actorUri = 'https://example.com/users/actor';
      const targetUri = 'https://example.com/users/actor/featured';
      const addActivities = [
        { objectUri: 'https://example.com/notes/1' },
        { objectUri: 'https://example.com/notes/2' },
        { objectUri: null } // Should be filtered out
      ];

      vi.mocked(db.query.activity.findMany).mockResolvedValue(addActivities);

      // Act
      const result = await getCollectionObjects(actorUri, targetUri);

      // Assert
      expect(result).toEqual([
        'https://example.com/notes/1',
        'https://example.com/notes/2'
      ]);
    });

    it('should handle database errors', async () => {
      // Arrange
      const actorUri = 'https://example.com/users/actor';
      const targetUri = 'https://example.com/users/actor/featured';

      vi.mocked(db.query.activity.findMany).mockRejectedValue(new Error('Database error'));

      // Act
      const result = await getCollectionObjects(actorUri, targetUri);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('isInCollection', () => {
    it('should return true if object is in collection', async () => {
      // Arrange
      const actorUri = 'https://example.com/users/actor';
      const objectUri = 'https://example.com/notes/1';
      const targetUri = 'https://example.com/users/actor/featured';
      const existingAdd = { id: 'add-id' };

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(existingAdd);

      // Act
      const result = await isInCollection(actorUri, objectUri, targetUri);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false if object is not in collection', async () => {
      // Arrange
      const actorUri = 'https://example.com/users/actor';
      const objectUri = 'https://example.com/notes/1';
      const targetUri = 'https://example.com/users/actor/featured';

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null);

      // Act
      const result = await isInCollection(actorUri, objectUri, targetUri);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle database errors', async () => {
      // Arrange
      const actorUri = 'https://example.com/users/actor';
      const objectUri = 'https://example.com/notes/1';
      const targetUri = 'https://example.com/users/actor/featured';

      vi.mocked(db.query.activity.findFirst).mockRejectedValue(new Error('Database error'));

      // Act
      const result = await isInCollection(actorUri, objectUri, targetUri);

      // Assert
      expect(result).toBe(false);
    });
  });
});
