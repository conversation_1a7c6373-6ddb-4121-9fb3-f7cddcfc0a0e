/**
 * Types for ActivityPub federation, inbox/outbox processing, and HTTP signatures.
 */

import type { Activity, Actor } from './Extended';
import type { CoreObject } from './Core';

// HTTP Signature types
export interface HttpSignatureHeader {
  keyId: string;
  algorithm: string;
  headers: string[];
  signature: string;
}

export interface HttpSignatureVerificationResult {
  valid: boolean;
  keyId?: string;
  actor?: Actor;
  error?: string;
}

// Inbox/Outbox processing types
export type ActivityDirection = 'inbound' | 'outbound';

export type ActivityProcessingStatus = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'duplicate';

export type ActivityDeliveryStatus = 
  | 'pending' 
  | 'delivered' 
  | 'failed' 
  | 'permanent_failure';

export interface InboxRequest {
  activity: Activity;
  signature?: HttpSignatureHeader;
  rawBody: string;
  headers: Record<string, string>;
  sourceIp?: string;
}

export interface OutboxRequest {
  activity: Activity;
  actor: Actor;
  recipients: string[]; // inbox URLs
}

export interface ActivityProcessingContext {
  activity: Activity;
  direction: ActivityDirection;
  signature?: HttpSignatureHeader;
  actor?: Actor;
  localActor?: Actor;
  rawActivity: string;
  receivedAt: Date;
}

export interface ActivityProcessingResult {
  success: boolean;
  status: ActivityProcessingStatus;
  activityId?: string;
  localObjectId?: string;
  error?: string;
  shouldRetry?: boolean;
}

// Delivery tracking types
export interface DeliveryAttempt {
  recipientInbox: string;
  recipientActor: string;
  attempt: number;
  httpStatus?: number;
  error?: string;
  timestamp: Date;
}

export interface DeliveryResult {
  recipientInbox: string;
  status: ActivityDeliveryStatus;
  attempts: DeliveryAttempt[];
  nextRetryAt?: Date;
  finalError?: string;
}

// Error types
export type FederationErrorType = 
  | 'invalid_signature'
  | 'invalid_activity'
  | 'actor_not_found'
  | 'delivery_failed'
  | 'rate_limited'
  | 'processing_error'
  | 'validation_error';

export interface FederationError {
  type: FederationErrorType;
  message: string;
  details?: Record<string, unknown>;
  retryable: boolean;
}

// Rate limiting types
export interface RateLimitInfo {
  identifier: string; // IP or actor URI
  windowStart: Date;
  requestCount: number;
  limit: number;
  resetAt: Date;
}

// Validation types
export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: string;
  validator?: (value: unknown) => boolean;
  message?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    value?: unknown;
  }>;
}

// Database mapping types
export interface ActivityRecord {
  id: string;
  uri: string;
  type: string;
  activityPubObject: Activity;
  actorId?: string;
  actorUri: string;
  objectUri?: string;
  targetUri?: string;
  direction: ActivityDirection;
  status: ActivityProcessingStatus;
  published: Date;
  received: Date;
  localPostId?: string;
  localUserId?: string;
  processedAt?: Date;
  errorMessage?: string;
  retryCount: number;
}

export interface FollowRecord {
  id: string;
  followerId?: string;
  followerUri: string;
  followingId?: string;
  followingUri: string;
  status: 'pending' | 'accepted' | 'rejected';
  followActivityUri?: string;
  acceptActivityUri?: string;
  requestedAt: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  isLocal: boolean;
}

export interface NotificationRecord {
  id: string;
  userId: string;
  type: 'follow' | 'like' | 'announce' | 'mention' | 'reply' | 'poll_ended';
  activityId?: string;
  postId?: string;
  fromUserId?: string;
  fromUserUri?: string;
  isRead: boolean;
  createdAt: Date;
  readAt?: Date;
  data?: Record<string, unknown>;
}

// Utility types for inbox/outbox handlers
export type ActivityHandler<T extends Activity = Activity> = (
  context: ActivityProcessingContext & { activity: T }
) => Promise<ActivityProcessingResult>;

export type ActivityHandlerMap = Record<string, ActivityHandler>;

// Collection processing types
export interface CollectionPage {
  items: CoreObject[];
  next?: string;
  prev?: string;
  totalItems?: number;
}

export interface PaginationOptions {
  page?: number;
  limit?: number;
  since?: Date;
  until?: Date;
}
