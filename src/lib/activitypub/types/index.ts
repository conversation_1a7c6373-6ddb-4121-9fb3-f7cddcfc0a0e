import type { Article, Audio, Image, Note, Page, Question, Video, Event } from '$lib/activitypub/types/Extended';
import type { AnyType, OrArray } from '$lib/activitypub/types/util';
import type { BaseEntity } from '$lib/activitypub/types/Core/Entity';

export * from './Core';
export * from './Extended';
export * from './util';
export * from './federation';
export * from './webfinger';
export * from './config';

// Explicitly re-export to resolve ambiguity
export type { CollectionPage, OrderedCollectionPage } from './Extended/Collection';

export type Notelike = Note | Question | Article | Page | Image | Audio | Video | Event;

export function narrowByType<T extends BaseEntity<AnyType>>(obj: BaseEntity<AnyType>, types: OrArray<T['type']>): obj is T {
  let type = obj.type;
  if (Array.isArray(type)) {
    type = type[0];
  }
  if (Array.isArray(types)) {
    return types.includes(type);
  }
  return type === types;
}
