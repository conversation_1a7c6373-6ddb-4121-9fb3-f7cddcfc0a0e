/**
 * Configuration types for ActivityPub federation.
 */

// Server configuration
export interface FederationConfig {
  // Server identity
  domain: string;
  baseUrl: string;
  
  // Rate limiting
  rateLimits: {
    inbox: {
      windowMs: number;
      maxRequests: number;
    };
    outbox: {
      windowMs: number;
      maxRequests: number;
    };
    discovery: {
      windowMs: number;
      maxRequests: number;
    };
  };
  
  // HTTP Signature settings
  httpSignature: {
    algorithm: string;
    headers: string[];
    clockSkewTolerance: number; // seconds
  };
  
  // Delivery settings
  delivery: {
    maxRetries: number;
    retryDelays: number[]; // milliseconds
    timeout: number; // milliseconds
    concurrency: number;
  };
  
  // Processing settings
  processing: {
    maxConcurrentActivities: number;
    activityTimeout: number; // milliseconds
    deduplicationWindow: number; // milliseconds
  };
  
  // Security settings
  security: {
    requireHttpSignature: boolean;
    allowedActorTypes: string[];
    blockedDomains: string[];
    allowedDomains?: string[]; // if set, only these domains are allowed
    maxActivitySize: number; // bytes
    maxAttachmentSize: number; // bytes
  };
  
  // Feature flags
  features: {
    enableInbox: boolean;
    enableOutbox: boolean;
    enableWebfinger: boolean;
    enableNodeinfo: boolean;
    enableSharedInbox: boolean;
  };
}

// Environment-specific overrides
export interface EnvironmentConfig {
  development?: Partial<FederationConfig>;
  production?: Partial<FederationConfig>;
  test?: Partial<FederationConfig>;
}

// Runtime configuration with validation
export interface ValidatedFederationConfig extends FederationConfig {
  _validated: true;
  _environment: 'development' | 'production' | 'test';
}

// Configuration validation result
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// Metrics configuration
export interface MetricsConfig {
  enabled: boolean;
  collectInterval: number; // milliseconds
  retentionPeriod: number; // milliseconds
  metrics: {
    activities: boolean;
    delivery: boolean;
    errors: boolean;
    performance: boolean;
  };
}

// Logging configuration
export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  destinations: Array<{
    type: 'console' | 'file' | 'http';
    config: Record<string, unknown>;
  }>;
  sensitiveFields: string[]; // fields to redact in logs
}

// Complete application configuration
export interface AppConfig {
  federation: FederationConfig;
  metrics?: MetricsConfig;
  logging?: LoggingConfig;
}
