/**
 * Utilities for determining ActivityPub post visibility
 */

import type { Notelike, EntityReference, OrArray } from '$lib/activitypub/types';
import { extractAllAddresses, isPublicCollection, isFollowersCollection } from './addresses';

export type PostVisibility = 'public' | 'unlisted' | 'followers' | 'direct';

/**
 * Determine the visibility of a post based on its to/cc fields
 */
export function getVisibilityFromPost(post: Notelike): PostVisibility {
  const { toAddresses, ccAddresses } = extractAllAddresses(post.to, post.cc);

  const isPublicInTo = toAddresses.some(isPublicCollection);
  const isPublicInCc = ccAddresses.some(isPublicCollection);

  // Public: Public collection in 'to' field
  if (isPublicInTo) {
    return 'public';
  }

  // Unlisted: Public collection in 'cc' field but not in 'to'
  if (isPublicInCc) {
    return 'unlisted';
  }

  // Check for followers collection in 'to' field
  const hasFollowersInTo = toAddresses.some(isFollowersCollection);

  if (hasFollowersInTo) {
    return 'followers';
  }

  // Direct: Not addressed to Public or followers collection
  return 'direct';
}

/**
 * Check if a post is public (visible to everyone)
 */
export function isPublicPost(post: Notelike): boolean {
  return getVisibilityFromPost(post) === 'public';
}

/**
 * Check if a post is unlisted (public but not in timelines)
 */
export function isUnlistedPost(post: Notelike): boolean {
  return getVisibilityFromPost(post) === 'unlisted';
}

/**
 * Check if a post is followers-only
 */
export function isFollowersOnlyPost(post: Notelike): boolean {
  return getVisibilityFromPost(post) === 'followers';
}

/**
 * Check if a post is direct (private message)
 */
export function isDirectPost(post: Notelike): boolean {
  return getVisibilityFromPost(post) === 'direct';
}

/**
 * Check if a post is publicly discoverable (public or unlisted)
 */
export function isPubliclyDiscoverable(post: Notelike): boolean {
  const visibility = getVisibilityFromPost(post);
  return visibility === 'public' || visibility === 'unlisted';
}

/**
 * Get all recipients of a post
 */
export function getPostRecipients(post: Notelike): {
  directRecipients: string[];
  publicRecipients: string[];
  followerCollections: string[];
  allRecipients: string[];
} {
  const { toAddresses, ccAddresses, allAddresses } = extractAllAddresses(post.to, post.cc);

  const publicRecipients = allAddresses.filter(isPublicCollection);
  const followerCollections = allAddresses.filter(isFollowersCollection);
  const directRecipients = allAddresses.filter(
    addr => !isPublicCollection(addr) && !isFollowersCollection(addr)
  );

  return {
    directRecipients,
    publicRecipients,
    followerCollections,
    allRecipients: allAddresses
  };
}

/**
 * Visibility level ordering (for comparison)
 */
export const VisibilityLevels: Record<PostVisibility, number> = {
  'public': 4,
  'unlisted': 3,
  'followers': 2,
  'direct': 1
} as const;

/**
 * Compare visibility levels
 */
export function compareVisibility(a: PostVisibility, b: PostVisibility): number {
  return VisibilityLevels[a] - VisibilityLevels[b];
}

/**
 * Check if visibility level is at least as open as the minimum level
 */
export function isVisibilityAtLeast(
  visibility: PostVisibility, 
  minimumLevel: PostVisibility
): boolean {
  return VisibilityLevels[visibility] >= VisibilityLevels[minimumLevel];
}
