/**
 * Common fetch utilities for ActivityPub objects
 */

import { rateLimitedFetch } from '$lib/activitypub/rateLimiter';
import { processAPObject } from '$lib/activitypub/utils/object';
import { ACTIVITYPUB_HEADERS } from './constants';
import { fetchWithServiceSignature } from './http-client';
import type { CoreObject, Entity } from '$lib/activitypub/types';
import {
  retryActivityPubFetch,
  retryBatch,
  ActivityPubRetryConfigs,
  type RetryStrategy,
  type RetryResult
} from './retry';

export interface FetchResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

/**
 * Safely fetch and process an ActivityPub object
 */
export async function fetchAndProcessAPObject<T extends CoreObject | Entity = Entity>(
  url: string,
  options: {
    timeout?: number;
    headers?: Record<string, string>;
    processObject?: boolean;
  } = {}
): Promise<FetchResult<T>> {
  const {
    timeout = 10000,
    headers = {},
    processObject = true
  } = options;

  try {
    const response = await fetchWithServiceSignature(url, {
      headers: {
        ...ACTIVITYPUB_HEADERS,
        ...headers
      },
      signal: AbortSignal.timeout(timeout)
    });

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        statusCode: response.status
      };
    }

    let data = await response.json() as T;
    
    if (processObject) {
      data = processAPObject(data);
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Fetch multiple ActivityPub objects in parallel with error handling
 */
export async function fetchMultipleAPObjects<T extends CoreObject | Entity = Entity>(
  urls: string[],
  options: {
    timeout?: number;
    maxConcurrent?: number;
    continueOnError?: boolean;
  } = {}
): Promise<Array<FetchResult<T>>> {
  const {
    timeout = 10000,
    maxConcurrent = 5,
    continueOnError = true
  } = options;

  const results: Array<FetchResult<T>> = [];
  
  // Process URLs in batches to avoid overwhelming the server
  for (let i = 0; i < urls.length; i += maxConcurrent) {
    const batch = urls.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(url => 
      fetchAndProcessAPObject<T>(url, { timeout })
    );

    try {
      const batchResults = await Promise.allSettled(batchPromises);
      
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            error: result.reason instanceof Error ? result.reason.message : 'Promise rejected'
          });
        }
      }
    } catch (error) {
      if (!continueOnError) {
        throw error;
      }
      
      // Add error results for the entire batch
      for (let j = 0; j < batch.length; j++) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Batch processing failed'
        });
      }
    }
  }

  return results;
}

// URL validation moved to utils/links.ts - import from there instead

/**
 * Enhanced fetch with retry support for ActivityPub objects
 */
export async function fetchAPObjectWithRetry<T extends CoreObject | Entity = Entity>(
  url: string,
  options: {
    timeout?: number;
    retryStrategy?: RetryStrategy;
    headers?: Record<string, string>;
  } = {}
): Promise<RetryResult<T>> {
  const { timeout = 30000, retryStrategy = ActivityPubRetryConfigs.postFetch, headers = {} } = options;

  return retryActivityPubFetch(
    url,
    async (response) => {
      const text = await response.text();
      let data: any;

      try {
        data = JSON.parse(text);
      } catch (error) {
        throw new Error(`Invalid JSON response from ${url}: ${error}`);
      }

      return processAPObject(data) as T;
    },
    {
      headers: {
        ...ACTIVITYPUB_HEADERS,
        ...headers
      },
      signal: AbortSignal.timeout(timeout)
    },
    retryStrategy
  );
}

/**
 * Batch fetch multiple ActivityPub objects with retry support
 */
export async function fetchMultipleAPObjectsWithRetry<T extends CoreObject | Entity = Entity>(
  urls: string[],
  options: {
    maxConcurrency?: number;
    timeout?: number;
    retryStrategy?: RetryStrategy;
    continueOnError?: boolean;
    onProgress?: (completed: number, total: number) => void;
  } = {}
): Promise<{
  results: Array<RetryResult<T>>;
  successful: number;
  failed: number;
  totalTime: number;
}> {
  const {
    maxConcurrency = 5,
    timeout = 30000,
    retryStrategy = ActivityPubRetryConfigs.postFetch,
    continueOnError = true,
    onProgress
  } = options;

  const operations = urls.map(url => () =>
    fetchAPObjectWithRetry<T>(url, { timeout, retryStrategy }).then(result => {
      if (!result.success) {
        throw result.error || new Error('Fetch failed');
      }
      return result.result!;
    })
  );

  return retryBatch(operations, retryStrategy, {
    maxConcurrency,
    continueOnError,
    onProgress
  });
}

/**
 * Specialized retry fetch for different ActivityPub object types
 */
export const RetryFetchers = {
  /**
   * Fetch actor with conservative retry strategy
   */
  async actor(url: string, timeout = 15000): Promise<RetryResult<Entity>> {
    return fetchAPObjectWithRetry(url, {
      timeout,
      retryStrategy: ActivityPubRetryConfigs.actorFetch
    });
  },

  /**
   * Fetch post with aggressive retry strategy
   */
  async post(url: string, timeout = 30000): Promise<RetryResult<Entity>> {
    return fetchAPObjectWithRetry(url, {
      timeout,
      retryStrategy: ActivityPubRetryConfigs.postFetch
    });
  },

  /**
   * Fetch collection with patient retry strategy
   */
  async collection(url: string, timeout = 60000): Promise<RetryResult<Entity>> {
    return fetchAPObjectWithRetry(url, {
      timeout,
      retryStrategy: ActivityPubRetryConfigs.collectionFetch
    });
  },

  /**
   * WebFinger lookup with fast retry strategy
   */
  async webfinger(url: string, timeout = 5000): Promise<RetryResult<any>> {
    return retryActivityPubFetch(
      url,
      async (response) => response.json(),
      {
        headers: {
          'Accept': 'application/jrd+json, application/json'
        },
        signal: AbortSignal.timeout(timeout)
      },
      ActivityPubRetryConfigs.webfingerLookup
    );
  }
} as const;
