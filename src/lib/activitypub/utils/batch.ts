/**
 * Batch processing utilities for ActivityPub operations
 */

import { retryBatch, type RetryStrategy, RetryStrategies } from './retry';
import { fetchAPObjectWithRetry, fetchMultipleAPObjectsWithRetry } from './fetch';
import { RequestPriority } from '../rateLimiter';
import type { CoreObject, Entity } from '$lib/activitypub/types';

/**
 * Batch operation configuration
 */
export interface BatchConfig {
  maxConcurrency: number;
  batchSize: number;
  delayBetweenBatches: number;
  continueOnError: boolean;
  retryStrategy: RetryStrategy;
  priority: RequestPriority;
  timeout: number;
}

/**
 * Batch operation result
 */
export interface BatchResult<T> {
  successful: T[];
  failed: Array<{
    input: any;
    error: Error;
    index: number;
  }>;
  totalProcessed: number;
  totalTime: number;
  successRate: number;
}

/**
 * Batch progress callback
 */
export type BatchProgressCallback = (
  completed: number,
  total: number,
  successful: number,
  failed: number
) => void;

/**
 * Predefined batch configurations
 */
export const BatchConfigs = {
  /**
   * Conservative batch processing for critical operations
   */
  conservative: {
    maxConcurrency: 3,
    batchSize: 10,
    delayBetweenBatches: 1000,
    continueOnError: true,
    retryStrategy: RetryStrategies.conservative,
    priority: RequestPriority.HIGH,
    timeout: 30000
  } as BatchConfig,

  /**
   * Aggressive batch processing for non-critical operations
   */
  aggressive: {
    maxConcurrency: 10,
    batchSize: 50,
    delayBetweenBatches: 200,
    continueOnError: true,
    retryStrategy: RetryStrategies.aggressive,
    priority: RequestPriority.NORMAL,
    timeout: 15000
  } as BatchConfig,

  /**
   * Background batch processing for large datasets
   */
  background: {
    maxConcurrency: 5,
    batchSize: 100,
    delayBetweenBatches: 2000,
    continueOnError: true,
    retryStrategy: RetryStrategies.patient,
    priority: RequestPriority.LOW,
    timeout: 60000
  } as BatchConfig,

  /**
   * Real-time batch processing for user-facing operations
   */
  realtime: {
    maxConcurrency: 8,
    batchSize: 20,
    delayBetweenBatches: 100,
    continueOnError: false,
    retryStrategy: RetryStrategies.fast,
    priority: RequestPriority.CRITICAL,
    timeout: 10000
  } as BatchConfig
} as const;

/**
 * Sleep utility for delays between batches
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generic batch processor
 */
export async function processBatch<TInput, TOutput>(
  items: TInput[],
  processor: (item: TInput, index: number) => Promise<TOutput>,
  config: BatchConfig = BatchConfigs.conservative,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<TOutput>> {
  const startTime = Date.now();
  const successful: TOutput[] = [];
  const failed: Array<{ input: TInput; error: Error; index: number }> = [];

  // Process items in batches
  for (let i = 0; i < items.length; i += config.batchSize) {
    const batch = items.slice(i, i + config.batchSize);
    const batchStartIndex = i;

    // Create operations for this batch
    const operations = batch.map((item, batchIndex) => async () => {
      const globalIndex = batchStartIndex + batchIndex;
      try {
        return await processor(item, globalIndex);
      } catch (error) {
        throw { item, error, index: globalIndex };
      }
    });

    // Process batch with retry logic
    const batchResult = await retryBatch(operations, config.retryStrategy, {
      maxConcurrency: config.maxConcurrency,
      continueOnError: config.continueOnError
    });

    // Process results
    for (let j = 0; j < batchResult.results.length; j++) {
      const result = batchResult.results[j];
      const globalIndex = batchStartIndex + j;

      if (result.success && result.result !== undefined) {
        successful.push(result.result);
      } else {
        const error = result.error || new Error('Unknown batch processing error');
        failed.push({
          input: batch[j],
          error,
          index: globalIndex
        });
      }
    }

    // Call progress callback
    if (onProgress) {
      onProgress(
        successful.length + failed.length,
        items.length,
        successful.length,
        failed.length
      );
    }

    // Stop on first error if not continuing
    if (!config.continueOnError && failed.length > 0) {
      break;
    }

    // Delay between batches (except for the last batch)
    if (i + config.batchSize < items.length && config.delayBetweenBatches > 0) {
      await sleep(config.delayBetweenBatches);
    }
  }

  const totalTime = Date.now() - startTime;
  const totalProcessed = successful.length + failed.length;
  const successRate = totalProcessed > 0 ? successful.length / totalProcessed : 0;

  return {
    successful,
    failed,
    totalProcessed,
    totalTime,
    successRate
  };
}

/**
 * Batch fetch ActivityPub objects
 */
export async function batchFetchAPObjects<T extends CoreObject | Entity = Entity>(
  urls: string[],
  config: BatchConfig = BatchConfigs.conservative,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<T>> {
  return processBatch(
    urls,
    async (url) => {
      const result = await fetchAPObjectWithRetry<T>(url, {
        timeout: config.timeout,
        retryStrategy: config.retryStrategy
      });

      if (!result.success) {
        throw result.error || new Error('Fetch failed');
      }

      return result.result!;
    },
    config,
    onProgress
  );
}

/**
 * Batch process ActivityPub collections
 */
export async function batchProcessCollections(
  collectionUrls: string[],
  itemProcessor: (item: Entity) => Promise<void>,
  config: BatchConfig = BatchConfigs.background,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<void>> {
  return processBatch(
    collectionUrls,
    async (collectionUrl) => {
      // Fetch collection
      const collectionResult = await fetchAPObjectWithRetry(collectionUrl, {
        timeout: config.timeout,
        retryStrategy: config.retryStrategy
      });

      if (!collectionResult.success) {
        throw collectionResult.error || new Error('Failed to fetch collection');
      }

      const collection = collectionResult.result!;

      // Process collection items
      if ('items' in collection && Array.isArray(collection.items)) {
        for (const item of collection.items) {
          if (typeof item === 'object' && item !== null) {
            await itemProcessor(item as Entity);
          }
        }
      }
    },
    config,
    onProgress
  );
}

/**
 * Batch import actors
 */
export async function batchImportActors(
  actorUrls: string[],
  importFunction: (actor: Entity) => Promise<void>,
  config: BatchConfig = BatchConfigs.conservative,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<void>> {
  return processBatch(
    actorUrls,
    async (url) => {
      const result = await fetchAPObjectWithRetry(url, {
        timeout: config.timeout,
        retryStrategy: config.retryStrategy
      });

      if (!result.success) {
        throw result.error || new Error('Failed to fetch actor');
      }

      await importFunction(result.result!);
    },
    config,
    onProgress
  );
}

/**
 * Batch import posts
 */
export async function batchImportPosts(
  postUrls: string[],
  importFunction: (post: Entity) => Promise<void>,
  config: BatchConfig = BatchConfigs.aggressive,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<void>> {
  return processBatch(
    postUrls,
    async (url) => {
      const result = await fetchAPObjectWithRetry(url, {
        timeout: config.timeout,
        retryStrategy: config.retryStrategy
      });

      if (!result.success) {
        throw result.error || new Error('Failed to fetch post');
      }

      await importFunction(result.result!);
    },
    config,
    onProgress
  );
}

/**
 * Batch operation with custom error handling
 */
export async function batchWithErrorHandling<TInput, TOutput>(
  items: TInput[],
  processor: (item: TInput, index: number) => Promise<TOutput>,
  errorHandler: (error: Error, item: TInput, index: number) => Promise<TOutput | null>,
  config: BatchConfig = BatchConfigs.conservative,
  onProgress?: BatchProgressCallback
): Promise<BatchResult<TOutput>> {
  return processBatch(
    items,
    async (item, index) => {
      try {
        return await processor(item, index);
      } catch (error) {
        const handled = await errorHandler(error as Error, item, index);
        if (handled !== null) {
          return handled;
        }
        throw error;
      }
    },
    config,
    onProgress
  );
}

/**
 * Batch operation metrics
 */
export interface BatchMetrics {
  totalBatches: number;
  averageBatchTime: number;
  averageSuccessRate: number;
  totalItemsProcessed: number;
  totalErrors: number;
}

/**
 * Batch metrics collector
 */
export class BatchMetricsCollector {
  private metrics: BatchMetrics = {
    totalBatches: 0,
    averageBatchTime: 0,
    averageSuccessRate: 0,
    totalItemsProcessed: 0,
    totalErrors: 0
  };

  recordBatch<T>(result: BatchResult<T>): void {
    this.metrics.totalBatches++;
    this.metrics.totalItemsProcessed += result.totalProcessed;
    this.metrics.totalErrors += result.failed.length;

    // Update averages
    const currentAvgTime = this.metrics.averageBatchTime;
    this.metrics.averageBatchTime = (currentAvgTime + result.totalTime) / 2;

    const currentAvgSuccess = this.metrics.averageSuccessRate;
    this.metrics.averageSuccessRate = (currentAvgSuccess + result.successRate) / 2;
  }

  getMetrics(): BatchMetrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      totalBatches: 0,
      averageBatchTime: 0,
      averageSuccessRate: 0,
      totalItemsProcessed: 0,
      totalErrors: 0
    };
  }
}

/**
 * Global batch metrics instance
 */
export const globalBatchMetrics = new BatchMetricsCollector();
