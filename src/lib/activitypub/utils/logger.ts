/**
 * Structured logging system for ActivityPub operations
 */

/**
 * Log levels
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4
}

/**
 * Log context for ActivityPub operations
 */
export interface LogContext {
  operation: string;
  actorId?: string;
  objectId?: string;
  url?: string;
  hostname?: string;
  userId?: string;
  requestId?: string;
  duration?: number;
  status?: number;
  error?: string;
  method?: string;
  keyId?: string;
  metadata?: Record<string, any>;
}

/**
 * Structured log entry
 */
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  correlationId?: string;
}

/**
 * Log output interface
 */
export interface LogOutput {
  write(entry: LogEntry): Promise<void>;
}

/**
 * Console log output
 */
export class ConsoleLogOutput implements LogOutput {
  async write(entry: LogEntry): Promise<void> {
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp;
    const context = JSON.stringify(entry.context, null, 2);

    const logMessage = `[${timestamp}] ${levelName}: ${entry.message}\nContext: ${context}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage);
        break;
      case LogLevel.INFO:
        console.info(logMessage);
        break;
      case LogLevel.WARN:
        console.warn(logMessage);
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        console.error(logMessage);
        break;
    }
  }
}

/**
 * ActivityPub Logger
 */
export class ActivityPubLogger {
  private readonly outputs: LogOutput[] = [];
  private minLevel: LogLevel = LogLevel.INFO;
  private correlationId?: string;

  constructor(outputs: LogOutput[] = [new ConsoleLogOutput()]) {
    this.outputs = outputs;
  }

  setMinLevel(level: LogLevel): void {
    this.minLevel = level;
  }

  setCorrelationId(id: string): void {
    this.correlationId = id;
  }

  addOutput(output: LogOutput): void {
    this.outputs.push(output);
  }

  private async log(level: LogLevel, message: string, context: LogContext): Promise<void> {
    if (level < this.minLevel) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      correlationId: this.correlationId
    };

    // Write to all outputs
    await Promise.all(
      this.outputs.map(output =>
        output.write(entry).catch(error =>
          console.error('Failed to write log entry:', error)
        )
      )
    );
  }

  debug(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.DEBUG, message, context);
  }

  info(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.INFO, message, context);
  }

  warn(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.WARN, message, context);
  }

  error(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.ERROR, message, context);
  }

  critical(message: string, context: LogContext): Promise<void> {
    return this.log(LogLevel.CRITICAL, message, context);
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: Partial<LogContext>): ActivityPubLogger {
    const childLogger = new ActivityPubLogger(this.outputs);
    childLogger.setMinLevel(this.minLevel);
    childLogger.setCorrelationId(this.correlationId || '');

    // Override log method to merge contexts
    const originalLog = childLogger.log.bind(childLogger);
    childLogger.log = async (level: LogLevel, message: string, context: LogContext) => {
      const mergedContext = { ...additionalContext, ...context };
      return originalLog(level, message, mergedContext);
    };

    return childLogger;
  }
}

/**
 * Global ActivityPub logger instance
 */
export const logger = new ActivityPubLogger();

/**
 * Logging utilities for common ActivityPub operations
 */
export const ActivityPubLogs = {
  /**
   * Log actor operations
   */
  actor: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Actor fetch started', {
        operation: 'actor.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, actorId: string, duration: number, requestId?: string) =>
      logger.info('Actor fetch completed', {
        operation: 'actor.fetch',
        url,
        actorId,
        duration,
        requestId
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Actor fetch failed', {
        operation: 'actor.fetch',
        url,
        error,
        duration,
        requestId
      }),

    imported: (actorId: string, url: string) =>
      logger.info('Actor imported', {
        operation: 'actor.import',
        actorId,
        url
      }),

    importFailed: (url: string, error: string) =>
      logger.error('Actor import failed', {
        operation: 'actor.import',
        url,
        error
      })
  },

  /**
   * Log post operations
   */
  post: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Post fetch started', {
        operation: 'post.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, objectId: string, duration: number, requestId?: string) =>
      logger.info('Post fetch completed', {
        operation: 'post.fetch',
        url,
        objectId,
        duration,
        requestId
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Post fetch failed', {
        operation: 'post.fetch',
        url,
        error,
        duration,
        requestId
      }),

    imported: (objectId: string, url: string, actorId?: string) =>
      logger.info('Post imported', {
        operation: 'post.import',
        objectId,
        url,
        actorId
      }),

    importFailed: (url: string, error: string, actorId?: string) =>
      logger.error('Post import failed', {
        operation: 'post.import',
        url,
        error,
        actorId
      })
  },

  /**
   * Log activity operations
   */
  activity: {
    received: (activityType: string, actorId: string, objectId?: string) =>
      logger.info('Activity received', {
        operation: 'activity.receive',
        metadata: { activityType },
        actorId,
        objectId
      }),

    processed: (activityType: string, actorId: string, objectId?: string, duration?: number) =>
      logger.info('Activity processed', {
        operation: 'activity.process',
        metadata: { activityType },
        actorId,
        objectId,
        duration
      }),

    processingFailed: (activityType: string, actorId: string, error: string, objectId?: string) =>
      logger.error('Activity processing failed', {
        operation: 'activity.process',
        metadata: { activityType },
        actorId,
        objectId,
        error
      }),

    validated: (activityType: string, actorId: string) =>
      logger.debug('Activity validated', {
        operation: 'activity.validate',
        metadata: { activityType },
        actorId
      }),

    validationFailed: (activityType: string, actorId: string, error: string) =>
      logger.warn('Activity validation failed', {
        operation: 'activity.validate',
        metadata: { activityType },
        actorId,
        error
      })
  },

  /**
   * Log collection operations
   */
  collection: {
    fetchStarted: (url: string, requestId?: string) =>
      logger.info('Collection fetch started', {
        operation: 'collection.fetch',
        url,
        requestId
      }),

    fetchCompleted: (url: string, itemCount: number, duration: number, requestId?: string) =>
      logger.info('Collection fetch completed', {
        operation: 'collection.fetch',
        url,
        duration,
        requestId,
        metadata: { itemCount }
      }),

    fetchFailed: (url: string, error: string, duration: number, requestId?: string) =>
      logger.error('Collection fetch failed', {
        operation: 'collection.fetch',
        url,
        error,
        duration,
        requestId
      }),

    processed: (url: string, processedCount: number, failedCount: number, duration: number) =>
      logger.info('Collection processed', {
        operation: 'collection.process',
        url,
        duration,
        metadata: { processedCount, failedCount }
      })
  },

  /**
   * Log rate limiting events
   */
  rateLimit: {
    hit: (hostname: string, retryAfter?: number) =>
      logger.warn('Rate limit hit', {
        operation: 'rateLimit.hit',
        hostname,
        metadata: { retryAfter }
      }),

    queuedRequest: (hostname: string, queueLength: number) =>
      logger.debug('Request queued due to rate limit', {
        operation: 'rateLimit.queue',
        hostname,
        metadata: { queueLength }
      }),

    requestCompleted: (hostname: string, waitTime: number, status: number) =>
      logger.debug('Rate limited request completed', {
        operation: 'rateLimit.complete',
        hostname,
        duration: waitTime,
        status
      })
  },

  /**
   * Log batch operations
   */
  batch: {
    started: (operation: string, itemCount: number, batchSize: number) =>
      logger.info('Batch operation started', {
        operation: `batch.${operation}`,
        metadata: { itemCount, batchSize }
      }),

    completed: (operation: string, successful: number, failed: number, duration: number) =>
      logger.info('Batch operation completed', {
        operation: `batch.${operation}`,
        duration,
        metadata: { successful, failed }
      }),

    batchProcessed: (operation: string, batchIndex: number, successful: number, failed: number) =>
      logger.debug('Batch processed', {
        operation: `batch.${operation}`,
        metadata: { batchIndex, successful, failed }
      })
  },

  /**
   * Log security events
   */
  security: {
    signatureVerified: (actorId: string, url: string) =>
      logger.info('HTTP signature verified', {
        operation: 'security.signature.verify',
        actorId,
        url
      }),

    signatureVerificationFailed: (actorId: string, url: string, error: string) =>
      logger.warn('HTTP signature verification failed', {
        operation: 'security.signature.verify',
        actorId,
        url,
        error
      }),

    signatureValidationFailed: (actorId: string, url: string, error: string) =>
      logger.warn('HTTP signature validation failed', {
        operation: 'security.signature.validate',
        actorId,
        url,
        error
      }),

    suspiciousActivity: (actorId: string, reason: string, metadata?: Record<string, any>) =>
      logger.warn('Suspicious activity detected', {
        operation: 'security.suspicious',
        actorId,
        error: reason,
        metadata
      }),

    blockedRequest: (url: string, reason: string) =>
      logger.warn('Request blocked', {
        operation: 'security.block',
        url,
        error: reason
      }),

    // Key management logging
    keyGenerationStarted: (actorUrl: string) =>
      logger.info('Key generation started', {
        operation: 'security.key.generate.start',
        url: actorUrl
      }),

    keyGenerationCompleted: (actorUrl: string, keyId: string) =>
      logger.info('Key generation completed', {
        operation: 'security.key.generate.complete',
        url: actorUrl,
        keyId
      }),

    keyGenerationFailed: (actorUrl: string, error: string) =>
      logger.error('Key generation failed', {
        operation: 'security.key.generate.failed',
        url: actorUrl,
        error
      }),

    keyStored: (userId: string, keyId: string) =>
      logger.info('Actor keys stored', {
        operation: 'security.key.store',
        userId,
        keyId
      }),

    keyRotationStarted: (userId: string, actorUrl: string) =>
      logger.info('Key rotation started', {
        operation: 'security.key.rotate.start',
        userId,
        url: actorUrl
      }),

    keyRotationCompleted: (userId: string, actorUrl: string, keyId: string) =>
      logger.info('Key rotation completed', {
        operation: 'security.key.rotate.complete',
        userId,
        url: actorUrl,
        keyId
      }),

    keyRotationFailed: (userId: string, actorUrl: string, error: string) =>
      logger.error('Key rotation failed', {
        operation: 'security.key.rotate.failed',
        userId,
        url: actorUrl,
        error
      }),

    keyInitialization: (userId: string, actorUrl: string) =>
      logger.info('Key initialization for new actor', {
        operation: 'security.key.initialize',
        userId,
        url: actorUrl
      }),

    // Key cache logging
    keyRetrieved: (keyId: string, source: string) =>
      logger.debug('Public key retrieved', {
        operation: 'security.key.retrieve',
        keyId,
        metadata: { source }
      }),

    keyRetrievalFailed: (keyId: string, error: string) =>
      logger.warn('Public key retrieval failed', {
        operation: 'security.key.retrieve.failed',
        keyId,
        error
      }),

    keyFetchStarted: (keyId: string, actorUrl: string) =>
      logger.debug('Public key fetch started', {
        operation: 'security.key.fetch.start',
        keyId,
        url: actorUrl
      }),

    keyFetchCompleted: (keyId: string, actorUrl: string) =>
      logger.info('Public key fetch completed', {
        operation: 'security.key.fetch.complete',
        keyId,
        url: actorUrl
      }),

    keyFetchFailed: (keyId: string, error: string) =>
      logger.warn('Public key fetch failed', {
        operation: 'security.key.fetch.failed',
        keyId,
        error
      }),

    keyRefreshFailed: (keyId: string, error: string) =>
      logger.warn('Public key refresh failed', {
        operation: 'security.key.refresh.failed',
        keyId,
        error
      }),

    keyInvalidated: (keyId: string) =>
      logger.info('Public key invalidated', {
        operation: 'security.key.invalidate',
        keyId
      }),

    keyCacheCleared: () =>
      logger.info('Public key cache cleared', {
        operation: 'security.key.cache.clear'
      }),

    // Emergency key rotation
    emergencyKeyRotation: (userId: string) =>
      logger.warn('Emergency key rotation initiated', {
        operation: 'security.key.emergency.start',
        userId
      }),

    emergencyKeyRotationCompleted: (userId: string, keyId: string) =>
      logger.info('Emergency key rotation completed', {
        operation: 'security.key.emergency.complete',
        userId,
        keyId
      }),

    emergencyKeyRotationFailed: (userId: string, error: string) =>
      logger.error('Emergency key rotation failed', {
        operation: 'security.key.emergency.failed',
        userId,
        error
      })
  },

  /**
   * Log federation events
   */
  federation: {
    incomingRequest: (method: string, url: string) =>
      logger.info('Incoming federation request', {
        operation: 'federation.incoming.request',
        method,
        url
      }),

    incomingResponse: (method: string, url: string, status: number) =>
      logger.info('Incoming federation response', {
        operation: 'federation.incoming.response',
        method,
        url,
        status
      }),

    incomingError: (method: string, url: string, error: string) =>
      logger.error('Incoming federation error', {
        operation: 'federation.incoming.error',
        method,
        url,
        error
      }),

    outgoingRequest: (method: string, url: string) =>
      logger.info('Outgoing federation request', {
        operation: 'federation.outgoing.request',
        method,
        url
      }),

    outgoingResponse: (method: string, url: string, status: number) =>
      logger.info('Outgoing federation response', {
        operation: 'federation.outgoing.response',
        method,
        url,
        status
      }),

    outgoingError: (method: string, url: string, error: string) =>
      logger.error('Outgoing federation error', {
        operation: 'federation.outgoing.error',
        method,
        url,
        error
      }),

    signatureGenerated: (keyId: string, url: string) =>
      logger.debug('HTTP signature generated', {
        operation: 'federation.signature.generate',
        keyId,
        url
      }),

    // Delivery logging
    deliveryScheduled: (activityId: string, recipientInbox: string, priority: number) =>
      logger.info('Activity delivery scheduled', {
        operation: 'federation.delivery.scheduled',
        objectId: activityId,
        url: recipientInbox,
        metadata: { priority }
      }),

    deliverySchedulingFailed: (activityId: string, recipientInbox: string, error: string) =>
      logger.error('Activity delivery scheduling failed', {
        operation: 'federation.delivery.scheduling_failed',
        objectId: activityId,
        url: recipientInbox,
        error
      }),

    deliveryAttemptStarted: (deliveryId: string, recipientInbox: string, attempt: number) =>
      logger.debug('Delivery attempt started', {
        operation: 'federation.delivery.attempt_started',
        objectId: deliveryId,
        url: recipientInbox,
        metadata: { attempt }
      }),

    deliverySucceeded: (deliveryId: string, recipientInbox: string, duration: number) =>
      logger.info('Activity delivery succeeded', {
        operation: 'federation.delivery.succeeded',
        objectId: deliveryId,
        url: recipientInbox,
        duration
      }),

    deliveryFailed: (deliveryId: string, recipientInbox: string, error: string, attempt: number) =>
      logger.warn('Activity delivery failed', {
        operation: 'federation.delivery.failed',
        objectId: deliveryId,
        url: recipientInbox,
        error,
        metadata: { attempt }
      }),

    deliveryBatchStarted: (batchSize: number, concurrency: number) =>
      logger.info('Delivery batch processing started', {
        operation: 'federation.delivery.batch_started',
        metadata: { batchSize, concurrency }
      }),

    deliveryBatchCompleted: (processed: number, delivered: number, failed: number, permanentFailures: number) =>
      logger.info('Delivery batch processing completed', {
        operation: 'federation.delivery.batch_completed',
        metadata: { processed, delivered, failed, permanentFailures }
      }),

    deliveryBatchFailed: (error: string) =>
      logger.error('Delivery batch processing failed', {
        operation: 'federation.delivery.batch_failed',
        error
      }),

    deliveryCleanupCompleted: (deletedCount: number, olderThanDays: number) =>
      logger.info('Delivery cleanup completed', {
        operation: 'federation.delivery.cleanup_completed',
        metadata: { deletedCount, olderThanDays }
      }),

    // Scheduler logging
    schedulerStarted: (config: any) =>
      logger.info('Delivery scheduler started', {
        operation: 'federation.scheduler.started',
        metadata: config
      }),

    schedulerStopped: () =>
      logger.info('Delivery scheduler stopped', {
        operation: 'federation.scheduler.stopped'
      }),

    schedulerAlreadyRunning: () =>
      logger.warn('Delivery scheduler already running', {
        operation: 'federation.scheduler.already_running'
      }),

    schedulerDisabled: () =>
      logger.info('Delivery scheduler disabled', {
        operation: 'federation.scheduler.disabled'
      }),

    schedulerConfigUpdated: (config: any) =>
      logger.info('Delivery scheduler configuration updated', {
        operation: 'federation.scheduler.config_updated',
        metadata: config
      }),

    schedulerProcessedBatch: (processed: number, delivered: number, failed: number, permanentFailures: number) =>
      logger.debug('Scheduler processed delivery batch', {
        operation: 'federation.scheduler.batch_processed',
        metadata: { processed, delivered, failed, permanentFailures }
      }),

    schedulerBatchErrors: (errors: string[]) =>
      logger.warn('Scheduler batch had errors', {
        operation: 'federation.scheduler.batch_errors',
        metadata: { errors, errorCount: errors.length }
      }),

    schedulerProcessingError: (error: string) =>
      logger.error('Scheduler processing error', {
        operation: 'federation.scheduler.processing_error',
        error
      }),

    schedulerCleanupCompleted: (deletedCount: number, olderThanDays: number) =>
      logger.info('Scheduler cleanup completed', {
        operation: 'federation.scheduler.cleanup_completed',
        metadata: { deletedCount, olderThanDays }
      }),

    schedulerCleanupError: (error: string) =>
      logger.error('Scheduler cleanup error', {
        operation: 'federation.scheduler.cleanup_error',
        error
      }),

    schedulerStats: (stats: any) =>
      logger.debug('Scheduler delivery statistics', {
        operation: 'federation.scheduler.stats',
        metadata: stats
      }),

    schedulerStatsError: (error: string) =>
      logger.error('Scheduler stats error', {
        operation: 'federation.scheduler.stats_error',
        error
      }),

    // Deduplication and idempotency logging
    activityDuplicate: (activityUri: string, activityType: string, existingStatus: string) =>
      logger.info('Duplicate activity detected', {
        operation: 'federation.deduplication.duplicate_detected',
        objectId: activityUri,
        metadata: { activityType, existingStatus }
      }),

    deduplicationError: (activityUri: string, error: string) =>
      logger.error('Deduplication check failed', {
        operation: 'federation.deduplication.check_failed',
        objectId: activityUri,
        error
      }),

    processingRecordCreated: (processingId: string, activityUri: string, activityType: string) =>
      logger.debug('Processing record created', {
        operation: 'federation.processing.record_created',
        objectId: processingId,
        url: activityUri,
        metadata: { activityType }
      }),

    processingStatusUpdated: (processingId: string, status: string, errorMessage?: string) =>
      logger.debug('Processing status updated', {
        operation: 'federation.processing.status_updated',
        objectId: processingId,
        metadata: { status, errorMessage }
      }),

    processingCleanupCompleted: (deletedCount: number, olderThanHours: number) =>
      logger.info('Processing records cleanup completed', {
        operation: 'federation.processing.cleanup_completed',
        metadata: { deletedCount, olderThanHours }
      }),

    idempotentOperationSkipped: (activityUri: string, activityType: string, existingStatus: string) =>
      logger.info('Idempotent operation skipped', {
        operation: 'federation.idempotency.operation_skipped',
        objectId: activityUri,
        metadata: { activityType, existingStatus }
      }),

    idempotentOperationStarted: (activityUri: string, activityType: string, processingId: string) =>
      logger.debug('Idempotent operation started', {
        operation: 'federation.idempotency.operation_started',
        objectId: activityUri,
        metadata: { activityType, processingId }
      }),

    idempotentOperationCompleted: (activityUri: string, activityType: string, processingId: string) =>
      logger.info('Idempotent operation completed', {
        operation: 'federation.idempotency.operation_completed',
        objectId: activityUri,
        metadata: { activityType, processingId }
      }),

    idempotentOperationFailed: (activityUri: string, activityType: string, processingId: string, error: string) =>
      logger.error('Idempotent operation failed', {
        operation: 'federation.idempotency.operation_failed',
        objectId: activityUri,
        error,
        metadata: { activityType, processingId }
      }),

    idempotentOperationRetried: (activityUri: string, activityType: string, processingId: string) =>
      logger.info('Idempotent operation retried', {
        operation: 'federation.idempotency.operation_retried',
        objectId: activityUri,
        metadata: { activityType, processingId }
      })
  },

  /**
   * Log outbox events
   */
  outbox: {
    activityCreated: (activityId: string, activityType: string, actor: string) =>
      logger.info('Outbox activity created', {
        operation: 'outbox.activity.created',
        objectId: activityId,
        actorId: actor,
        metadata: { activityType }
      }),

    activityScheduled: (activityId: string, recipientCount: number, deliveryCount: number, duration: number) =>
      logger.info('Outbox activity scheduled for delivery', {
        operation: 'outbox.activity.scheduled',
        objectId: activityId,
        duration,
        metadata: { recipientCount, deliveryCount }
      }),

    activityFailed: (activityType: string, actor: string, error: string, duration: number) =>
      logger.error('Outbox activity failed', {
        operation: 'outbox.activity.failed',
        actorId: actor,
        error,
        duration,
        metadata: { activityType }
      }),

    noRecipientsFound: (activityId: string, activityType: string) =>
      logger.warn('No recipients found for activity', {
        operation: 'outbox.activity.no_recipients',
        objectId: activityId,
        metadata: { activityType }
      }),

    recipientDiscoveryFailed: (recipientUri: string, error: string) =>
      logger.warn('Recipient discovery failed', {
        operation: 'outbox.recipient.discovery_failed',
        url: recipientUri,
        error
      })
  }
} as const;

/**
 * Performance timing utility
 */
export function createTimer() {
  const start = Date.now();
  return {
    end: () => Date.now() - start
  };
}

/**
 * Generate correlation ID for request tracking
 */
export function generateCorrelationId(): string {
  return `ap_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}
