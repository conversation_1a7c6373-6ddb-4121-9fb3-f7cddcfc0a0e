/**
 * Actor key management utilities for ActivityPub federation.
 * Provides high-level functions for managing actor keys, initialization, and rotation.
 */

import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { 
  generateActor<PERSON>eyPair, 
  storeActor<PERSON><PERSON><PERSON>, 
  getActor<PERSON><PERSON><PERSON>, 
  rotateActor<PERSON><PERSON><PERSON>,
  ensureA<PERSON><PERSON><PERSON><PERSON>,
  validateKeyPair,
  type ActorKeyPair 
} from './crypto';
import { ActivityPubLogs } from './logger';
import { SecurityError, ValidationError } from './errors';
import { invalidatePublicKey } from './key-cache';

/**
 * Actor key management result
 */
export interface KeyManagementResult {
  success: boolean;
  keyPair?: ActorKeyPair;
  error?: string;
}

/**
 * Initialize keys for a local actor
 */
export async function initializeActorKeys(
  userId: string,
  actorUrl: string
): Promise<KeyManagementResult> {
  try {
    ActivityPubLogs.security.keyInitialization(userId, actorUrl);

    const keyPair = await ensureActorKeys(userId, actorUrl);

    return {
      success: true,
      keyPair
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Rotate keys for an existing actor
 */
export async function rotateActorKeysForUser(
  userId: string
): Promise<KeyManagementResult> {
  try {
    // Get current user data
    const userData = await db
      .select({
        uri: user.uri
      })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (!userData[0]) {
      throw new ValidationError(`User not found: ${userId}`, 'USER_NOT_FOUND');
    }

    const actorUrl = userData[0].uri;

    // Invalidate cached public key
    const oldKeyId = `${actorUrl}#main-key`;
    invalidatePublicKey(oldKeyId);

    // Generate and store new keys
    const keyPair = await rotateActorKeys(userId, actorUrl);

    return {
      success: true,
      keyPair
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Get actor keys for signing requests
 */
export async function getActorKeysForSigning(userId: string): Promise<ActorKeyPair | null> {
  try {
    const keyPair = await getActorKeys(userId);
    
    if (!keyPair) {
      ActivityPubLogs.security.keyRetrievalFailed(userId, 'No keys found for user');
      return null;
    }

    if (!validateKeyPair(keyPair)) {
      ActivityPubLogs.security.keyRetrievalFailed(userId, 'Invalid key pair format');
      return null;
    }

    return keyPair;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.keyRetrievalFailed(userId, errorMessage);
    return null;
  }
}

/**
 * Validate actor has valid keys
 */
export async function validateActorKeys(userId: string): Promise<boolean> {
  try {
    const keyPair = await getActorKeys(userId);
    
    if (!keyPair) {
      return false;
    }

    return validateKeyPair(keyPair);

  } catch {
    return false;
  }
}

/**
 * Get public key for an actor (for external use)
 */
export async function getActorPublicKey(userId: string): Promise<string | null> {
  try {
    const keyPair = await getActorKeys(userId);
    return keyPair?.publicKey || null;

  } catch {
    return null;
  }
}

/**
 * Create ActivityPub publicKey object for actor
 */
export async function createPublicKeyObject(userId: string): Promise<object | null> {
  try {
    // Get user data and keys
    const userData = await db
      .select({
        uri: user.uri,
        publicKey: user.publicKey
      })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (!userData[0] || !userData[0].publicKey) {
      return null;
    }

    const actorUrl = userData[0].uri;
    const publicKeyPem = userData[0].publicKey;

    return {
      id: `${actorUrl}#main-key`,
      owner: actorUrl,
      publicKeyPem
    };

  } catch {
    return null;
  }
}

/**
 * Bulk key validation for multiple actors
 */
export async function validateMultipleActorKeys(userIds: string[]): Promise<{
  valid: string[];
  invalid: string[];
  errors: Record<string, string>;
}> {
  const valid: string[] = [];
  const invalid: string[] = [];
  const errors: Record<string, string> = {};

  for (const userId of userIds) {
    try {
      const isValid = await validateActorKeys(userId);
      
      if (isValid) {
        valid.push(userId);
      } else {
        invalid.push(userId);
        errors[userId] = 'Invalid or missing keys';
      }

    } catch (error) {
      invalid.push(userId);
      errors[userId] = error instanceof Error ? error.message : 'Unknown error';
    }
  }

  return { valid, invalid, errors };
}

/**
 * Emergency key rotation for compromised keys
 */
export async function emergencyKeyRotation(userId: string): Promise<KeyManagementResult> {
  try {
    ActivityPubLogs.security.emergencyKeyRotation(userId);

    // Get user data
    const userData = await db
      .select({
        uri: user.uri
      })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (!userData[0]) {
      throw new ValidationError(`User not found: ${userId}`, 'USER_NOT_FOUND');
    }

    const actorUrl = userData[0].uri;

    // Invalidate all cached keys for this actor
    const keyId = `${actorUrl}#main-key`;
    invalidatePublicKey(keyId);

    // Generate new keys immediately
    const keyPair = await rotateActorKeys(userId, actorUrl);

    ActivityPubLogs.security.emergencyKeyRotationCompleted(userId, keyPair.keyId);

    return {
      success: true,
      keyPair
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.emergencyKeyRotationFailed(userId, errorMessage);
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Get key statistics for monitoring
 */
export async function getKeyStatistics(): Promise<{
  totalActors: number;
  actorsWithKeys: number;
  actorsWithoutKeys: number;
  invalidKeys: number;
}> {
  try {
    // Get all local actors
    const allActors = await db
      .select({
        id: user.id,
        publicKey: user.publicKey,
        privateKey: user.privateKey
      })
      .from(user);

    const totalActors = allActors.length;
    let actorsWithKeys = 0;
    let invalidKeys = 0;

    for (const actor of allActors) {
      if (actor.publicKey && actor.privateKey) {
        actorsWithKeys++;
        
        // Validate key format
        const keyPair = {
          publicKey: actor.publicKey,
          privateKey: actor.privateKey,
          keyId: `actor#main-key` // Dummy keyId for validation
        };
        
        if (!validateKeyPair(keyPair)) {
          invalidKeys++;
        }
      }
    }

    return {
      totalActors,
      actorsWithKeys,
      actorsWithoutKeys: totalActors - actorsWithKeys,
      invalidKeys
    };

  } catch {
    return {
      totalActors: 0,
      actorsWithKeys: 0,
      actorsWithoutKeys: 0,
      invalidKeys: 0
    };
  }
}
