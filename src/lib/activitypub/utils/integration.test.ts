import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processActivityBase } from './activities.js';
import { validateActivityPubObject } from './validation.js';
import { normalizeAPObject } from './normalization.js';
import { signedFetch } from './http-client.js';
import {
  createTestCreateActivity,
  createTestFollowActivity,
  createTestPerson,
  createTestNote
} from '../../../test/activitypub-helpers.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

// Mock external dependencies
vi.mock('../actors', () => ({
  discoverActor: vi.fn()
}));

vi.mock('../rateLimiter', () => ({
  rateLimitedFetch: vi.fn()
}));

vi.mock('./http-signature', () => ({
  generateSignature: vi.fn().mockReturnValue('mock-signature'),
  generateDigest: vi.fn().mockReturnValue('mock-digest'),
  signRequest: vi.fn().mockImplementation((method, url, headers, body, privateKey, keyId) => {
    const urlObj = new URL(url);
    const result = { ...headers };

    // Add standard headers that would be added by the real function
    result['Host'] = urlObj.host;
    result['Date'] = new Date().toUTCString();
    result['Signature'] = 'mock-signature';

    // Only add Digest for requests with body (POST, PUT, etc.)
    if (body && method !== 'GET') {
      result['Digest'] = 'SHA-256=mock-digest';
    }

    return Promise.resolve(result);
  })
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    federation: {
      outgoingRequest: vi.fn(),
      outgoingResponse: vi.fn(),
      outgoingError: vi.fn(),
      outgoingSuccess: vi.fn()
    }
  },
  logger: {
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

const mockFetch = vi.fn();
global.fetch = mockFetch;

import { discoverActor } from '../actors';
import { generateSignature, generateDigest } from './http-signature';
import { rateLimitedFetch } from '../rateLimiter';

describe('ActivityPub Utils - Integration Tests', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
    
    // Setup default mocks
    vi.mocked(generateSignature).mockReturnValue('mock-signature');
    vi.mocked(generateDigest).mockReturnValue('SHA-256=mock-digest');
    vi.mocked(discoverActor).mockResolvedValue(createTestPerson());

    // Setup fetch mocks
    mockFetch.mockResolvedValue(new Response('{"success": true}', {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }));

    vi.mocked(rateLimitedFetch).mockResolvedValue(new Response('{"success": true}', {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }));
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('End-to-End Activity Processing', () => {
    it('should process a complete Follow activity workflow', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      const mockActor = createTestPerson('https://example.com/users/alice');
      const mockTargetActor = createTestPerson('https://example.com/users/bob');
      
      vi.mocked(discoverActor)
        .mockResolvedValueOnce(mockActor)
        .mockResolvedValueOnce(mockTargetActor);

      let processedActivity: any = null;
      let processedActors: any = null;

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob'
        }),
        processActivity: async (activity, actors) => {
          processedActivity = activity;
          processedActors = actors;
          return true;
        },
        activityName: 'Follow'
      });

      expect(result).toBe(true);
      expect(processedActivity).toEqual(activity);
      expect(processedActors.actor).toEqual(mockActor);
      expect(processedActors.objectActor).toEqual(mockTargetActor);
      expect(discoverActor).toHaveBeenCalledTimes(2);
    });

    it('should handle validation and normalization pipeline', async () => {
      const rawActivity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/123',
        actor: 'https://example.com/users/alice',
        published: '2023-01-01T00:00:00Z',
        object: {
          type: 'Note',
          id: 'https://example.com/notes/456',
          content: 'Hello world!',
          published: '2023-01-01T00:00:00Z',
          attributedTo: 'https://example.com/users/alice'
        }
      };

      // Step 1: Validate
      const validationResult = validateActivityPubObject(rawActivity);
      expect(validationResult.valid).toBe(true);

      // Step 2: Normalize
      const normalizationResult = normalizeAPObject(rawActivity);
      expect(normalizationResult.value).toBeDefined();
      expect(normalizationResult.errors).toHaveLength(0);

      // Step 3: Process with base function
      const result = await processActivityBase(normalizationResult.value as any, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice'
        }),
        processActivity: async () => true,
        activityName: 'Create'
      });

      expect(result).toBe(true);
    });

    it('should handle network requests with proper signing', async () => {
      const url = 'https://example.com/inbox';
      const activity = createTestCreateActivity();
      const privateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----';
      const keyId = 'https://example.com/users/alice#main-key';

      const response = await signedFetch(url, {
        method: 'POST',
        body: JSON.stringify(activity),
        privateKey,
        keyId,
        headers: {
          'Content-Type': 'application/activity+json'
        }
      });

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(activity),
        headers: expect.objectContaining({
          'Signature': 'mock-signature',
          'Digest': 'SHA-256=mock-digest',
          'Content-Type': 'application/activity+json'
        })
      }));

      expect(response.ok).toBe(true);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle actor discovery failures gracefully', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      
      vi.mocked(discoverActor).mockResolvedValue(null);

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/nonexistent',
          objectUri: 'https://example.com/users/bob'
        }),
        processActivity: async () => true,
        activityName: 'Follow'
      });

      expect(result).toBe(false);
    });

    it('should handle network failures with proper error propagation', async () => {
      vi.mocked(rateLimitedFetch).mockRejectedValue(new Error('Network timeout'));

      const url = 'https://example.com/unreachable';
      
      await expect(signedFetch(url, {
        method: 'GET',
        privateKey: 'key',
        keyId: 'keyId'
      })).rejects.toThrow('Network timeout');
    });

    it('should handle malformed ActivityPub objects', async () => {
      const malformedActivity = {
        type: 'InvalidType',
        // Missing required fields
      };

      // With default options (strict: false), unknown types are warnings, not errors
      const validationResult = validateActivityPubObject(malformedActivity);
      expect(validationResult.valid).toBe(true);
      expect(validationResult.warnings.length).toBeGreaterThan(0);

      // With strict validation, it should be invalid
      const strictValidationResult = validateActivityPubObject(malformedActivity, { strict: true });
      expect(strictValidationResult.valid).toBe(false);
      expect(strictValidationResult.errors.length).toBeGreaterThan(0);

      // Should not proceed to processing
      const result = await processActivityBase(malformedActivity as any, {
        requiredFields: ['actor', 'object'],
        extractUrls: () => ({ actorUri: 'test' }),
        processActivity: async () => true,
        activityName: 'Invalid'
      });

      expect(result).toBe(false);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle batch processing efficiently', async () => {
      const activities = Array.from({ length: 10 }, () => createTestFollowActivity('https://example.com/users/bob'));
      const results: boolean[] = [];

      // Process activities in parallel
      const promises = activities.map(activity => 
        processActivityBase(activity, {
          requiredFields: ['actor', 'object'],
          extractUrls: (activity) => ({
            actorUri: 'https://example.com/users/alice',
            objectUri: 'https://example.com/users/bob'
          }),
          processActivity: async () => true,
          activityName: 'Follow'
        })
      );

      const batchResults = await Promise.all(promises);
      
      expect(batchResults).toHaveLength(10);
      expect(batchResults.every(result => result === true)).toBe(true);
    });

    it('should handle large objects without memory issues', async () => {
      const largeActivity = {
        ...createTestCreateActivity(),
        object: {
          ...createTestNote(),
          content: 'x'.repeat(10000), // Large content
          attachment: Array.from({ length: 100 }, (_, i) => ({
            type: 'Document',
            url: `https://example.com/file${i}.jpg`
          }))
        }
      };

      const validationResult = validateActivityPubObject(largeActivity);
      expect(validationResult.valid).toBe(true);

      const normalizationResult = normalizeAPObject(largeActivity);
      expect(normalizationResult.value).toBeDefined();
    });
  });

  describe('Security Integration', () => {
    it('should validate and sanitize content throughout the pipeline', async () => {
      const activityWithDangerousContent = {
        type: 'Create',
        actor: 'https://example.com/users/alice',
        object: {
          type: 'Note',
          content: '<script>alert("xss")</script>Hello world!',
          name: 'javascript:alert("xss")'
        }
      };

      // Validation should catch security issues
      const validationResult = validateActivityPubObject(activityWithDangerousContent);
      
      // Normalization should sanitize content
      const normalizationResult = normalizeAPObject(activityWithDangerousContent);
      
      // Content should be processed (control characters removed, but HTML tags preserved)
      expect(normalizationResult.value.object.content).toContain('<script>');
      expect(normalizationResult.value.object.content).toContain('Hello world!');
    });

    it('should verify HTTP signatures in request pipeline', async () => {
      const url = 'https://example.com/inbox';
      const activity = createTestCreateActivity();
      
      const response = await signedFetch(url, {
        method: 'POST',
        body: JSON.stringify(activity),
        privateKey: 'valid-key',
        keyId: 'https://example.com/users/alice#main-key'
      });

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        headers: expect.objectContaining({
          'Signature': 'mock-signature'
        })
      }));
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle a complete federation handshake', async () => {
      // Scenario: Alice follows Bob on a remote server
      const followActivity = createTestFollowActivity('https://bob.example/users/bob');
      const aliceActor = createTestPerson('https://alice.example/users/alice');
      const bobActor = createTestPerson('https://bob.example/users/bob');
      
      vi.mocked(discoverActor)
        .mockResolvedValueOnce(aliceActor)
        .mockResolvedValueOnce(bobActor);

      // Step 1: Process the Follow activity
      const followResult = await processActivityBase(followActivity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: aliceActor.id,
          objectUri: bobActor.id
        }),
        processActivity: async (activity, actors) => {
          // Simulate database operations
          expect(actors.actor.id).toBe(aliceActor.id);
          expect(actors.objectActor.id).toBe(bobActor.id);
          return true;
        },
        activityName: 'Follow'
      });

      expect(followResult).toBe(true);

      // Step 2: Send Accept activity back
      const acceptActivity = {
        type: 'Accept',
        actor: bobActor.id,
        object: followActivity
      };

      const acceptResult = await signedFetch(`${aliceActor.inbox}`, {
        method: 'POST',
        body: JSON.stringify(acceptActivity),
        privateKey: 'bob-private-key',
        keyId: `${bobActor.id}#main-key`
      });

      expect(acceptResult.ok).toBe(true);
    });

    it('should handle content federation with media attachments', async () => {
      const createActivity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        id: 'https://example.com/activities/456',
        actor: 'https://example.com/users/alice',
        published: '2023-01-01T00:00:00Z',
        object: {
          type: 'Note',
          id: 'https://example.com/notes/789',
          content: 'Check out this image!',
          published: '2023-01-01T00:00:00Z',
          attributedTo: 'https://example.com/users/alice',
          attachment: [
            {
              type: 'Document',
              mediaType: 'image/jpeg',
              url: 'https://example.com/media/image.jpg'
            }
          ]
        }
      };

      // Validate the complex object
      const validationResult = validateActivityPubObject(createActivity);
      expect(validationResult.valid).toBe(true);

      // Process with media handling
      const result = await processActivityBase(createActivity as any, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice'
        }),
        processActivity: async (activity, actors) => {
          // Verify media attachment is preserved
          expect(activity.object.attachment).toHaveLength(1);
          expect(activity.object.attachment[0].url).toBe('https://example.com/media/image.jpg');
          return true;
        },
        activityName: 'Create'
      });

      expect(result).toBe(true);
    });
  });
});
