/**
 * HTTP Signature validation middleware for ActivityPub inbox processing.
 * Provides request validation, signature verification, and security checks.
 */

import type { RequestEvent } from '@sveltejs/kit';
import { validateIncomingSignature } from './http-signature';
import { ActivityPubLogs } from './logger';
import { SecurityError, ValidationError } from './errors';
import { getFederationConfig } from '$lib/activitypub/config/federation';

/**
 * Signature validation result
 */
export interface SignatureValidationResult {
  valid: boolean;
  keyId?: string;
  error?: string;
  statusCode?: number;
}

/**
 * Signature validation options
 */
export interface SignatureValidationOptions {
  requireSignature?: boolean;     // Whether signature is required
  allowedClockSkew?: number;      // Allowed clock skew in seconds
  maxBodySize?: number;           // Maximum request body size
  trustedDomains?: string[];      // List of trusted domains
  blockedDomains?: string[];      // List of blocked domains
}

/**
 * Extract domain from keyId or actor URL
 */
function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return null;
  }
}

/**
 * Check if domain is in allowed/blocked lists
 */
function isDomainAllowed(
  domain: string,
  options: SignatureValidationOptions
): boolean {
  // Check blocked domains first
  if (options.blockedDomains?.includes(domain)) {
    return false;
  }

  // If trusted domains are specified, only allow those
  if (options.trustedDomains && options.trustedDomains.length > 0) {
    return options.trustedDomains.includes(domain);
  }

  // Allow all domains if no restrictions
  return true;
}

/**
 * Validate HTTP signature for incoming ActivityPub request
 */
export async function validateRequestSignature(
  event: RequestEvent,
  options: SignatureValidationOptions = {}
): Promise<SignatureValidationResult> {
  const federationConfig = getFederationConfig();
  
  // Merge options with federation config
  const validationOptions: SignatureValidationOptions = {
    requireSignature: federationConfig.httpSignature.required,
    allowedClockSkew: federationConfig.httpSignature.clockSkewTolerance / 1000, // Convert to seconds
    maxBodySize: 1024 * 1024, // 1MB default
    ...options
  };

  try {
    const { request } = event;
    const method = request.method;
    const url = request.url;

    // Check if signature is present
    const signatureHeader = request.headers.get('signature');
    
    if (!signatureHeader) {
      if (validationOptions.requireSignature) {
        ActivityPubLogs.security.signatureValidationFailed('unknown', url, 'Missing signature header');
        return {
          valid: false,
          error: 'Signature required but not provided',
          statusCode: 401
        };
      } else {
        // Signature not required, allow request
        return { valid: true };
      }
    }

    // Get request body
    let body = '';
    if (method !== 'GET' && method !== 'HEAD') {
      try {
        body = await request.text();
        
        // Check body size
        if (validationOptions.maxBodySize && body.length > validationOptions.maxBodySize) {
          ActivityPubLogs.security.blockedRequest(url, 'Request body too large');
          return {
            valid: false,
            error: 'Request body too large',
            statusCode: 413
          };
        }
      } catch (error) {
        ActivityPubLogs.security.signatureValidationFailed('unknown', url, 'Failed to read request body');
        return {
          valid: false,
          error: 'Failed to read request body',
          statusCode: 400
        };
      }
    }

    // Convert headers to object
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });

    // Validate signature
    const validationResult = await validateIncomingSignature(
      method,
      url,
      headers,
      body
    );

    if (!validationResult.valid) {
      ActivityPubLogs.security.signatureVerificationFailed(
        validationResult.keyId || 'unknown',
        url,
        validationResult.error || 'Signature validation failed'
      );
      
      return {
        valid: false,
        error: validationResult.error || 'Invalid signature',
        statusCode: 401
      };
    }

    // Check domain restrictions
    if (validationResult.keyId) {
      const domain = extractDomain(validationResult.keyId);
      
      if (domain && !isDomainAllowed(domain, validationOptions)) {
        ActivityPubLogs.security.blockedRequest(url, `Domain not allowed: ${domain}`);
        return {
          valid: false,
          error: 'Domain not allowed',
          statusCode: 403
        };
      }
    }

    ActivityPubLogs.security.signatureVerified(
      validationResult.keyId || 'unknown',
      url
    );

    return {
      valid: true,
      keyId: validationResult.keyId
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.signatureValidationFailed('unknown', event.request.url, errorMessage);
    
    return {
      valid: false,
      error: errorMessage,
      statusCode: 500
    };
  }
}

/**
 * Create signature validation middleware function
 */
export function createSignatureValidationMiddleware(
  options: SignatureValidationOptions = {}
) {
  return async (event: RequestEvent): Promise<SignatureValidationResult> => {
    return validateRequestSignature(event, options);
  };
}

/**
 * Strict signature validation for high-security endpoints
 */
export async function validateStrictSignature(
  event: RequestEvent
): Promise<SignatureValidationResult> {
  return validateRequestSignature(event, {
    requireSignature: true,
    allowedClockSkew: 30, // 30 seconds
    maxBodySize: 512 * 1024 // 512KB
  });
}

/**
 * Lenient signature validation for public endpoints
 */
export async function validateLenientSignature(
  event: RequestEvent
): Promise<SignatureValidationResult> {
  return validateRequestSignature(event, {
    requireSignature: false,
    allowedClockSkew: 300, // 5 minutes
    maxBodySize: 2 * 1024 * 1024 // 2MB
  });
}

/**
 * Validate signature and extract actor information
 */
export async function validateAndExtractActor(
  event: RequestEvent,
  options: SignatureValidationOptions = {}
): Promise<{
  valid: boolean;
  keyId?: string;
  actorUrl?: string;
  domain?: string;
  error?: string;
  statusCode?: number;
}> {
  const result = await validateRequestSignature(event, options);
  
  if (!result.valid) {
    return result;
  }

  if (result.keyId) {
    // Extract actor URL from keyId (usually keyId is actorUrl#main-key)
    const actorUrl = result.keyId.split('#')[0];
    const domain = extractDomain(actorUrl);

    return {
      ...result,
      actorUrl,
      domain
    };
  }

  return result;
}

/**
 * Rate limiting based on signature validation
 */
export interface RateLimitConfig {
  windowMs: number;        // Time window in milliseconds
  maxRequests: number;     // Maximum requests per window
  skipSuccessful?: boolean; // Skip rate limiting for valid signatures
}

/**
 * Simple in-memory rate limiter (for production, use Redis or similar)
 */
class SimpleRateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(key: string, config: RateLimitConfig): boolean {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Get existing requests for this key
    let requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (requests.length >= config.maxRequests) {
      return false;
    }
    
    // Add current request
    requests.push(now);
    this.requests.set(key, requests);
    
    return true;
  }

  cleanup(): void {
    const now = Date.now();
    const cutoff = now - (24 * 60 * 60 * 1000); // 24 hours
    
    for (const [key, requests] of this.requests.entries()) {
      const validRequests = requests.filter(timestamp => timestamp > cutoff);
      
      if (validRequests.length === 0) {
        this.requests.delete(key);
      } else {
        this.requests.set(key, validRequests);
      }
    }
  }
}

const rateLimiter = new SimpleRateLimiter();

/**
 * Validate signature with rate limiting
 */
export async function validateWithRateLimit(
  event: RequestEvent,
  rateLimitConfig: RateLimitConfig,
  validationOptions: SignatureValidationOptions = {}
): Promise<SignatureValidationResult & { rateLimited?: boolean }> {
  const clientIP = event.getClientAddress();
  
  // Check rate limit first
  if (!rateLimiter.isAllowed(clientIP, rateLimitConfig)) {
    ActivityPubLogs.security.blockedRequest(event.request.url, 'Rate limit exceeded');
    return {
      valid: false,
      error: 'Rate limit exceeded',
      statusCode: 429,
      rateLimited: true
    };
  }

  // Validate signature
  const result = await validateRequestSignature(event, validationOptions);
  
  // If signature is valid and we should skip rate limiting for successful requests
  if (result.valid && rateLimitConfig.skipSuccessful) {
    // Remove the request from rate limiting
    // (This is a simplified implementation)
  }

  return result;
}
