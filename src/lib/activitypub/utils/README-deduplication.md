# ActivityPub Deduplication and Idempotency System

This system provides comprehensive deduplication and idempotency mechanisms for ActivityPub operations, ensuring that activities are not processed multiple times and operations can be safely retried.

## Core Components

### 1. Deduplication (`deduplication.ts`)

Provides basic deduplication functionality:

```typescript
import { 
  checkActivityDuplication, 
  createProcessingRecord, 
  updateProcessingStatus 
} from '$lib/activitypub/utils/deduplication';

// Check if activity is duplicate
const duplicationCheck = await checkActivityDuplication(activity, sourceInbox);
if (duplicationCheck.isDuplicate) {
  // Activity already processed
  return duplicationCheck.existingResult;
}

// Create processing record
const processingId = await createProcessingRecord(activity, sourceInbox);

// Update status
await updateProcessingStatus(processingId, 'completed', result);
```

### 2. Idempotency (`idempotency.ts`)

Provides idempotent operation execution:

```typescript
import { executeIdempotent } from '$lib/activitypub/utils/idempotency';

// Execute operation idempotently
const result = await executeIdempotent(
  activity,
  async () => {
    // Your operation here
    return await processActivity(activity);
  },
  sourceInbox
);

if (result.success) {
  console.log('Operation completed:', result.result);
  console.log('Was cached:', result.wasProcessed);
}
```

### 3. Middleware (`deduplication-middleware.ts`)

Provides high-level middleware for inbox/outbox processing:

```typescript
import { withInboxDeduplication } from '$lib/activitypub/utils/deduplication-middleware';

// Wrap inbox processing
const result = await withInboxDeduplication(
  activity,
  async () => {
    // Process the activity
    return await processInboxActivity(activity);
  },
  sourceInbox
);

if (result.isDuplicate) {
  console.log('Activity was already processed');
} else {
  console.log('Activity processed successfully');
}
```

### 4. Scheduler (`deduplication-scheduler.ts`)

Provides automatic cleanup of old processing records:

```typescript
import { 
  startDeduplicationScheduler,
  getDeduplicationStats 
} from '$lib/activitypub/utils/deduplication-scheduler';

// Start automatic cleanup
startDeduplicationScheduler({
  cleanupIntervalMs: 60 * 60 * 1000, // 1 hour
  cleanupOlderThanHours: 24, // 24 hours
  statsIntervalMs: 15 * 60 * 1000 // 15 minutes
});

// Get statistics
const stats = await getDeduplicationStats();
console.log('Processing stats:', stats);
```

## Database Schema

The system uses the `activityProcessing` table:

```sql
CREATE TABLE activity_processing (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activity_uri VARCHAR NOT NULL UNIQUE,
  activity_type VARCHAR NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'pending',
  idempotency_key VARCHAR NOT NULL UNIQUE,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  received_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  source_inbox VARCHAR,
  http_signature_valid BOOLEAN,
  result JSONB
);
```

## Configuration

Configure deduplication through federation config:

```typescript
// In federation.ts
processing: {
  deduplicationWindow: 300000, // 5 minutes
  maxConcurrentActivities: 20,
  activityTimeout: 30000
}
```

## Usage Patterns

### Basic Inbox Processing

```typescript
import { withInboxDeduplication } from '$lib/activitypub/utils/deduplication-middleware';

export async function processInboxActivity(activity: Activity, sourceInbox: string) {
  return withInboxDeduplication(
    activity,
    async () => {
      // Your processing logic
      switch (activity.type) {
        case 'Create':
          return await processCreate(activity);
        case 'Follow':
          return await processFollow(activity);
        // ... other types
      }
    },
    sourceInbox
  );
}
```

### Outbox Processing

```typescript
import { withOutboxDeduplication } from '$lib/activitypub/utils/deduplication-middleware';

export async function sendActivity(activity: Activity) {
  return withOutboxDeduplication(
    activity,
    async () => {
      // Store and deliver activity
      const stored = await storeActivity(activity);
      await scheduleDelivery(stored.id);
      return stored;
    }
  );
}
```

### Custom Idempotency Keys

```typescript
import { executeIdempotent } from '$lib/activitypub/utils/idempotency';

// Include timestamp for time-sensitive operations
const result = await executeIdempotent(
  activity,
  operation,
  sourceInbox,
  {
    includeTimestamp: true,
    customSalt: 'follow-operation'
  }
);
```

### Batch Processing

```typescript
import { withBatchDeduplication } from '$lib/activitypub/utils/deduplication-middleware';

const activities = [
  { activity: activity1, processor: () => processActivity1() },
  { activity: activity2, processor: () => processActivity2() }
];

const results = await withBatchDeduplication(activities);
results.forEach(result => {
  if (result.success && !result.isDuplicate) {
    console.log('Processed:', result.activityId);
  }
});
```

## Error Handling

The system handles various error scenarios:

- **Database errors**: Gracefully degrades to allow processing
- **Duplicate detection**: Returns existing results when available
- **Processing failures**: Marks records as failed with error messages
- **Timeout handling**: Prevents infinite processing loops

## Monitoring

Monitor the system through:

1. **Statistics**: Use `getDeduplicationStats()` for current metrics
2. **Logs**: All operations are logged through ActivityPubLogs
3. **Health checks**: Use `getSchedulerHealth()` for scheduler status

## Performance Considerations

- **Idempotency keys**: Generated using SHA-256 hashing for consistency
- **Database indexes**: Unique indexes on `activity_uri` and `idempotency_key`
- **Cleanup**: Automatic cleanup prevents table growth
- **Caching**: Results are cached in processing records

## Testing

Comprehensive test suite covers:

- Idempotency key generation
- Duplicate detection
- Processing record management
- Middleware functionality
- Error scenarios

Run tests with:
```bash
pnpm test src/lib/activitypub/utils/deduplication.test.ts
```

## Integration

The system integrates with:

- **Inbox processing**: Automatic deduplication of incoming activities
- **Outbox processing**: Prevents duplicate outgoing activities
- **Delivery system**: Ensures delivery attempts are idempotent
- **Activity processors**: All activity types benefit from deduplication
- **Logging system**: Comprehensive logging of all operations
- **Metrics system**: Statistics collection and monitoring
