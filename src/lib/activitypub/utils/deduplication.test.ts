/**
 * Tests for deduplication and idempotency utilities
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  generateIdempotencyKey,
  checkActivityDuplication,
  createProcessingRecord,
  updateProcessingStatus,
  getProcessingRecord,
  cleanupOldProcessingRecords,
  getProcessingStats
} from './deduplication';
import {
  executeIdempotent,
  isOperationCompleted,
  retryIdempotent,
  getOperationStatus
} from './idempotency';
import {
  withInboxDeduplication,
  withOutboxDeduplication,
  shouldProcessActivity
} from './deduplication-middleware';
import type { Activity } from '$lib/activitypub/types';

// Mock database
vi.mock('$lib/server/db', () => ({
  db: {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    where: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    values: vi.fn().mockReturnThis(),
    returning: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis()
  }
}));

// Mock logger
vi.mock('./logger', () => ({
  ActivityPubLogs: {
    federation: {
      activityDuplicate: vi.fn(),
      deduplicationError: vi.fn(),
      processingRecordCreated: vi.fn(),
      processingStatusUpdated: vi.fn(),
      processingCleanupCompleted: vi.fn(),
      idempotentOperationSkipped: vi.fn(),
      idempotentOperationStarted: vi.fn(),
      idempotentOperationCompleted: vi.fn(),
      idempotentOperationFailed: vi.fn(),
      idempotentOperationRetried: vi.fn()
    }
  }
}));

describe('Deduplication Utils', () => {
  const mockActivity: Activity = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    id: 'https://example.com/activities/123',
    type: 'Create',
    actor: 'https://example.com/users/alice',
    object: {
      id: 'https://example.com/posts/456',
      type: 'Note',
      content: 'Hello world'
    },
    published: '2023-01-01T00:00:00Z'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateIdempotencyKey', () => {
    it('should generate consistent keys for same activity', () => {
      const key1 = generateIdempotencyKey(mockActivity);
      const key2 = generateIdempotencyKey(mockActivity);
      expect(key1).toBe(key2);
    });

    it('should generate different keys for different activities', () => {
      const activity2 = { ...mockActivity, id: 'https://example.com/activities/456' };
      const key1 = generateIdempotencyKey(mockActivity);
      const key2 = generateIdempotencyKey(activity2);
      expect(key1).not.toBe(key2);
    });

    it('should include source inbox when specified', () => {
      const key1 = generateIdempotencyKey(mockActivity, undefined, { includeSource: false });
      const key2 = generateIdempotencyKey(mockActivity, 'https://example.com/inbox', { includeSource: true });
      expect(key1).not.toBe(key2);
    });

    it('should include timestamp when specified', () => {
      const key1 = generateIdempotencyKey(mockActivity, undefined, { includeTimestamp: false });
      const key2 = generateIdempotencyKey(mockActivity, undefined, { includeTimestamp: true });
      expect(key1).not.toBe(key2);
    });

    it('should include custom salt when provided', () => {
      const key1 = generateIdempotencyKey(mockActivity);
      const key2 = generateIdempotencyKey(mockActivity, undefined, { customSalt: 'test' });
      expect(key1).not.toBe(key2);
    });
  });

  describe('checkActivityDuplication', () => {
    it('should return no duplicate for new activity', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      }));

      const result = await checkActivityDuplication(mockActivity);
      expect(result.isDuplicate).toBe(false);
      expect(result.shouldProcess).toBe(true);
    });

    it('should detect duplicate by activity URI', async () => {
      const { db } = await import('$lib/server/db');
      const existingRecord = {
        id: 'proc-123',
        status: 'completed',
        result: { success: true }
      };

      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([existingRecord])
      }));

      const result = await checkActivityDuplication(mockActivity);
      expect(result.isDuplicate).toBe(true);
      expect(result.shouldProcess).toBe(false);
      expect(result.existingStatus).toBe('completed');
      expect(result.processingId).toBe('proc-123');
    });

    it('should handle errors gracefully', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => {
        throw new Error('Database error');
      });

      const result = await checkActivityDuplication(mockActivity);
      expect(result.isDuplicate).toBe(false);
      expect(result.shouldProcess).toBe(true);
    });
  });

  describe('createProcessingRecord', () => {
    it('should create new processing record', async () => {
      const { db } = await import('$lib/server/db');
      (db.insert as any).mockImplementation(() => ({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 'proc-123' }])
      }));

      const processingId = await createProcessingRecord(mockActivity);
      expect(processingId).toBe('proc-123');
    });

    it('should throw error if activity has no ID', async () => {
      const activityWithoutId = { ...mockActivity };
      delete activityWithoutId.id;

      await expect(createProcessingRecord(activityWithoutId)).rejects.toThrow('Activity must have an ID');
    });
  });

  describe('updateProcessingStatus', () => {
    it('should update processing status', async () => {
      const { db } = await import('$lib/server/db');
      (db.update as any).mockImplementation(() => ({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      }));

      await updateProcessingStatus('proc-123', 'completed', { success: true });
      expect(db.update).toHaveBeenCalled();
    });
  });
});

describe('Idempotency Utils', () => {
  const mockActivity: Activity = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    id: 'https://example.com/activities/123',
    type: 'Create',
    actor: 'https://example.com/users/alice',
    object: 'https://example.com/posts/456'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('executeIdempotent', () => {
    it('should execute operation for new activity', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      }));
      (db.insert as any).mockImplementation(() => ({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 'proc-123' }])
      }));
      (db.update as any).mockImplementation(() => ({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      }));

      const operation = vi.fn().mockResolvedValue({ success: true });
      const result = await executeIdempotent(mockActivity, operation);

      expect(result.success).toBe(true);
      expect(result.wasProcessed).toBe(false);
      expect(result.result).toEqual({ success: true });
      expect(operation).toHaveBeenCalledOnce();
    });

    it('should return existing result for duplicate activity', async () => {
      const { db } = await import('$lib/server/db');
      const existingRecord = {
        id: 'proc-123',
        status: 'completed',
        result: { success: true, cached: true }
      };

      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([existingRecord])
      }));

      const operation = vi.fn().mockResolvedValue({ success: true });
      const result = await executeIdempotent(mockActivity, operation);

      expect(result.success).toBe(true);
      expect(result.wasProcessed).toBe(true);
      expect(result.result).toEqual({ success: true, cached: true });
      expect(operation).not.toHaveBeenCalled();
    });

    it('should handle operation failures', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      }));
      (db.insert as any).mockImplementation(() => ({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 'proc-123' }])
      }));
      (db.update as any).mockImplementation(() => ({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      }));

      const operation = vi.fn().mockRejectedValue(new Error('Operation failed'));
      const result = await executeIdempotent(mockActivity, operation);

      expect(result.success).toBe(false);
      expect(result.wasProcessed).toBe(false);
      expect(result.error).toBe('Operation failed');
    });
  });
});

describe('Deduplication Middleware', () => {
  const mockActivity: Activity = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    id: 'https://example.com/activities/123',
    type: 'Create',
    actor: 'https://example.com/users/alice',
    object: 'https://example.com/posts/456'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('withInboxDeduplication', () => {
    it('should process new activity', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      }));
      (db.insert as any).mockImplementation(() => ({
        values: vi.fn().mockReturnThis(),
        returning: vi.fn().mockResolvedValue([{ id: 'proc-123' }])
      }));
      (db.update as any).mockImplementation(() => ({
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockResolvedValue(undefined)
      }));

      const processor = vi.fn().mockResolvedValue({ processed: true });
      const result = await withInboxDeduplication(mockActivity, processor);

      expect(result.success).toBe(true);
      expect(result.isDuplicate).toBe(false);
      expect(result.result).toEqual({ processed: true });
      expect(processor).toHaveBeenCalledOnce();
    });

    it('should skip duplicate activity', async () => {
      const { db } = await import('$lib/server/db');
      const existingRecord = {
        id: 'proc-123',
        status: 'completed',
        result: { processed: true, cached: true }
      };

      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([existingRecord])
      }));

      const processor = vi.fn().mockResolvedValue({ processed: true });
      const result = await withInboxDeduplication(mockActivity, processor);

      expect(result.success).toBe(true);
      expect(result.isDuplicate).toBe(true);
      expect(result.result).toEqual({ processed: true, cached: true });
      expect(processor).not.toHaveBeenCalled();
    });
  });

  describe('shouldProcessActivity', () => {
    it('should return true for new activity', async () => {
      const { db } = await import('$lib/server/db');
      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([])
      }));

      const result = await shouldProcessActivity(mockActivity);
      expect(result.shouldProcess).toBe(true);
    });

    it('should return false for duplicate activity', async () => {
      const { db } = await import('$lib/server/db');
      const existingRecord = {
        id: 'proc-123',
        status: 'completed'
      };

      (db.select as any).mockImplementation(() => ({
        from: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        limit: vi.fn().mockResolvedValue([existingRecord])
      }));

      const result = await shouldProcessActivity(mockActivity);
      expect(result.shouldProcess).toBe(false);
      expect(result.reason).toContain('already processed');
    });
  });
});
