import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processActivityBase } from './activities.js';
import { validateActivityPubObject } from './validation.js';
import { normalizeAPObject } from './normalization.js';
import {
  createTestCreateActivity,
  createTestFollowActivity,
  createTestPerson,
  createTestNote
} from '../../../test/activitypub-helpers.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

// Mock dependencies
vi.mock('../actors', () => ({
  discoverActor: vi.fn()
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    federation: {
      outgoingError: vi.fn(),
      outgoingSuccess: vi.fn()
    }
  },
  logger: {
    debug: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

import { discoverActor } from '../actors';

describe('ActivityPub Utils - Performance Tests', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
    
    vi.mocked(discoverActor).mockResolvedValue(createTestPerson());
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('Validation Performance', () => {
    it('should validate simple objects quickly', () => {
      const note = createTestNote();
      const iterations = 1000;
      
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const result = validateActivityPubObject(note);
        expect(result.valid).toBe(true);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      // Should validate in less than 1ms per object on average
      expect(avgTime).toBeLessThan(1);
      console.log(`Validation: ${avgTime.toFixed(3)}ms per object (${iterations} iterations)`);
    });

    it('should validate complex objects efficiently', () => {
      const complexActivity = {
        ...createTestCreateActivity(),
        object: {
          ...createTestNote(),
          attachment: Array.from({ length: 50 }, (_, i) => ({
            type: 'Document',
            url: `https://example.com/file${i}.jpg`,
            mediaType: 'image/jpeg'
          })),
          tag: Array.from({ length: 20 }, (_, i) => ({
            type: 'Hashtag',
            name: `#tag${i}`,
            href: `https://example.com/tags/tag${i}`
          }))
        }
      };
      
      const iterations = 100;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const result = validateActivityPubObject(complexActivity);
        expect(result.valid).toBe(true);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      // Complex objects should still validate reasonably quickly
      expect(avgTime).toBeLessThan(10);
      console.log(`Complex validation: ${avgTime.toFixed(3)}ms per object (${iterations} iterations)`);
    });

    it('should handle batch validation efficiently', () => {
      const objects = Array.from({ length: 100 }, () => createTestNote());
      
      const startTime = performance.now();
      
      const results = objects.map(obj => validateActivityPubObject(obj));
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / objects.length;
      
      expect(results.every(r => r.valid)).toBe(true);
      expect(avgTime).toBeLessThan(2);
      console.log(`Batch validation: ${avgTime.toFixed(3)}ms per object (${objects.length} objects)`);
    });
  });

  describe('Normalization Performance', () => {
    it('should normalize objects quickly', () => {
      const note = createTestNote();
      const iterations = 1000;
      
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const result = normalizeAPObject(note);
        expect(result.value).toBeDefined();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      expect(avgTime).toBeLessThan(2);
      console.log(`Normalization: ${avgTime.toFixed(3)}ms per object (${iterations} iterations)`);
    });

    it('should handle large content normalization', () => {
      const largeNote = {
        ...createTestNote(),
        content: {
          en: 'x'.repeat(10000),
          es: 'y'.repeat(10000),
          fr: 'z'.repeat(10000)
        }
      };
      
      const iterations = 100;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const result = normalizeAPObject(largeNote);
        expect(result.value).toBeDefined();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      expect(avgTime).toBeLessThan(5);
      console.log(`Large content normalization: ${avgTime.toFixed(3)}ms per object (${iterations} iterations)`);
    });
  });

  describe('Activity Processing Performance', () => {
    it('should process activities efficiently', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      const iterations = 100;
      
      const startTime = performance.now();
      
      const promises = Array.from({ length: iterations }, () =>
        processActivityBase(activity, {
          requiredFields: ['actor', 'object'],
          extractUrls: (activity) => ({
            actorUri: 'https://example.com/users/alice',
            objectUri: 'https://example.com/users/bob'
          }),
          processActivity: async () => true,
          activityName: 'Follow'
        })
      );
      
      const results = await Promise.all(promises);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      expect(results.every(r => r === true)).toBe(true);
      expect(avgTime).toBeLessThan(50); // Including async operations
      console.log(`Activity processing: ${avgTime.toFixed(3)}ms per activity (${iterations} activities)`);
    });

    it('should handle concurrent processing', async () => {
      const activities = Array.from({ length: 50 }, (_, i) => createTestFollowActivity(`https://example.com/users/user${i}`));
      
      const startTime = performance.now();
      
      // Process in batches to simulate real-world concurrency
      const batchSize = 10;
      const batches = [];
      
      for (let i = 0; i < activities.length; i += batchSize) {
        const batch = activities.slice(i, i + batchSize);
        const batchPromise = Promise.all(
          batch.map(activity =>
            processActivityBase(activity, {
              requiredFields: ['actor', 'object'],
              extractUrls: (activity) => ({
                actorUri: 'https://example.com/users/alice',
                objectUri: 'https://example.com/users/bob'
              }),
              processActivity: async () => {
                // Simulate some processing time
                await new Promise(resolve => setTimeout(resolve, 1));
                return true;
              },
              activityName: 'Follow'
            })
          )
        );
        batches.push(batchPromise);
      }
      
      const results = await Promise.all(batches);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const totalActivities = activities.length;
      const avgTime = duration / totalActivities;
      
      expect(results.flat().every(r => r === true)).toBe(true);
      console.log(`Concurrent processing: ${avgTime.toFixed(3)}ms per activity (${totalActivities} activities, ${batches.length} batches)`);
    });
  });

  describe('Memory Usage', () => {
    it('should not leak memory during repeated operations', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform many operations
      for (let i = 0; i < 1000; i++) {
        const note = createTestNote();
        validateActivityPubObject(note);
        normalizeAPObject(note);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreaseKB = memoryIncrease / 1024;
      
      // Memory increase should be reasonable (less than 50MB for 1000 operations)
      // Note: Memory usage can vary significantly based on system conditions
      expect(memoryIncreaseKB).toBeLessThan(50 * 1024);
      console.log(`Memory increase: ${memoryIncreaseKB.toFixed(2)}KB for 1000 operations`);
    });

    it('should handle large object arrays efficiently', () => {
      const largeArray = Array.from({ length: 1000 }, () => createTestNote());
      
      const startTime = performance.now();
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Process the large array
      const results = largeArray.map(note => validateActivityPubObject(note));
      
      const endTime = performance.now();
      const finalMemory = process.memoryUsage().heapUsed;
      
      const duration = endTime - startTime;
      const memoryIncrease = (finalMemory - initialMemory) / 1024; // KB
      
      expect(results.every(r => r.valid)).toBe(true);
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
      
      console.log(`Large array processing: ${duration.toFixed(2)}ms, memory: ${memoryIncrease.toFixed(2)}KB`);
    });
  });

  describe('Stress Tests', () => {
    it('should handle high-frequency validation', () => {
      const note = createTestNote();
      const iterations = 10000;
      
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        validateActivityPubObject(note);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const opsPerSecond = (iterations / duration) * 1000;
      
      // Should handle at least 1000 validations per second
      expect(opsPerSecond).toBeGreaterThan(1000);
      console.log(`High-frequency validation: ${opsPerSecond.toFixed(0)} ops/second`);
    });

    it('should handle deep object nesting', () => {
      // Create deeply nested object
      let deepObject: any = { type: 'Note', id: 'test' };
      let current = deepObject;
      
      for (let i = 0; i < 100; i++) {
        current.nested = { level: i, type: 'Object' };
        current = current.nested;
      }
      
      const startTime = performance.now();
      
      const result = validateActivityPubObject(deepObject);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(result).toBeDefined();
      expect(duration).toBeLessThan(100); // Should handle deep nesting reasonably
      console.log(`Deep nesting validation: ${duration.toFixed(3)}ms (100 levels deep)`);
    });

    it('should handle wide object structures', () => {
      // Create object with many properties
      const wideObject = {
        type: 'Note',
        id: 'test',
        ...Object.fromEntries(
          Array.from({ length: 1000 }, (_, i) => [`prop${i}`, `value${i}`])
        )
      };
      
      const startTime = performance.now();
      
      const result = validateActivityPubObject(wideObject);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(result).toBeDefined();
      expect(duration).toBeLessThan(50); // Should handle wide objects efficiently
      console.log(`Wide object validation: ${duration.toFixed(3)}ms (1000 properties)`);
    });
  });

  describe('Real-world Performance Scenarios', () => {
    it('should handle typical social media post processing', async () => {
      const socialPost = {
        type: 'Create',
        id: 'https://example.com/activities/123',
        actor: 'https://example.com/users/alice',
        published: new Date().toISOString(),
        object: {
          type: 'Note',
          id: 'https://example.com/notes/456',
          content: 'Just had an amazing day at the beach! 🏖️ #summer #vacation',
          attributedTo: 'https://example.com/users/alice',
          published: new Date().toISOString(),
          tag: [
            { type: 'Hashtag', name: '#summer' },
            { type: 'Hashtag', name: '#vacation' }
          ],
          to: ['https://www.w3.org/ns/activitystreams#Public'],
          cc: ['https://example.com/users/alice/followers']
        }
      };
      
      const iterations = 100;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        // Simulate full processing pipeline
        const validation = validateActivityPubObject(socialPost);
        const normalization = normalizeAPObject(socialPost);
        
        expect(validation.valid).toBe(true);
        expect(normalization.value).toBeDefined();
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / iterations;
      
      expect(avgTime).toBeLessThan(5);
      console.log(`Social post processing: ${avgTime.toFixed(3)}ms per post (${iterations} posts)`);
    });

    it('should handle federation inbox processing simulation', async () => {
      const inboxActivities = Array.from({ length: 50 }, (_, i) => ({
        ...createTestFollowActivity(`https://example.com/users/user${i}`),
        id: `https://example.com/activities/${i}`,
        actor: `https://remote${i % 5}.example/users/user${i}`
      }));
      
      const startTime = performance.now();
      
      // Simulate inbox processing
      const results = await Promise.all(
        inboxActivities.map(async (activity) => {
          // Validate
          const validation = validateActivityPubObject(activity);
          if (!validation.valid) return false;
          
          // Normalize
          const normalization = normalizeAPObject(activity);
          
          // Process
          return processActivityBase(normalization.value as any, {
            requiredFields: ['actor', 'object'],
            extractUrls: (activity) => ({
              actorUri: activity.actor as string,
              objectUri: activity.object as string
            }),
            processActivity: async () => {
              // Simulate database write
              await new Promise(resolve => setTimeout(resolve, 1));
              return true;
            },
            activityName: 'Follow'
          });
        })
      );
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      const avgTime = duration / inboxActivities.length;
      
      expect(results.every(r => r === true)).toBe(true);
      console.log(`Inbox processing: ${avgTime.toFixed(3)}ms per activity (${inboxActivities.length} activities)`);
    });
  });
});
