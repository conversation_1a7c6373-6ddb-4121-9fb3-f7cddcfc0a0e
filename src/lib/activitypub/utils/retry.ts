/**
 * Universal retry mechanisms for ActivityPub operations
 */

import { rateLimitedFetch } from '../rateLimiter';
import { fetchWithServiceSignature } from './http-client';
import {
  NetworkError,
  TimeoutError,
  RateLimitError,
  FetchError,
  type ActivityPubError
} from './errors';

/**
 * Retry strategy configuration
 */
export interface RetryStrategy {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryableErrors: string[];
  retryableStatusCodes: number[];
}

/**
 * Retry context for tracking attempts
 */
export interface RetryContext {
  attempt: number;
  totalAttempts: number;
  lastError?: Error;
  startTime: number;
  delays: number[];
}

/**
 * Retry result with metrics
 */
export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  context: RetryContext;
  totalTime: number;
}

/**
 * Predefined retry strategies
 */
export const RetryStrategies = {
  /**
   * Conservative strategy for critical operations
   */
  conservative: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    jitter: true,
    retryableErrors: ['NetworkError', 'TimeoutError', 'FetchError'],
    retryableStatusCodes: [408, 429, 500, 502, 503, 504]
  } as RetryStrategy,

  /**
   * Aggressive strategy for non-critical operations
   */
  aggressive: {
    maxAttempts: 5,
    baseDelay: 500,
    maxDelay: 30000,
    backoffMultiplier: 1.5,
    jitter: true,
    retryableErrors: ['NetworkError', 'TimeoutError', 'FetchError', 'RateLimitError'],
    retryableStatusCodes: [408, 429, 500, 502, 503, 504, 520, 521, 522, 523, 524]
  } as RetryStrategy,

  /**
   * Fast strategy for real-time operations
   */
  fast: {
    maxAttempts: 2,
    baseDelay: 200,
    maxDelay: 2000,
    backoffMultiplier: 2,
    jitter: false,
    retryableErrors: ['TimeoutError'],
    retryableStatusCodes: [408, 502, 503, 504]
  } as RetryStrategy,

  /**
   * Patient strategy for background operations
   */
  patient: {
    maxAttempts: 10,
    baseDelay: 2000,
    maxDelay: 60000,
    backoffMultiplier: 1.2,
    jitter: true,
    retryableErrors: ['NetworkError', 'TimeoutError', 'FetchError', 'RateLimitError'],
    retryableStatusCodes: [408, 429, 500, 502, 503, 504, 520, 521, 522, 523, 524]
  } as RetryStrategy
} as const;

/**
 * Check if an error is retryable based on strategy
 */
export function isRetryableError(error: Error, strategy: RetryStrategy): boolean {
  // Check error type
  const errorName = error.constructor.name;
  if (strategy.retryableErrors.includes(errorName)) {
    return true;
  }

  // Check status codes for HTTP errors
  if (error instanceof FetchError && error.status) {
    return strategy.retryableStatusCodes.includes(error.status);
  }

  // Special handling for rate limit errors
  if (error instanceof RateLimitError) {
    return strategy.retryableErrors.includes('RateLimitError');
  }

  return false;
}

/**
 * Calculate delay for next retry attempt
 */
export function calculateDelay(
  attempt: number, 
  strategy: RetryStrategy
): number {
  let delay = strategy.baseDelay * Math.pow(strategy.backoffMultiplier, attempt - 1);
  
  // Apply maximum delay limit
  delay = Math.min(delay, strategy.maxDelay);
  
  // Add jitter to prevent thundering herd
  if (strategy.jitter) {
    const jitterAmount = delay * 0.1; // 10% jitter
    delay += (Math.random() - 0.5) * 2 * jitterAmount;
  }
  
  return Math.max(0, Math.floor(delay));
}

/**
 * Sleep for specified duration
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Universal retry function
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  strategy: RetryStrategy = RetryStrategies.conservative,
  onRetry?: (context: RetryContext) => void
): Promise<RetryResult<T>> {
  const context: RetryContext = {
    attempt: 0,
    totalAttempts: strategy.maxAttempts,
    startTime: Date.now(),
    delays: []
  };

  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= strategy.maxAttempts; attempt++) {
    context.attempt = attempt;

    try {
      const result = await operation();
      
      return {
        success: true,
        result,
        context,
        totalTime: Date.now() - context.startTime
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      context.lastError = lastError;

      // Don't retry on last attempt
      if (attempt === strategy.maxAttempts) {
        break;
      }

      // Check if error is retryable
      if (!isRetryableError(lastError, strategy)) {
        break;
      }

      // Calculate and apply delay
      const delay = calculateDelay(attempt, strategy);
      context.delays.push(delay);

      // Call retry callback if provided
      if (onRetry) {
        onRetry(context);
      }

      // Wait before next attempt
      if (delay > 0) {
        await sleep(delay);
      }
    }
  }

  return {
    success: false,
    error: lastError,
    context,
    totalTime: Date.now() - context.startTime
  };
}

/**
 * Retry wrapper for fetch operations
 */
export async function retryFetch(
  url: string,
  options?: RequestInit,
  strategy: RetryStrategy = RetryStrategies.conservative
): Promise<RetryResult<Response>> {
  return withRetry(
    async () => {
      const response = await rateLimitedFetch(url, options);
      
      // Check for HTTP errors
      if (!response.ok) {
        throw new FetchError(
          `HTTP ${response.status}: ${response.statusText}`,
          'FETCH_ERROR',
          { url, status: response.status, statusText: response.statusText }
        );
      }
      
      return response;
    },
    strategy,
    (context) => {
      console.warn(
        `Retrying fetch to ${url} (attempt ${context.attempt}/${context.totalAttempts})`,
        { error: context.lastError?.message, delay: context.delays[context.delays.length - 1] }
      );
    }
  );
}

/**
 * Retry wrapper for ActivityPub object fetching
 */
export async function retryActivityPubFetch<T>(
  url: string,
  processor: (response: Response) => Promise<T>,
  options?: RequestInit,
  strategy: RetryStrategy = RetryStrategies.conservative
): Promise<RetryResult<T>> {
  return withRetry(
    async () => {
      const response = await fetchWithServiceSignature(url, {
        ...options,
        headers: {
          'Accept': 'application/activity+json, application/ld+json; profile="https://www.w3.org/ns/activitystreams"',
          ...options?.headers
        }
      });
      
      if (!response.ok) {
        throw new FetchError(
          `HTTP ${response.status}: ${response.statusText}`,
          'FETCH_ERROR',
          { url, status: response.status, statusText: response.statusText }
        );
      }
      
      return await processor(response);
    },
    strategy,
    (context) => {
      console.warn(
        `Retrying ActivityPub fetch to ${url} (attempt ${context.attempt}/${context.totalAttempts})`,
        { error: context.lastError?.message, delay: context.delays[context.delays.length - 1] }
      );
    }
  );
}

/**
 * Batch retry operations with concurrency control
 */
export async function retryBatch<T>(
  operations: Array<() => Promise<T>>,
  strategy: RetryStrategy = RetryStrategies.conservative,
  options: {
    maxConcurrency?: number;
    continueOnError?: boolean;
    onProgress?: (completed: number, total: number) => void;
  } = {}
): Promise<{
  results: Array<RetryResult<T>>;
  successful: number;
  failed: number;
  totalTime: number;
}> {
  const { maxConcurrency = 5, continueOnError = true, onProgress } = options;
  const startTime = Date.now();
  const results: Array<RetryResult<T>> = [];
  
  // Process operations in batches
  for (let i = 0; i < operations.length; i += maxConcurrency) {
    const batch = operations.slice(i, i + maxConcurrency);
    
    const batchPromises = batch.map(operation => 
      withRetry(operation, strategy)
    );
    
    const batchResults = await Promise.allSettled(batchPromises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          success: false,
          error: result.reason,
          context: {
            attempt: 1,
            totalAttempts: 1,
            startTime: Date.now(),
            delays: []
          },
          totalTime: 0
        });
      }
      
      // Call progress callback
      if (onProgress) {
        onProgress(results.length, operations.length);
      }
      
      // Stop on first error if not continuing
      if (!continueOnError && !results[results.length - 1].success) {
        break;
      }
    }
    
    // Stop processing if we hit an error and not continuing
    if (!continueOnError && results.some(r => !r.success)) {
      break;
    }
  }
  
  const successful = results.filter(r => r.success).length;
  const failed = results.length - successful;
  
  return {
    results,
    successful,
    failed,
    totalTime: Date.now() - startTime
  };
}

/**
 * Circuit breaker pattern for retry operations
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(
    operation: () => Promise<T>,
    strategy: RetryStrategy = RetryStrategies.fast
  ): Promise<RetryResult<T>> {
    // Check circuit breaker state
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
        return {
          success: false,
          error: new Error('Circuit breaker is open'),
          context: {
            attempt: 0,
            totalAttempts: 0,
            startTime: Date.now(),
            delays: []
          },
          totalTime: 0
        };
      } else {
        this.state = 'half-open';
      }
    }

    const result = await withRetry(operation, strategy);

    // Update circuit breaker state based on result
    if (result.success) {
      this.failures = 0;
      this.state = 'closed';
    } else {
      this.failures++;
      this.lastFailureTime = Date.now();

      if (this.failures >= this.failureThreshold) {
        this.state = 'open';
      }
    }

    return result;
  }

  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }

  reset(): void {
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = 'closed';
  }
}

/**
 * Retry metrics collector
 */
export class RetryMetrics {
  private metrics = new Map<string, {
    attempts: number;
    successes: number;
    failures: number;
    totalTime: number;
    avgDelay: number;
  }>();

  record(operation: string, result: RetryResult<any>): void {
    const existing = this.metrics.get(operation) || {
      attempts: 0,
      successes: 0,
      failures: 0,
      totalTime: 0,
      avgDelay: 0
    };

    existing.attempts += result.context.attempt;
    existing.totalTime += result.totalTime;

    if (result.success) {
      existing.successes++;
    } else {
      existing.failures++;
    }

    // Calculate average delay
    const totalDelays = result.context.delays.reduce((sum, delay) => sum + delay, 0);
    existing.avgDelay = (existing.avgDelay + totalDelays) / 2;

    this.metrics.set(operation, existing);
  }

  getMetrics(operation?: string): Record<string, any> {
    if (operation) {
      return this.metrics.get(operation) || {};
    }

    return Object.fromEntries(this.metrics.entries());
  }

  reset(operation?: string): void {
    if (operation) {
      this.metrics.delete(operation);
    } else {
      this.metrics.clear();
    }
  }
}

/**
 * Global retry metrics instance
 */
export const globalRetryMetrics = new RetryMetrics();

/**
 * Enhanced retry function with metrics and circuit breaker
 */
export async function withEnhancedRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  strategy: RetryStrategy = RetryStrategies.conservative,
  circuitBreaker?: CircuitBreaker
): Promise<RetryResult<T>> {
  const executeOperation = circuitBreaker
    ? () => circuitBreaker.execute(operation, strategy)
    : () => withRetry(operation, strategy);

  const result = await executeOperation();

  // Record metrics
  globalRetryMetrics.record(operationName, result);

  return result;
}

/**
 * Retry configuration for different ActivityPub operations
 */
export const ActivityPubRetryConfigs = {
  actorFetch: RetryStrategies.conservative,
  postFetch: RetryStrategies.aggressive,
  collectionFetch: RetryStrategies.patient,
  webfingerLookup: RetryStrategies.fast,
  signatureVerification: RetryStrategies.conservative,
  delivery: RetryStrategies.patient
} as const;
