/**
 * Delivery scheduler for ActivityPub activities.
 * Manages automatic processing of delivery queues and retry scheduling.
 */

import { processDeliveryQueue, cleanupOldDeliveries, getDeliveryStats } from './delivery';
import { ActivityPubLogs } from './logger';
import { getFederationConfig } from '$lib/activitypub/config/federation';

/**
 * Scheduler configuration
 */
export interface SchedulerConfig {
  enabled: boolean;
  intervalMs: number;
  batchSize: number;
  maxConcurrency: number;
  cleanupIntervalMs: number;
  cleanupOlderThanDays: number;
  statsIntervalMs: number;
}

/**
 * Default scheduler configuration
 */
const DEFAULT_SCHEDULER_CONFIG: SchedulerConfig = {
  enabled: true,
  intervalMs: 30000, // 30 seconds
  batchSize: 20,
  maxConcurrency: 5,
  cleanupIntervalMs: 3600000, // 1 hour
  cleanupOlderThanDays: 30,
  statsIntervalMs: 300000 // 5 minutes
};

/**
 * Delivery scheduler class
 */
export class DeliveryScheduler {
  private config: SchedulerConfig;
  private processingInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private statsInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private userId: string;

  constructor(userId: string, config: Partial<SchedulerConfig> = {}) {
    this.userId = userId;
    this.config = { ...DEFAULT_SCHEDULER_CONFIG, ...config };
  }

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      ActivityPubLogs.federation.schedulerAlreadyRunning();
      return;
    }

    if (!this.config.enabled) {
      ActivityPubLogs.federation.schedulerDisabled();
      return;
    }

    this.isRunning = true;
    ActivityPubLogs.federation.schedulerStarted(this.config);

    // Start processing interval
    this.processingInterval = setInterval(
      () => this.processQueue(),
      this.config.intervalMs
    );

    // Start cleanup interval
    this.cleanupInterval = setInterval(
      () => this.performCleanup(),
      this.config.cleanupIntervalMs
    );

    // Start stats interval
    this.statsInterval = setInterval(
      () => this.logStats(),
      this.config.statsIntervalMs
    );

    // Process queue immediately
    this.processQueue();
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    ActivityPubLogs.federation.schedulerStopped();

    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    if (this.statsInterval) {
      clearInterval(this.statsInterval);
      this.statsInterval = null;
    }
  }

  /**
   * Check if scheduler is running
   */
  isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Update scheduler configuration
   */
  updateConfig(newConfig: Partial<SchedulerConfig>): void {
    const wasRunning = this.isRunning;
    
    if (wasRunning) {
      this.stop();
    }

    this.config = { ...this.config, ...newConfig };
    ActivityPubLogs.federation.schedulerConfigUpdated(this.config);

    if (wasRunning && this.config.enabled) {
      this.start();
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): SchedulerConfig {
    return { ...this.config };
  }

  /**
   * Process delivery queue
   */
  private async processQueue(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      const result = await processDeliveryQueue(
        this.userId,
        this.config.batchSize,
        this.config.maxConcurrency
      );

      if (result.processed > 0) {
        ActivityPubLogs.federation.schedulerProcessedBatch(
          result.processed,
          result.delivered,
          result.failed,
          result.permanentFailures
        );
      }

      // Log errors if any
      if (result.errors.length > 0) {
        ActivityPubLogs.federation.schedulerBatchErrors(result.errors);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.federation.schedulerProcessingError(errorMessage);
    }
  }

  /**
   * Perform cleanup of old delivery records
   */
  private async performCleanup(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      const deletedCount = await cleanupOldDeliveries(this.config.cleanupOlderThanDays);
      
      if (deletedCount > 0) {
        ActivityPubLogs.federation.schedulerCleanupCompleted(
          deletedCount,
          this.config.cleanupOlderThanDays
        );
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.federation.schedulerCleanupError(errorMessage);
    }
  }

  /**
   * Log delivery statistics
   */
  private async logStats(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      const stats = await getDeliveryStats();
      ActivityPubLogs.federation.schedulerStats(stats);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.federation.schedulerStatsError(errorMessage);
    }
  }

  /**
   * Force immediate processing of delivery queue
   */
  async processNow(): Promise<void> {
    if (!this.isRunning) {
      throw new Error('Scheduler is not running');
    }

    await this.processQueue();
  }

  /**
   * Force immediate cleanup
   */
  async cleanupNow(): Promise<number> {
    if (!this.isRunning) {
      throw new Error('Scheduler is not running');
    }

    return await cleanupOldDeliveries(this.config.cleanupOlderThanDays);
  }
}

/**
 * Global scheduler instance
 */
let globalScheduler: DeliveryScheduler | null = null;

/**
 * Initialize global delivery scheduler
 */
export function initializeDeliveryScheduler(
  userId: string,
  config: Partial<SchedulerConfig> = {}
): DeliveryScheduler {
  if (globalScheduler) {
    globalScheduler.stop();
  }

  globalScheduler = new DeliveryScheduler(userId, config);
  return globalScheduler;
}

/**
 * Get global scheduler instance
 */
export function getDeliveryScheduler(): DeliveryScheduler | null {
  return globalScheduler;
}

/**
 * Start global delivery scheduler
 */
export function startDeliveryScheduler(
  userId: string,
  config: Partial<SchedulerConfig> = {}
): void {
  const scheduler = initializeDeliveryScheduler(userId, config);
  scheduler.start();
}

/**
 * Stop global delivery scheduler
 */
export function stopDeliveryScheduler(): void {
  if (globalScheduler) {
    globalScheduler.stop();
    globalScheduler = null;
  }
}

/**
 * Create scheduler configuration from federation config
 */
export function createSchedulerConfigFromFederation(): Partial<SchedulerConfig> {
  const federationConfig = getFederationConfig();
  
  return {
    batchSize: federationConfig.delivery.concurrency * 2,
    maxConcurrency: federationConfig.delivery.concurrency,
    intervalMs: 30000, // 30 seconds
    cleanupIntervalMs: 3600000, // 1 hour
    statsIntervalMs: 300000 // 5 minutes
  };
}
