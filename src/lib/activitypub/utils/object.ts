/**
 * Improved utilities for processing ActivityPub objects.
 * This module provides safe, immutable, and well-typed functions for object processing.
 */

import type {
  Collection,
  CollectionPage,
  CollectionPageReference,
  CollectionReference,
  CoreObject,
  Entity,
  EntityReference,
  Link,
  OrderedCollectionPage,
  OrderedCollectionPageReference,
  OrderedCollectionReference
} from '$lib/activitypub/types';
import { narrowByType } from '$lib/activitypub/types';
import { rateLimitedFetch } from '$lib/activitypub/rateLimiter';
import { getUrlFromAPObjectSafe } from './links';

// Error types for better error handling
export class ObjectProcessingError extends Error {
  constructor(message: string, public readonly cause?: unknown) {
    super(message);
    this.name = 'ObjectProcessingError';
  }
}

export class CircularReferenceError extends ObjectProcessingError {
  constructor(path: string) {
    super(`Circular reference detected at path: ${path}`);
    this.name = 'CircularReferenceError';
  }
}

// Processing options
export interface ProcessingOptions {
  maxDepth?: number;
  preserveOriginal?: boolean;
  skipValidation?: boolean;
  processUrls?: boolean;
  processDates?: boolean;
}

const DEFAULT_OPTIONS: Required<ProcessingOptions> = {
  maxDepth: 10,
  preserveOriginal: true,
  skipValidation: false,
  processUrls: true,
  processDates: true,
};

// URL regex pattern
const URL_PATTERN = /^(https?:)?\/\/.+/;

// ISO 8601 date pattern (more precise than original)
const ISO_DATE_PATTERN = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?([+-]\d{2}:\d{2}|Z)$/;

/**
 * Main function to process ActivityPub objects with improved error handling and immutability.
 */
export function processAPObject<T extends CoreObject | Entity>(
  object: T,
  options: ProcessingOptions = {}
): T {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  try {
    // Create a deep copy if preserveOriginal is true
    const workingObject = opts.preserveOriginal ? deepClone(object) : object;
    
    // Track visited objects to prevent circular references
    const visited = new WeakSet();
    
    let result = workingObject;
    
    if (opts.processUrls) {
      result = processUrlsInAPObjectSafe(result, visited, opts.maxDepth, '');
    }
    
    if (opts.processDates) {
      result = processDatesInAPObjectSafe(result, visited, opts.maxDepth, '');
    }
    
    return result;
  } catch (error) {
    throw new ObjectProcessingError(
      `Failed to process ActivityPub object: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    );
  }
}

/**
 * Safe URL processing with circular reference detection and depth limiting.
 */
function processUrlsInAPObjectSafe<T>(
  object: T,
  visited: WeakSet<object>,
  maxDepth: number,
  path: string
): T {
  if (maxDepth <= 0) {
    return object;
  }

  // Handle primitive types
  if (typeof object === 'string') {
    if (URL_PATTERN.test(object)) {
      try {
        const url = new URL(object);
        // Only return URL if T could reasonably be a URL or unknown type
        return url as unknown as T;
      } catch {
        // If URL construction fails, return original string
        return object;
      }
    }
    return object;
  }

  if (object === null || typeof object !== 'object') {
    return object;
  }

  // Check for circular references
  if (visited.has(object as object)) {
    throw new CircularReferenceError(path);
  }

  visited.add(object as object);

  try {
    if (Array.isArray(object)) {
      return object.map((item, index) =>
        processUrlsInAPObjectSafe(item, visited, maxDepth - 1, `${path}[${index}]`)
      ) as unknown as T;
    }

    // Handle URL objects
    if (object instanceof URL) {
      return object;
    }

    // Process object properties
    const result = {} as T;
    for (const [key, value] of Object.entries(object)) {
      if (typeof value === 'string' && URL_PATTERN.test(value)) {
        try {
          (result as any)[key] = new URL(value);
        } catch {
          // If URL construction fails, keep original value
          (result as any)[key] = value;
        }
      } else if (value !== null && typeof value === 'object') {
        (result as any)[key] = processUrlsInAPObjectSafe(
          value,
          visited,
          maxDepth - 1,
          `${path}.${key}`
        );
      } else {
        (result as any)[key] = value;
      }
    }

    return result;
  } finally {
    visited.delete(object as object);
  }
}

/**
 * Safe date processing with circular reference detection and depth limiting.
 */
function processDatesInAPObjectSafe<T>(
  object: T,
  visited: WeakSet<object>,
  maxDepth: number,
  path: string
): T {
  if (maxDepth <= 0) {
    return object;
  }

  // Handle primitive types
  if (typeof object === 'string') {
    if (ISO_DATE_PATTERN.test(object)) {
      try {
        const date = new Date(object);
        // Validate that the date is valid
        if (isNaN(date.getTime())) {
          return object; // Return original string if date is invalid
        }
        // Only return Date if T could reasonably be a Date or unknown type
        return date as unknown as T;
      } catch {
        // If Date construction fails, return original string
        return object;
      }
    }
    return object;
  }

  if (object === null || typeof object !== 'object') {
    return object;
  }

  // Check for circular references
  if (visited.has(object as object)) {
    throw new CircularReferenceError(path);
  }

  visited.add(object as object);

  try {
    if (Array.isArray(object)) {
      return object.map((item, index) =>
        processDatesInAPObjectSafe(item, visited, maxDepth - 1, `${path}[${index}]`)
      ) as unknown as T;
    }

    // Handle Date objects and URLs
    if (object instanceof Date || object instanceof URL) {
      return object;
    }

    // Process object properties
    const result = {} as T;
    for (const [key, value] of Object.entries(object)) {
      if (typeof value === 'string' && ISO_DATE_PATTERN.test(value)) {
        try {
          (result as any)[key] = new Date(value);
        } catch {
          // If Date construction fails, keep original value
          (result as any)[key] = value;
        }
      } else if (value !== null && typeof value === 'object') {
        (result as any)[key] = processDatesInAPObjectSafe(
          value,
          visited,
          maxDepth - 1,
          `${path}.${key}`
        );
      } else {
        (result as any)[key] = value;
      }
    }

    return result;
  } finally {
    visited.delete(object as object);
  }
}

/**
 * Deep clone utility for preserving original objects.
 */
function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }

  if (obj instanceof URL) {
    return new URL(obj.toString()) as unknown as T;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  const cloned = {} as T;
  for (const [key, value] of Object.entries(obj)) {
    (cloned as any)[key] = deepClone(value);
  }

  return cloned;
}

/**
 * Legacy function wrappers for backward compatibility.
 * These maintain the original API but use the improved implementation.
 */
export function processUrlsInAPObject<T extends CoreObject | Entity | string>(object: T): T {
  return processUrlsInAPObjectSafe(object, new WeakSet(), DEFAULT_OPTIONS.maxDepth, '');
}

export function processDatesInAPObject<T extends CoreObject | Entity | string>(object: T): T {
  return processDatesInAPObjectSafe(object, new WeakSet(), DEFAULT_OPTIONS.maxDepth, '');
}

/**
 * Improved collection utilities with better error handling.
 */
export async function getCollectionTotalItems(
  collection: CollectionReference | OrderedCollectionReference
): Promise<number> {
  try {
    let collectionObj: Collection | OrderedCollectionReference = collection;

    if (collection instanceof URL) {
      const response = await rateLimitedFetch(collection.toString(), {
        headers: {
          accept: 'application/activity+json'
        }
      });

      if (!response.ok) {
        throw new ObjectProcessingError(`Failed to fetch collection: ${response.status} ${response.statusText}`);
      }

      collectionObj = await response.json() as Collection;
      collectionObj = processAPObject(collectionObj);
    }

    // Type guard to ensure we have a collection object, not a URL
    if (collectionObj instanceof URL) {
      throw new ObjectProcessingError('Collection object is still a URL after processing');
    }

    const totalItems = collectionObj.totalItems ?? 0;

    if (totalItems === 0 && collectionObj.first) {
      return await getCollectionItemsPageByPage(collectionObj.first);
    }

    return totalItems;
  } catch (error) {
    throw new ObjectProcessingError(
      `Failed to get collection total items: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    );
  }
}

export async function getCollectionItemsPageByPage(
  collectionPage: CollectionPageReference | OrderedCollectionPageReference | Link,
  totalItems: number = 0,
  maxPages: number = 100 // Prevent infinite loops
): Promise<number> {
  if (maxPages <= 0) {
    throw new ObjectProcessingError('Maximum page limit reached');
  }

  try {
    let pageObj = collectionPage;

    if ('type' in collectionPage && collectionPage.type === 'Link') {
      const link = getUrlFromAPObjectSafe(collectionPage);
      if (!link) {
        return totalItems;
      }
      pageObj = new URL(link);
    }

    if (pageObj instanceof URL) {
      const response = await rateLimitedFetch(pageObj.toString(), {
        headers: {
          accept: 'application/activity+json'
        }
      });

      if (!response.ok) {
        throw new ObjectProcessingError(`Failed to fetch collection page: ${response.status} ${response.statusText}`);
      }
      
      pageObj = await response.json() as (CollectionPage | OrderedCollectionPage);
      pageObj = processAPObject(pageObj);
    }

    if (!narrowByType<CollectionPage | OrderedCollectionPage>(pageObj, ['CollectionPage', 'OrderedCollectionPage'])) {
      return totalItems;
    }

    let items = pageObj.orderedItems ?? pageObj.items;
    if (!Array.isArray(items)) {
      items = items ? [items as EntityReference] : [];
    }
    
    totalItems += items.length;
    
    if (pageObj.next) {
      return await getCollectionItemsPageByPage(pageObj.next, totalItems, maxPages - 1);
    }
    
    return totalItems;
  } catch (error) {
    throw new ObjectProcessingError(
      `Failed to process collection page: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    );
  }
}
