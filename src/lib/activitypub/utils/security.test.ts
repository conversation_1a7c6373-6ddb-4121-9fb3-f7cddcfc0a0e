import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  escapeHtml,
  isSecureUrl,
  isSafeContent
} from './security.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

describe('ActivityPub Utils - Security', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('escapeHtml', () => {
    it('should escape basic HTML characters', () => {
      const input = '<script>alert("xss")</script>';
      const expected = '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;';
      
      const result = escapeHtml(input);

      expect(result).toBe(expected);
    });

    it('should escape all dangerous characters', () => {
      const input = `<>&"'`;
      const expected = '&lt;&gt;&amp;&quot;&#039;';
      
      const result = escapeHtml(input);

      expect(result).toBe(expected);
    });

    it('should handle empty string', () => {
      const result = escapeHtml('');

      expect(result).toBe('');
    });

    it('should handle string without special characters', () => {
      const input = 'Hello world!';
      
      const result = escapeHtml(input);

      expect(result).toBe(input);
    });

    it('should handle mixed content', () => {
      const input = 'Hello <b>world</b> & "friends"!';
      const expected = 'Hello &lt;b&gt;world&lt;/b&gt; &amp; &quot;friends&quot;!';
      
      const result = escapeHtml(input);

      expect(result).toBe(expected);
    });

    it('should handle unicode characters', () => {
      const input = 'Hello 世界 & <émojis> 🎉';
      const expected = 'Hello 世界 &amp; &lt;émojis&gt; 🎉';
      
      const result = escapeHtml(input);

      expect(result).toBe(expected);
    });
  });

  describe('isSecureUrl', () => {
    it('should accept HTTPS URLs', () => {
      const urls = [
        'https://example.com',
        'https://example.com/path',
        'https://example.com:8080/path?query=value',
        'https://subdomain.example.com/path#fragment'
      ];

      urls.forEach(url => {
        expect(isSecureUrl(url)).toBe(true);
      });
    });

    it('should accept HTTP URLs', () => {
      const urls = [
        'http://example.com',
        'http://localhost:3000',
        'http://***********:8080/api'
      ];

      urls.forEach(url => {
        expect(isSecureUrl(url)).toBe(true);
      });
    });

    it('should reject dangerous protocols', () => {
      const dangerousUrls = [
        'javascript:alert("xss")',
        'data:text/html,<script>alert("xss")</script>',
        'file:///etc/passwd',
        'ftp://example.com/file',
        'mailto:<EMAIL>',
        'tel:+1234567890'
      ];

      dangerousUrls.forEach(url => {
        expect(isSecureUrl(url)).toBe(false);
      });
    });

    it('should reject malformed URLs', () => {
      const malformedUrls = [
        'not-a-url',
        'http://',
        'https://',
        '://example.com',
        'http://[invalid-ipv6',
        ''
      ];

      malformedUrls.forEach(url => {
        expect(isSecureUrl(url)).toBe(false);
      });
    });

    it('should handle relative URLs', () => {
      const relativeUrls = [
        '/path/to/resource',
        '../relative/path',
        'relative/path',
        '?query=value',
        '#fragment'
      ];

      relativeUrls.forEach(url => {
        expect(isSecureUrl(url)).toBe(false);
      });
    });
  });

  describe('isSafeContent', () => {
    it('should accept safe content', () => {
      const safeContent = [
        'Hello world!',
        'This is a normal post with <b>bold</b> text.',
        'Check out this link: https://example.com',
        'Some unicode: 世界 🎉',
        '<p>Paragraph with <em>emphasis</em></p>'
      ];

      safeContent.forEach(content => {
        expect(isSafeContent(content)).toBe(true);
      });
    });

    it('should reject script tags', () => {
      const dangerousContent = [
        '<script>alert("xss")</script>',
        '<script src="evil.js"></script>',
        '<SCRIPT>alert("xss")</SCRIPT>',
        'Before <script>alert("xss")</script> after',
        '<script type="text/javascript">alert("xss")</script>'
      ];

      dangerousContent.forEach(content => {
        expect(isSafeContent(content)).toBe(false);
      });
    });

    it('should reject javascript: URLs', () => {
      const dangerousContent = [
        '<a href="javascript:alert(\'xss\')">Click me</a>',
        'javascript:alert("xss")',
        'JAVASCRIPT:alert("xss")',
        '<img src="x" onerror="javascript:alert(\'xss\')">'
      ];

      dangerousContent.forEach(content => {
        expect(isSafeContent(content)).toBe(false);
      });
    });

    it('should reject event handlers', () => {
      const dangerousContent = [
        '<div onclick="alert(\'xss\')">Click me</div>',
        '<img onload="alert(\'xss\')" src="image.jpg">',
        '<body onload="alert(\'xss\')">',
        '<input onfocus="alert(\'xss\')" type="text">',
        '<a onmouseover="alert(\'xss\')" href="#">Hover me</a>'
      ];

      dangerousContent.forEach(content => {
        expect(isSafeContent(content)).toBe(false);
      });
    });

    it('should reject dangerous tags', () => {
      const dangerousContent = [
        '<iframe src="evil.html"></iframe>',
        '<object data="evil.swf"></object>',
        '<embed src="evil.swf">',
        '<form action="evil.php"><input type="submit"></form>',
        '<IFRAME SRC="evil.html"></IFRAME>'
      ];

      dangerousContent.forEach(content => {
        expect(isSafeContent(content)).toBe(false);
      });
    });

    it('should handle mixed safe and dangerous content', () => {
      const mixedContent = [
        'Safe content <script>alert("xss")</script> more safe content',
        '<p>Normal paragraph</p><script>evil()</script>',
        'Hello <iframe src="evil"></iframe> world'
      ];

      mixedContent.forEach(content => {
        expect(isSafeContent(content)).toBe(false);
      });
    });

    it('should handle edge cases', () => {
      expect(isSafeContent('')).toBe(true);
      expect(isSafeContent('   ')).toBe(true);
      expect(isSafeContent('\n\t')).toBe(true);
    });
  });

  // Note: Additional security functions like sanitizeContent, validateContentSafety,
  // isAllowedDomain, checkRateLimit, and SecurityConfig would be tested here
  // if they exist in the actual security.ts file
});
