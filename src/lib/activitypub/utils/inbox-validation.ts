/**
 * Inbox request validation with HTTP signature support
 */

import { validateIncomingSignature, extractSignature } from './http-signature';
import { getFederationConfig } from '$lib/activitypub/config/federation';
import { isValidActivityPubObject, isValidActivity } from './validation';
import { isSafeContent, isSecureUrl } from './security';
import { ActivityPubLogs } from './logger';
import { extractActivityActor } from './activities';
import {
  SecurityError,
  SignatureVerificationError,
  UnsafeContentError,
  ValidationError,
  MalformedObjectError
} from './errors';
import type { InboxRequest, ActivityProcessingContext } from '$lib/activitypub/types/federation';
import type { Activity } from '$lib/activitypub/types';

/**
 * Validate incoming inbox request
 */
export async function validateInboxRequest(
  method: string,
  url: string,
  headers: Record<string, string>,
  rawBody: string,
  sourceIp?: string
): Promise<{
  valid: boolean;
  activity?: Activity;
  signature?: any;
  error?: string;
  statusCode?: number;
}> {
  try {
    // Check content size
    const federationConfig = getFederationConfig();
    if (rawBody.length > federationConfig.security.maxActivitySize) {
      ActivityPubLogs.security.blockedRequest(url, 'Activity size too large');
      return {
        valid: false,
        error: 'Activity size exceeds maximum allowed',
        statusCode: 413
      };
    }

    // Parse activity
    let activity: Activity;
    try {
      activity = JSON.parse(rawBody);
    } catch (error) {
      return {
        valid: false,
        error: 'Invalid JSON in request body',
        statusCode: 400
      };
    }

    // Validate ActivityPub object structure
    if (!isValidActivityPubObject(activity)) {
      return {
        valid: false,
        error: 'Invalid ActivityPub object',
        statusCode: 400
      };
    }

    // Validate as Activity
    if (!isValidActivity(activity)) {
      return {
        valid: false,
        error: 'Invalid Activity object',
        statusCode: 400
      };
    }

    // Check content safety
    const activityString = JSON.stringify(activity);
    if (!isSafeContent(activityString)) {
      const actorRef = extractActivityActor(activity);
      const actorId = actorRef ?
        (typeof actorRef === 'string' ? actorRef :
         actorRef instanceof URL ? actorRef.toString() :
         (actorRef as any).id?.toString() || 'unknown') : 'unknown';

      ActivityPubLogs.security.suspiciousActivity(
        actorId,
        'Unsafe content detected',
        { activity: activity.type }
      );
      throw new UnsafeContentError('Activity contains unsafe content');
    }

    // Validate HTTP signature
    const signatureResult = await validateIncomingSignature(method, url, headers, rawBody);
    
    if (!signatureResult.valid) {
      const federationConfig = getFederationConfig();
      if (federationConfig.security.requireHttpSignature) {
        return {
          valid: false,
          error: signatureResult.error || 'Signature validation failed',
          statusCode: 401
        };
      } else {
        // Log warning but continue
        ActivityPubLogs.security.signatureVerificationFailed(
          'unknown',
          url,
          signatureResult.error || 'No signature provided'
        );
      }
    }

    // Extract signature info
    const signature = extractSignature(headers);

    return {
      valid: true,
      activity,
      signature
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown validation error';
    
    if (error instanceof SecurityError) {
      return {
        valid: false,
        error: errorMessage,
        statusCode: 403
      };
    }

    if (error instanceof ValidationError) {
      return {
        valid: false,
        error: errorMessage,
        statusCode: 400
      };
    }

    ActivityPubLogs.security.suspiciousActivity(
      'unknown',
      'Inbox validation error',
      { error: errorMessage, url }
    );

    return {
      valid: false,
      error: 'Internal validation error',
      statusCode: 500
    };
  }
}

/**
 * Create processing context from validated request
 */
export function createInboxProcessingContext(
  activity: Activity,
  signature?: any,
  rawActivity?: string,
  sourceIp?: string
): ActivityProcessingContext {
  return {
    activity,
    direction: 'inbound',
    signature,
    rawActivity: rawActivity || JSON.stringify(activity),
    receivedAt: new Date()
  };
}

/**
 * Middleware for inbox request validation
 */
export function createInboxValidationMiddleware() {
  return async (request: Request, url: string) => {
    const method = request.method;
    const headers: Record<string, string> = {};
    
    // Extract headers
    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });

    // Get raw body
    const rawBody = await request.text();

    // Get source IP (if available)
    const sourceIp = headers['x-forwarded-for'] || headers['x-real-ip'];

    return validateInboxRequest(method, url, headers, rawBody, sourceIp);
  };
}