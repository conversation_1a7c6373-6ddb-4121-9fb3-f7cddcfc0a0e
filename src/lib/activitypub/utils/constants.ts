/**
 * Common constants for ActivityPub processing
 */

// Supported ActivityPub object types for posts
export const SUPPORTED_POST_TYPES = [
  'Note',
  'Question',
  'Article',
  'Page',
  'Image',
  'Audio',
  'Video',
  'Event'
] as const;

export type SupportedPostType = (typeof SUPPORTED_POST_TYPES)[number];

// ActivityPub collection types
export const COLLECTION_TYPES = ['Collection', 'OrderedCollection'] as const;
export const COLLECTION_PAGE_TYPES = ['CollectionPage', 'OrderedCollectionPage'] as const;

// ActivityPub activity types
export const ACTIVITY_TYPES = [
  'Create',
  'Update',
  'Delete',
  'Follow',
  'Accept',
  'Reject',
  'Add',
  'Remove',
  'Like',
  'Announce',
  'Undo'
] as const;

// Default processing limits
export const DEFAULT_PROCESSING_LIMITS = {
  maxDepth: 20,
  maxCollectionSize: 1000,
  timeout: 60000, // 30 seconds
  itemTimeout: 20000, // 5 seconds per item
  maxRetries: 3
} as const;

// Common HTTP headers for ActivityPub requests
export const ACTIVITYPUB_HEADERS = {
  accept: 'application/activity+json',
  'content-type': 'application/activity+json'
} as const;
