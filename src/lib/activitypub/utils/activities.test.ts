import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  processActivityBase,
  processActivitySafely,
  isCreateActivity,
  getActivitySummary,
  processActivityObjects,
  ActivityProcessors
} from './activities.js';
import {
  createTestCreateActivity,
  createTestFollowActivity,
  createTestBlockActivity,
  createTestAddActivity,
  createTestPerson
} from '../../../test/activitypub-helpers.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

// Mock dependencies
vi.mock('../actors', () => ({
  discoverActor: vi.fn()
}));

vi.mock('./types', () => ({
  getObjectType: vi.fn(),
  isActivityType: vi.fn(),
  validateObjectStructure: vi.fn()
}));

vi.mock('./links', () => ({
  validateUrl: vi.fn(),
  getUrlFromAPObjectSafe: vi.fn()
}));

vi.mock('./errors', () => ({
  ValidationError: class ValidationError extends Error {
    constructor(message: string, public code?: string) {
      super(message);
    }
  },
  MissingFieldError: class MissingFieldError extends Error {
    constructor(message: string) {
      super(message);
    }
  },
  InvalidTypeError: class InvalidTypeError extends Error {
    constructor(message: string) {
      super(message);
    }
  },
  MalformedObjectError: class MalformedObjectError extends Error {
    constructor(type: string, message: string) {
      super(`${type}: ${message}`);
    }
  }
}));

import { discoverActor } from '../actors';
import { getObjectType } from './types';
import { ValidationError, MalformedObjectError } from './errors';

describe('ActivityPub Utils - Activities', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('processActivityBase', () => {
    const mockActor = createTestPerson('https://example.com/users/alice');

    beforeEach(() => {
      vi.mocked(discoverActor).mockResolvedValue(mockActor);
    });

    it('should process a valid Follow activity', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      const mockProcessActivity = vi.fn().mockResolvedValue(true);

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob'
        }),
        processActivity: mockProcessActivity,
        activityName: 'Follow'
      });

      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/alice');
      expect(mockProcessActivity).toHaveBeenCalledWith(
        activity,
        expect.objectContaining({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob',
          actor: mockActor
        })
      );
    });

    it('should fail when required fields are missing', async () => {
      const activity = { type: 'Follow' }; // Missing actor and object

      const result = await processActivityBase(activity as any, {
        requiredFields: ['actor', 'object'],
        extractUrls: () => ({ actorUri: 'test' }),
        processActivity: vi.fn(),
        activityName: 'Follow'
      });

      expect(result).toBe(false);
      expect(discoverActor).not.toHaveBeenCalled();
    });

    it('should fail when actor discovery fails', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      vi.mocked(discoverActor).mockResolvedValue(null);

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob'
        }),
        processActivity: vi.fn(),
        activityName: 'Follow'
      });

      expect(result).toBe(false);
      expect(discoverActor).toHaveBeenCalledWith('https://example.com/users/alice');
    });

    it('should handle validation function', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      const mockValidateActivity = vi.fn().mockResolvedValue(false);
      const mockProcessActivity = vi.fn();

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob'
        }),
        validateActivity: mockValidateActivity,
        processActivity: mockProcessActivity,
        activityName: 'Follow'
      });

      expect(result).toBe(false);
      expect(mockValidateActivity).toHaveBeenCalled();
      expect(mockProcessActivity).not.toHaveBeenCalled();
    });

    it('should discover object actor when objectUri is provided', async () => {
      const activity = createTestAddActivity('https://example.com/note/1', 'https://example.com/collection/1');
      const mockObjectActor = createTestPerson('https://example.com/users/bob');
      
      vi.mocked(discoverActor)
        .mockResolvedValueOnce(mockActor) // First call for main actor
        .mockResolvedValueOnce(mockObjectActor); // Second call for object actor

      const mockProcessActivity = vi.fn().mockResolvedValue(true);

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object', 'target'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob',
          targetUri: 'https://example.com/collections/featured'
        }),
        processActivity: mockProcessActivity,
        activityName: 'Add'
      });

      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledTimes(2);
      expect(discoverActor).toHaveBeenNthCalledWith(1, 'https://example.com/users/alice');
      expect(discoverActor).toHaveBeenNthCalledWith(2, 'https://example.com/users/bob');
      
      expect(mockProcessActivity).toHaveBeenCalledWith(
        activity,
        expect.objectContaining({
          actor: mockActor,
          objectActor: mockObjectActor
        })
      );
    });

    it('should handle errors in processActivity function', async () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      const mockProcessActivity = vi.fn().mockRejectedValue(new Error('Processing failed'));

      await expect(processActivityBase(activity, {
        requiredFields: ['actor', 'object'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/users/bob'
        }),
        processActivity: mockProcessActivity,
        activityName: 'Follow'
      })).rejects.toThrow('Processing failed');
    });

    it('should handle object actor discovery failure gracefully', async () => {
      const activity = createTestAddActivity('https://example.com/note/1', 'https://example.com/collection/1');
      
      vi.mocked(discoverActor)
        .mockResolvedValueOnce(mockActor) // First call succeeds
        .mockRejectedValueOnce(new Error('Actor not found')); // Second call fails

      const mockProcessActivity = vi.fn().mockResolvedValue(true);

      const result = await processActivityBase(activity, {
        requiredFields: ['actor', 'object', 'target'],
        extractUrls: (activity) => ({
          actorUri: 'https://example.com/users/alice',
          objectUri: 'https://example.com/posts/123', // Not an actor
          targetUri: 'https://example.com/collections/featured'
        }),
        processActivity: mockProcessActivity,
        activityName: 'Add'
      });

      expect(result).toBe(true);
      expect(mockProcessActivity).toHaveBeenCalledWith(
        activity,
        expect.objectContaining({
          actor: mockActor,
          objectActor: null // Should be null when discovery fails
        })
      );
    });
  });

  describe('processActivitySafely', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockReturnValue('Create');
    });

    it('should return success for valid activity', () => {
      const activity = createTestCreateActivity();
      
      const result = processActivitySafely(activity);

      expect(result.success).toBe(false);
      if (result.success) {
        expect(result.activity).toEqual(activity);
      }
    });

    it('should return error for invalid activity structure', () => {
      const invalidActivity = null;
      
      const result = processActivitySafely(invalidActivity);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(MalformedObjectError);
      }
    });

    it('should return error for activity without type', () => {
      const invalidActivity = { actor: 'https://example.com/users/alice' };
      
      const result = processActivitySafely(invalidActivity);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(MalformedObjectError);
      }
    });
  });

  describe('isCreateActivity', () => {
    beforeEach(() => {
      vi.mocked(getObjectType).mockImplementation((obj) => obj.type);
    });

    it('should return true for Create activity', () => {
      const activity = createTestCreateActivity();
      
      const result = isCreateActivity(activity);

      expect(result).toBe(true);
    });

    it('should return false for non-Create activity', () => {
      const activity = createTestFollowActivity('https://example.com/users/bob');
      
      const result = isCreateActivity(activity);

      expect(result).toBe(false);
    });
  });
});
