/**
 * Debug utilities for ActivityPub operations
 */

import { logger, createTimer, generateCorrelationId, type LogContext } from './logger';
import { getDomainMetrics, getGlobalMetrics } from '../rateLimiter';
import type { Entity, CoreObject } from '$lib/activitypub/types';

/**
 * Debug levels
 */
export enum DebugLevel {
  MINIMAL = 0,
  BASIC = 1,
  DETAILED = 2,
  VERBOSE = 3
}

/**
 * Debug configuration
 */
export interface DebugConfig {
  level: DebugLevel;
  includeMetadata: boolean;
  includeTimestamps: boolean;
  includeStackTrace: boolean;
  maxDepth: number;
  colorOutput: boolean;
}

/**
 * Default debug configuration
 */
export const DEFAULT_DEBUG_CONFIG: DebugConfig = {
  level: DebugLevel.BASIC,
  includeMetadata: true,
  includeTimestamps: true,
  includeStackTrace: false,
  maxDepth: 5,
  colorOutput: true
};

/**
 * Request trace information
 */
export interface RequestTrace {
  id: string;
  url: string;
  method: string;
  headers: Record<string, string>;
  body?: any;
  startTime: number;
  endTime?: number;
  duration?: number;
  status?: number;
  response?: any;
  error?: string;
}

/**
 * ActivityPub object inspector
 */
export class APObjectInspector {
  private config: DebugConfig;

  constructor(config: DebugConfig = DEFAULT_DEBUG_CONFIG) {
    this.config = config;
  }

  /**
   * Inspect an ActivityPub object
   */
  inspect(obj: Entity | CoreObject, title?: string): string {
    const output: string[] = [];
    
    if (title) {
      output.push(this.formatTitle(title));
    }

    if (this.config.includeTimestamps) {
      output.push(`Timestamp: ${new Date().toISOString()}`);
    }

    output.push(this.formatObject(obj, 0));

    if (this.config.includeMetadata) {
      output.push(this.formatMetadata(obj));
    }

    return output.join('\n');
  }

  private formatTitle(title: string): string {
    const border = '='.repeat(Math.max(50, title.length + 4));
    return `${border}\n  ${title}\n${border}`;
  }

  private formatObject(obj: any, depth: number): string {
    if (depth > this.config.maxDepth) {
      return '[Max depth reached]';
    }

    if (obj === null || obj === undefined) {
      return String(obj);
    }

    if (typeof obj !== 'object') {
      return JSON.stringify(obj);
    }

    if (Array.isArray(obj)) {
      return this.formatArray(obj, depth);
    }

    return this.formatObjectProperties(obj, depth);
  }

  private formatArray(arr: any[], depth: number): string {
    if (arr.length === 0) return '[]';
    
    const items = arr.map(item => 
      '  '.repeat(depth + 1) + this.formatObject(item, depth + 1)
    );
    
    return `[\n${items.join(',\n')}\n${'  '.repeat(depth)}]`;
  }

  private formatObjectProperties(obj: Record<string, any>, depth: number): string {
    const entries = Object.entries(obj);
    if (entries.length === 0) return '{}';

    const props = entries.map(([key, value]) => {
      const indent = '  '.repeat(depth + 1);
      const formattedValue = this.formatObject(value, depth + 1);
      return `${indent}${key}: ${formattedValue}`;
    });

    return `{\n${props.join(',\n')}\n${'  '.repeat(depth)}}`;
  }

  private formatMetadata(obj: any): string {
    const metadata: string[] = [];
    
    metadata.push('\n--- Metadata ---');
    
    if (obj && typeof obj === 'object') {
      metadata.push(`Type: ${obj.type || 'Unknown'}`);
      metadata.push(`ID: ${obj.id || 'No ID'}`);
      
      if (obj.published) {
        metadata.push(`Published: ${obj.published}`);
      }
      
      if (obj.updated) {
        metadata.push(`Updated: ${obj.updated}`);
      }

      // Count properties
      const propCount = Object.keys(obj).length;
      metadata.push(`Properties: ${propCount}`);

      // Detect circular references
      try {
        JSON.stringify(obj);
        metadata.push('Circular references: None detected');
      } catch {
        metadata.push('Circular references: DETECTED');
      }
    }

    return metadata.join('\n');
  }
}

/**
 * Schema validator with detailed error reporting
 */
export class APSchemaValidator {
  /**
   * Validate ActivityPub object structure
   */
  validate(obj: any): ValidationResult {
    const errors: DebugValidationError[] = [];
    const warnings: DebugValidationWarning[] = [];

    // Basic structure validation
    if (!obj || typeof obj !== 'object') {
      errors.push({
        path: 'root',
        message: 'Object must be a non-null object',
        severity: 'error'
      });
      return { valid: false, errors, warnings };
    }

    // Required fields validation
    this.validateRequiredFields(obj, errors);
    
    // Type validation
    this.validateTypes(obj, errors, warnings);
    
    // URL validation
    this.validateUrls(obj, errors, warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  private validateRequiredFields(obj: any, errors: DebugValidationError[]): void {
    if (!obj.type) {
      errors.push({
        path: 'type',
        message: 'Missing required field: type',
        severity: 'error'
      });
    }

    // Context validation for ActivityPub objects
    if (!obj['@context'] && !obj.context) {
      errors.push({
        path: '@context',
        message: 'Missing ActivityPub context',
        severity: 'error'
      });
    }
  }

  private validateTypes(obj: any, errors: DebugValidationError[], warnings: DebugValidationWarning[]): void {
    const knownTypes = [
      'Person', 'Service', 'Organization', 'Application', 'Group',
      'Note', 'Article', 'Image', 'Video', 'Audio', 'Document',
      'Create', 'Update', 'Delete', 'Follow', 'Accept', 'Reject',
      'Like', 'Announce', 'Undo', 'Block', 'Flag',
      'Collection', 'OrderedCollection', 'CollectionPage', 'OrderedCollectionPage'
    ];

    if (obj.type && !knownTypes.includes(obj.type)) {
      warnings.push({
        path: 'type',
        message: `Unknown ActivityPub type: ${obj.type}`,
        severity: 'warning'
      });
    }
  }

  private validateUrls(obj: any, errors: DebugValidationError[], warnings: DebugValidationWarning[]): void {
    const urlFields = ['id', 'url', 'href', 'inbox', 'outbox', 'following', 'followers'];
    
    for (const field of urlFields) {
      if (obj[field] && typeof obj[field] === 'string') {
        try {
          new URL(obj[field]);
        } catch {
          errors.push({
            path: field,
            message: `Invalid URL format: ${obj[field]}`,
            severity: 'error'
          });
        }
      }
    }
  }
}

/**
 * Validation result
 */
export interface ValidationResult {
  valid: boolean;
  errors: DebugValidationError[];
  warnings: DebugValidationWarning[];
}

export interface DebugValidationError {
  path: string;
  message: string;
  severity: 'error';
}

export interface DebugValidationWarning {
  path: string;
  message: string;
  severity: 'warning';
}

/**
 * Request tracer for debugging HTTP operations
 */
export class RequestTracer {
  private traces = new Map<string, RequestTrace>();
  private config: DebugConfig;

  constructor(config: DebugConfig = DEFAULT_DEBUG_CONFIG) {
    this.config = config;
  }

  /**
   * Start tracing a request
   */
  startTrace(url: string, method: string = 'GET', headers: Record<string, string> = {}, body?: any): string {
    const id = generateCorrelationId();
    
    const trace: RequestTrace = {
      id,
      url,
      method,
      headers,
      body,
      startTime: Date.now()
    };

    this.traces.set(id, trace);

    if (this.config.level >= DebugLevel.DETAILED) {
      console.log(`[TRACE] Starting request ${id}: ${method} ${url}`);
    }

    return id;
  }

  /**
   * End tracing a request
   */
  endTrace(id: string, status?: number, response?: any, error?: string): RequestTrace | null {
    const trace = this.traces.get(id);
    if (!trace) return null;

    trace.endTime = Date.now();
    trace.duration = trace.endTime - trace.startTime;
    trace.status = status;
    trace.response = response;
    trace.error = error;

    if (this.config.level >= DebugLevel.DETAILED) {
      console.log(`[TRACE] Completed request ${id}: ${status || 'ERROR'} (${trace.duration}ms)`);
    }

    return trace;
  }

  /**
   * Get trace by ID
   */
  getTrace(id: string): RequestTrace | null {
    return this.traces.get(id) || null;
  }

  /**
   * Get all traces
   */
  getAllTraces(): RequestTrace[] {
    return Array.from(this.traces.values());
  }

  /**
   * Clear all traces
   */
  clearTraces(): void {
    this.traces.clear();
  }

  /**
   * Format trace for display
   */
  formatTrace(trace: RequestTrace): string {
    const output: string[] = [];
    
    output.push(`Request ID: ${trace.id}`);
    output.push(`URL: ${trace.url}`);
    output.push(`Method: ${trace.method}`);
    output.push(`Duration: ${trace.duration || 'N/A'}ms`);
    output.push(`Status: ${trace.status || 'N/A'}`);
    
    if (trace.error) {
      output.push(`Error: ${trace.error}`);
    }

    if (this.config.level >= DebugLevel.VERBOSE) {
      output.push(`Headers: ${JSON.stringify(trace.headers, null, 2)}`);
      
      if (trace.body) {
        output.push(`Request Body: ${JSON.stringify(trace.body, null, 2)}`);
      }
      
      if (trace.response) {
        output.push(`Response: ${JSON.stringify(trace.response, null, 2)}`);
      }
    }

    return output.join('\n');
  }
}

/**
 * Federation diagnostics
 */
export class FederationDiagnostics {
  /**
   * Diagnose federation issues with a domain
   */
  async diagnoseDomain(hostname: string): Promise<DiagnosticResult> {
    const result: DiagnosticResult = {
      hostname,
      timestamp: new Date().toISOString(),
      checks: [],
      overall: 'unknown'
    };

    // Check rate limiting status
    const rateLimitMetrics = getDomainMetrics(hostname);
    if (rateLimitMetrics) {
      result.checks.push({
        name: 'Rate Limiting',
        status: rateLimitMetrics.rateLimitHits > 0 ? 'warning' : 'ok',
        message: `${rateLimitMetrics.totalRequests} requests, ${rateLimitMetrics.rateLimitHits} rate limited`,
        details: rateLimitMetrics
      });
    }

    // Check DNS resolution
    try {
      const url = new URL(`https://${hostname}`);
      result.checks.push({
        name: 'DNS Resolution',
        status: 'ok',
        message: 'Hostname resolves correctly'
      });
    } catch {
      result.checks.push({
        name: 'DNS Resolution',
        status: 'error',
        message: 'Invalid hostname format'
      });
    }

    // Determine overall status
    const hasErrors = result.checks.some(check => check.status === 'error');
    const hasWarnings = result.checks.some(check => check.status === 'warning');
    
    result.overall = hasErrors ? 'error' : hasWarnings ? 'warning' : 'ok';

    return result;
  }

  /**
   * Get global federation health
   */
  getGlobalHealth(): GlobalHealth {
    const metrics = getGlobalMetrics();
    
    return {
      totalDomains: metrics.domainsTracked,
      totalRequests: metrics.totalRequests,
      totalRateLimitHits: metrics.totalRateLimitHits,
      averageResponseTime: metrics.averageResponseTime,
      activeConnections: metrics.activeConnections,
      healthScore: this.calculateHealthScore(metrics)
    };
  }

  private calculateHealthScore(metrics: any): number {
    if (metrics.totalRequests === 0) return 1.0;
    
    const rateLimitRate = metrics.totalRateLimitHits / metrics.totalRequests;
    const responseTimeScore = Math.max(0, 1 - (metrics.averageResponseTime / 10000)); // 10s baseline
    const rateLimitScore = Math.max(0, 1 - rateLimitRate);
    
    return (responseTimeScore + rateLimitScore) / 2;
  }
}

export interface DiagnosticResult {
  hostname: string;
  timestamp: string;
  checks: DiagnosticCheck[];
  overall: 'ok' | 'warning' | 'error' | 'unknown';
}

export interface DiagnosticCheck {
  name: string;
  status: 'ok' | 'warning' | 'error';
  message: string;
  details?: any;
}

export interface GlobalHealth {
  totalDomains: number;
  totalRequests: number;
  totalRateLimitHits: number;
  averageResponseTime: number;
  activeConnections: number;
  healthScore: number;
}

/**
 * Global debug instances
 */
export const debugInspector = new APObjectInspector();
export const debugValidator = new APSchemaValidator();
export const debugTracer = new RequestTracer();
export const debugDiagnostics = new FederationDiagnostics();

/**
 * Quick debug functions
 */
export const debug = {
  /**
   * Quick inspect an object
   */
  inspect: (obj: any, title?: string) => {
    console.log(debugInspector.inspect(obj, title));
  },

  /**
   * Quick validate an object
   */
  validate: (obj: any) => {
    const result = debugValidator.validate(obj);
    console.log('Validation Result:', result);
    return result;
  },

  /**
   * Quick trace a request
   */
  trace: (url: string, method?: string) => {
    return debugTracer.startTrace(url, method);
  },

  /**
   * Quick diagnose a domain
   */
  diagnose: async (hostname: string) => {
    const result = await debugDiagnostics.diagnoseDomain(hostname);
    console.log('Diagnostic Result:', result);
    return result;
  },

  /**
   * Quick health check
   */
  health: () => {
    const health = debugDiagnostics.getGlobalHealth();
    console.log('Global Health:', health);
    return health;
  }
} as const;
