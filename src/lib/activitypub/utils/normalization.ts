/**
 * Advanced normalization utilities for ActivityPub objects
 */

import type { 
  Entity, 
  CoreObject, 
  OrArray, 
  StringReferenceMap,
  EntityReference 
} from '$lib/activitypub/types';
import { processUrlsInAPObject, processDatesInAPObject } from './object';
import { logger } from './logger';

/**
 * Language map normalization options
 */
export interface LanguageMapOptions {
  defaultLanguage?: string;
  allowedLanguages?: string[];
  maxLength?: number;
  sanitize?: boolean;
}

/**
 * Normalization result
 */
export interface NormalizationResult<T> {
  value: T;
  warnings: string[];
  errors: string[];
}

/**
 * Default language map options
 */
export const DEFAULT_LANGUAGE_MAP_OPTIONS: LanguageMapOptions = {
  defaultLanguage: 'en',
  allowedLanguages: undefined, // Allow all languages
  maxLength: 5000,
  sanitize: true
};

/**
 * Normalize a language map (StringReferenceMap)
 */
export function normalizeLanguageMap(
  languageMap: StringReferenceMap | string | undefined,
  options: LanguageMapOptions = DEFAULT_LANGUAGE_MAP_OPTIONS
): NormalizationResult<StringReferenceMap | undefined> {
  const warnings: string[] = [];
  const errors: string[] = [];

  if (!languageMap) {
    return { value: undefined, warnings, errors };
  }

  // If it's a string, convert to language map with default language
  if (typeof languageMap === 'string') {
    const defaultLang = options.defaultLanguage || 'en';
    return {
      value: { [defaultLang]: languageMap },
      warnings: [`Converted string to language map with default language: ${defaultLang}`],
      errors
    };
  }

  if (typeof languageMap !== 'object') {
    errors.push('Language map must be an object or string');
    return { value: undefined, warnings, errors };
  }

  const normalized: StringReferenceMap = {};

  for (const [lang, content] of Object.entries(languageMap)) {
    // Validate language code
    if (!isValidLanguageCode(lang)) {
      warnings.push(`Invalid language code: ${lang}`);
      continue;
    }

    // Check allowed languages
    if (options.allowedLanguages && !options.allowedLanguages.includes(lang)) {
      warnings.push(`Language not in allowed list: ${lang}`);
      continue;
    }

    // Validate content
    if (typeof content !== 'string') {
      warnings.push(`Non-string content for language ${lang}`);
      continue;
    }

    // Check length
    if (options.maxLength && content.length > options.maxLength) {
      warnings.push(`Content too long for language ${lang}: ${content.length} > ${options.maxLength}`);
      normalized[lang] = content.substring(0, options.maxLength);
    } else {
      normalized[lang] = options.sanitize ? sanitizeText(content) : content;
    }
  }

  if (Object.keys(normalized).length === 0) {
    errors.push('No valid language entries found');
    return { value: undefined, warnings, errors };
  }

  return { value: normalized, warnings, errors };
}

/**
 * Normalize OrArray type to always be an array
 */
export function normalizeOrArray<T>(value: OrArray<T> | undefined): T[] {
  if (value === undefined || value === null) {
    return [];
  }

  if (Array.isArray(value)) {
    return value.filter(item => item !== null && item !== undefined);
  }

  return [value];
}

/**
 * Normalize OrArray type but preserve single values when appropriate
 */
export function normalizeOrArrayPreserveSingle<T>(value: OrArray<T> | undefined): OrArray<T> | undefined {
  if (value === undefined || value === null) {
    return undefined;
  }

  if (Array.isArray(value)) {
    const filtered = value.filter(item => item !== null && item !== undefined);
    if (filtered.length === 0) return undefined;
    if (filtered.length === 1) return filtered[0];
    return filtered;
  }

  return value;
}

/**
 * Normalize entity reference
 */
export function normalizeEntityReference(ref: EntityReference | undefined): EntityReference | undefined {
  if (!ref) return undefined;

  // If it's a string (URL), return as is
  if (typeof ref === 'string') {
    try {
      new URL(ref); // Validate URL
      return ref;
    } catch {
      logger.warn('Invalid URL in entity reference', {
        operation: 'normalization.normalizeEntityReference',
        metadata: { url: ref }
      });
      return undefined;
    }
  }

  // If it's an object, ensure it has required properties
  if (typeof ref === 'object') {
    if ('id' in ref && ref.id) {
      return ref;
    }
    if ('href' in ref && ref.href) {
      return ref;
    }
    logger.warn('Entity reference missing id or href', {
      operation: 'normalization.normalizeEntityReference',
      metadata: { ref }
    });
    return undefined;
  }

  return undefined;
}

/**
 * Normalize dates in an object
 */
export function normalizeDates<T extends Record<string, any>>(obj: T): T {
  const dateFields = ['published', 'updated', 'startTime', 'endTime'];
  const normalized = { ...obj } as any;

  for (const field of dateFields) {
    if (normalized[field]) {
      if (typeof normalized[field] === 'string') {
        try {
          normalized[field] = new Date(normalized[field]);
        } catch {
          logger.warn(`Invalid date format in field ${field}`, {
            operation: 'normalization.normalizeDates',
            metadata: { field, value: normalized[field] }
          });
          delete normalized[field];
        }
      } else if (!(normalized[field] instanceof Date)) {
        logger.warn(`Non-date value in date field ${field}`, {
          operation: 'normalization.normalizeDates',
          metadata: { field, value: normalized[field] }
        });
        delete normalized[field];
      }
    }
  }

  return normalized as T;
}

/**
 * Comprehensive ActivityPub object normalization
 */
export function normalizeAPObject<T extends CoreObject>(
  obj: T,
  options: {
    normalizeLanguageMaps?: boolean;
    normalizeArrays?: boolean;
    normalizeDates?: boolean;
    normalizeUrls?: boolean;
    preserveSingleValues?: boolean;
  } = {}
): NormalizationResult<T> {
  const {
    normalizeLanguageMaps = true,
    normalizeArrays = true,
    normalizeDates = true,
    normalizeUrls = true,
    preserveSingleValues = false
  } = options;

  let normalized = { ...obj };
  const warnings: string[] = [];
  const errors: string[] = [];

  try {
    // Normalize URLs
    if (normalizeUrls) {
      normalized = processUrlsInAPObject(normalized);
    }

    // Normalize dates
    if (normalizeDates) {
      normalized = processDatesInAPObject(normalized);
    }

    // Normalize language maps
    if (normalizeLanguageMaps) {
      const languageMapFields = ['nameMap', 'summaryMap', 'contentMap'];
      
      for (const field of languageMapFields) {
        if (normalized[field as keyof T]) {
          const result = normalizeLanguageMap(normalized[field as keyof T] as any);
          normalized[field as keyof T] = result.value as any;
          warnings.push(...result.warnings.map(w => `${field}: ${w}`));
          errors.push(...result.errors.map(e => `${field}: ${e}`));
        }
      }

      // Handle source.contentMap
      if (normalized.source && typeof normalized.source === 'object' && 'contentMap' in normalized.source) {
        const result = normalizeLanguageMap(normalized.source.contentMap);
        if (normalized.source) {
          (normalized.source as any).contentMap = result.value;
        }
        warnings.push(...result.warnings.map(w => `source.contentMap: ${w}`));
        errors.push(...result.errors.map(e => `source.contentMap: ${e}`));
      }
    }

    // Normalize arrays
    if (normalizeArrays) {
      const arrayFields = [
        'attributedTo', 'audience', 'bcc', 'bto', 'cc', 'to',
        'context', 'generator', 'icon', 'image', 'inReplyTo',
        'location', 'preview', 'tag', 'url'
      ];

      for (const field of arrayFields) {
        if (normalized[field as keyof T]) {
          if (preserveSingleValues) {
            normalized[field as keyof T] = normalizeOrArrayPreserveSingle(
              normalized[field as keyof T] as any
            ) as any;
          } else {
            normalized[field as keyof T] = normalizeOrArray(
              normalized[field as keyof T] as any
            ) as any;
          }
        }
      }
    }

    // Normalize entity references
    const entityRefFields = ['attributedTo', 'audience', 'bcc', 'bto', 'cc', 'to', 'context', 'generator', 'inReplyTo', 'location', 'preview', 'tag'];
    
    for (const field of entityRefFields) {
      if (normalized[field as keyof T]) {
        const value = normalized[field as keyof T];
        if (Array.isArray(value)) {
          normalized[field as keyof T] = value
            .map(ref => normalizeEntityReference(ref as any))
            .filter(ref => ref !== undefined) as any;
        } else {
          normalized[field as keyof T] = normalizeEntityReference(value as any) as any;
        }
      }
    }

  } catch (error) {
    errors.push(`Normalization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    logger.error('Object normalization failed', {
      operation: 'normalization.normalizeAPObject',
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: { objectType: obj.type }
    });
  }

  return { value: normalized, warnings, errors };
}

/**
 * Batch normalize multiple objects
 */
export async function normalizeAPObjects<T extends CoreObject>(
  objects: T[],
  options?: Parameters<typeof normalizeAPObject>[1]
): Promise<NormalizationResult<T[]>> {
  const normalized: T[] = [];
  const allWarnings: string[] = [];
  const allErrors: string[] = [];

  for (let i = 0; i < objects.length; i++) {
    const result = normalizeAPObject(objects[i], options);
    normalized.push(result.value);
    
    // Add index to warnings and errors for context
    allWarnings.push(...result.warnings.map(w => `[${i}] ${w}`));
    allErrors.push(...result.errors.map(e => `[${i}] ${e}`));
  }

  return {
    value: normalized,
    warnings: allWarnings,
    errors: allErrors
  };
}

/**
 * Validate language code (basic validation)
 */
function isValidLanguageCode(code: string): boolean {
  // Basic validation for language codes (ISO 639-1, ISO 639-2, or with region)
  const languageCodeRegex = /^[a-z]{2,3}(-[A-Z]{2})?$/;
  return languageCodeRegex.test(code);
}

/**
 * Basic text sanitization
 */
function sanitizeText(text: string): string {
  // Remove null bytes and control characters except newlines and tabs
  return text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
}

/**
 * Create a normalized copy of an object with all normalizations applied
 */
export function createNormalizedCopy<T extends CoreObject>(obj: T): T {
  const result = normalizeAPObject(obj, {
    normalizeLanguageMaps: true,
    normalizeArrays: true,
    normalizeDates: true,
    normalizeUrls: true,
    preserveSingleValues: false
  });

  if (result.errors.length > 0) {
    logger.warn('Normalization errors occurred', {
      operation: 'normalization.createNormalizedCopy',
      metadata: {
        errors: result.errors,
        objectType: obj.type
      }
    });
  }

  return result.value;
}
