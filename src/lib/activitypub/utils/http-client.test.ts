import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  signedFetch,
  signedPost,
  signedGet
} from './http-client.js';
import {
  ActivityPubTestBase,
  activityPubMocks
} from '../../../test/activitypub-test-utils';

// Mock dependencies
vi.mock('./http-signature', () => ({
  generateSignature: vi.fn().mockReturnValue('mock-signature'),
  generateDigest: vi.fn().mockReturnValue('mock-digest'),
  signRequest: vi.fn().mockResolvedValue({
    'Host': 'example.com',
    'Date': new Date().toUTCString(),
    'Signature': 'mock-signature',
    'Digest': 'SHA-256=mock-digest'
  })
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    federation: {
      outgoingError: vi.fn(),
      outgoingRequest: vi.fn(),
      outgoingResponse: vi.fn()
    }
  }
}));

vi.mock('$lib/activitypub/rateLimiter', () => ({
  rateLimitedFetch: vi.fn()
}));

// Note: We use rateLimitedFetch instead of global fetch

import { generateSignature, generateDigest, signRequest } from './http-signature';
import { ActivityPubLogs } from './logger';
import { rateLimitedFetch } from '$lib/activitypub/rateLimiter';

describe('ActivityPub Utils - HTTP Client', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
    vi.clearAllMocks();
    
    // Setup default mocks
    vi.mocked(generateSignature).mockReturnValue('mock-signature');
    vi.mocked(generateDigest).mockReturnValue('SHA-256=mock-digest');

    // Setup signRequest mock to handle different scenarios
    vi.mocked(signRequest).mockImplementation(async (method, url, headers, body, privateKey, keyId) => {
      const urlObj = new URL(url);
      const result = { ...headers };

      // Add standard headers that would be added by the real function
      result['Host'] = urlObj.host;
      result['Date'] = new Date().toUTCString();
      result['Signature'] = 'mock-signature';

      // Only add Digest for requests with body (POST, PUT, etc.)
      if (body && method.toUpperCase() === 'POST') {
        // Call the mocked generateDigest function
        result['Digest'] = vi.mocked(generateDigest)(body);
      }

      return result;
    });

    // Mock rateLimitedFetch to return a successful response
    vi.mocked(rateLimitedFetch).mockResolvedValue(new Response('{"success": true}', {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    }));

    // mockFetch is no longer needed since we use rateLimitedFetch
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('signedFetch', () => {
    const mockPrivateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----';
    const mockKeyId = 'https://example.com/users/alice#main-key';

    it('should make a signed GET request', async () => {
      const url = 'https://example.com/users/bob';
      
      const response = await signedFetch(url, {
        method: 'GET',
        privateKey: mockPrivateKey,
        keyId: mockKeyId
      });

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Signature': 'mock-signature',
          'Date': expect.any(String),
          'Host': 'example.com'
        })
      }));
      
      expect(response.ok).toBe(true);
    });

    it('should make a signed POST request with body', async () => {
      const url = 'https://example.com/inbox';
      const activity = { type: 'Follow', actor: 'alice', object: 'bob' };
      
      const response = await signedFetch(url, {
        method: 'POST',
        body: JSON.stringify(activity),
        privateKey: mockPrivateKey,
        keyId: mockKeyId,
        headers: {
          'Content-Type': 'application/activity+json'
        }
      });

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(activity),
        headers: expect.objectContaining({
          'Signature': 'mock-signature',
          'Digest': 'SHA-256=mock-digest',
          'Content-Type': 'application/activity+json',
          'Date': expect.any(String),
          'Host': 'example.com'
        })
      }));
      
      expect(generateDigest).toHaveBeenCalledWith(JSON.stringify(activity));
      expect(response.ok).toBe(true);
    });

    it('should handle request without body', async () => {
      const url = 'https://example.com/users/bob';
      
      await signedFetch(url, {
        method: 'GET',
        privateKey: mockPrivateKey,
        keyId: mockKeyId
      });

      expect(generateDigest).not.toHaveBeenCalled();
      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        headers: expect.not.objectContaining({
          'Digest': expect.any(String)
        })
      }));
    });

    it('should merge custom headers', async () => {
      const url = 'https://example.com/users/bob';
      const customHeaders = {
        'Accept': 'application/ld+json',
        'User-Agent': 'MyBot/1.0'
      };
      
      await signedFetch(url, {
        method: 'GET',
        privateKey: mockPrivateKey,
        keyId: mockKeyId,
        headers: customHeaders
      });

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        headers: expect.objectContaining({
          ...customHeaders,
          'Signature': 'mock-signature'
        })
      }));
    });

    it('should handle fetch errors', async () => {
      vi.mocked(rateLimitedFetch).mockRejectedValue(new Error('Network error'));
      
      const url = 'https://example.com/users/bob';
      
      await expect(signedFetch(url, {
        method: 'GET',
        privateKey: mockPrivateKey,
        keyId: mockKeyId
      })).rejects.toThrow('Network error');
    });

    it('should handle signature generation errors', async () => {
      vi.mocked(signRequest).mockRejectedValue(new Error('Signature generation failed'));

      const url = 'https://example.com/users/bob';

      await expect(signedFetch(url, {
        method: 'GET',
        privateKey: mockPrivateKey,
        keyId: mockKeyId
      })).rejects.toThrow('Signature generation failed');
    });
  });

  describe('signedPost', () => {
    const mockPrivateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----';
    const mockKeyId = 'https://example.com/users/alice#main-key';

    it('should make a signed POST request', async () => {
      const url = 'https://example.com/inbox';
      const activity = { type: 'Follow', actor: 'alice', object: 'bob' };
      
      const response = await signedPost(url, activity, mockPrivateKey, mockKeyId);

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(activity),
        headers: expect.objectContaining({
          'Content-Type': 'application/activity+json',
          'Signature': 'mock-signature'
        })
      }));
      
      expect(response.ok).toBe(true);
    });

    it('should handle complex activity objects', async () => {
      const url = 'https://example.com/inbox';
      const activity = {
        '@context': 'https://www.w3.org/ns/activitystreams',
        type: 'Create',
        actor: 'https://example.com/users/alice',
        object: {
          type: 'Note',
          content: 'Hello world!',
          to: ['https://www.w3.org/ns/activitystreams#Public']
        }
      };
      
      const response = await signedPost(url, activity, mockPrivateKey, mockKeyId);

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        body: JSON.stringify(activity)
      }));
      
      expect(response.ok).toBe(true);
    });
  });

  describe('signedGet', () => {
    const mockPrivateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----';
    const mockKeyId = 'https://example.com/users/alice#main-key';

    it('should make a signed GET request', async () => {
      const url = 'https://example.com/users/bob';
      
      const response = await signedGet(url, mockPrivateKey, mockKeyId);

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Signature': 'mock-signature'
        })
      }));
      
      expect(response.ok).toBe(true);
    });

    it('should not include digest header for GET requests', async () => {
      const url = 'https://example.com/users/bob';
      
      await signedGet(url, mockPrivateKey, mockKeyId);

      expect(rateLimitedFetch).toHaveBeenCalledWith(url, expect.objectContaining({
        headers: expect.not.objectContaining({
          'Digest': expect.any(String)
        })
      }));
    });
  });

  // Note: rateLimitedFetch and createHttpClient functions would be tested here
  // if they exist in the actual http-client.ts file

  describe('Error Handling', () => {
    const mockPrivateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY\n-----END PRIVATE KEY-----';
    const mockKeyId = 'https://example.com/users/alice#main-key';

    it('should handle HTTP error responses', async () => {
      vi.mocked(rateLimitedFetch).mockResolvedValue(new Response('Not Found', {
        status: 404,
        statusText: 'Not Found'
      }));
      
      const url = 'https://example.com/nonexistent';
      
      const response = await signedGet(url, mockPrivateKey, mockKeyId);
      
      expect(response.ok).toBe(false);
      expect(response.status).toBe(404);
    });

    it('should handle network timeouts', async () => {
      vi.mocked(rateLimitedFetch).mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout')), 100)
        )
      );
      
      const url = 'https://example.com/slow';
      
      await expect(signedGet(url, mockPrivateKey, mockKeyId)).rejects.toThrow('Timeout');
    });

    it('should log errors appropriately', async () => {
      vi.mocked(rateLimitedFetch).mockRejectedValue(new Error('Network error'));
      
      const url = 'https://example.com/error';
      
      try {
        await signedGet(url, mockPrivateKey, mockKeyId);
      } catch (error) {
        // Error should be thrown
      }
      
      // Logger should be called for error tracking
      expect(ActivityPubLogs.federation.outgoingError).toHaveBeenCalled();
    });
  });
});
