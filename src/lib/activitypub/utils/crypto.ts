/**
 * Cryptographic utilities for ActivityPub key management and HTTP signatures.
 * Provides functions for RSA key generation, key format conversion, and key management.
 */

import { generateKeyPair, createHash, randomBytes } from 'crypto';
import { promisify } from 'util';
import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { ActivityPubLogs } from './logger';
import { SecurityError, ValidationError } from './errors';
import { getFederationConfig } from '$lib/activitypub/config/federation';

const generateKeyPairAsync = promisify(generateKeyPair);

/**
 * RSA key pair for ActivityPub actors
 */
export interface ActorKeyPair {
  publicKey: string;  // PEM format
  privateKey: string; // PEM format
  keyId: string;      // Key identifier (actorUrl#main-key)
}

/**
 * Key generation options
 */
export interface KeyGenerationOptions {
  modulusLength?: number;  // RSA key size in bits (default: 2048)
  publicExponent?: number; // RSA public exponent (default: 65537)
  keyId?: string;         // Custom key ID
}

/**
 * Generate a new RSA key pair for an ActivityPub actor
 */
export async function generateActorKeyPair(
  actorUrl: string,
  options: KeyGenerationOptions = {}
): Promise<ActorKeyPair> {
  const {
    modulusLength = 2048,
    publicExponent = 65537,
    keyId = `${actorUrl}#main-key`
  } = options;

  try {
    ActivityPubLogs.security.keyGenerationStarted(actorUrl);

    const { publicKey, privateKey } = await generateKeyPairAsync('rsa', {
      modulusLength,
      publicExponent,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    const keyPair: ActorKeyPair = {
      publicKey,
      privateKey,
      keyId
    };

    ActivityPubLogs.security.keyGenerationCompleted(actorUrl, keyId);

    return keyPair;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.keyGenerationFailed(actorUrl, errorMessage);
    
    throw new SecurityError(
      `Failed to generate key pair for actor ${actorUrl}: ${errorMessage}`,
      'KEY_GENERATION_FAILED'
    );
  }
}

/**
 * Store actor key pair in database
 */
export async function storeActorKeys(
  userId: string,
  keyPair: ActorKeyPair
): Promise<void> {
  try {
    await db
      .update(user)
      .set({
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey
      })
      .where(eq(user.id, userId));

    ActivityPubLogs.security.keyStored(userId, keyPair.keyId);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    throw new SecurityError(
      `Failed to store keys for user ${userId}: ${errorMessage}`,
      'KEY_STORAGE_FAILED'
    );
  }
}

/**
 * Retrieve actor keys from database
 */
export async function getActorKeys(userId: string): Promise<ActorKeyPair | null> {
  try {
    const result = await db
      .select({
        publicKey: user.publicKey,
        privateKey: user.privateKey,
        uri: user.uri
      })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (!result[0] || !result[0].publicKey || !result[0].privateKey) {
      return null;
    }

    return {
      publicKey: result[0].publicKey,
      privateKey: result[0].privateKey,
      keyId: `${result[0].uri}#main-key`
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    throw new SecurityError(
      `Failed to retrieve keys for user ${userId}: ${errorMessage}`,
      'KEY_RETRIEVAL_FAILED'
    );
  }
}

/**
 * Generate new keys for an actor and store them
 */
export async function rotateActorKeys(
  userId: string,
  actorUrl: string,
  options: KeyGenerationOptions = {}
): Promise<ActorKeyPair> {
  try {
    ActivityPubLogs.security.keyRotationStarted(userId, actorUrl);

    // Generate new key pair
    const newKeyPair = await generateActorKeyPair(actorUrl, options);

    // Store new keys
    await storeActorKeys(userId, newKeyPair);

    ActivityPubLogs.security.keyRotationCompleted(userId, actorUrl, newKeyPair.keyId);

    return newKeyPair;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.keyRotationFailed(userId, actorUrl, errorMessage);
    
    throw new SecurityError(
      `Failed to rotate keys for user ${userId}: ${errorMessage}`,
      'KEY_ROTATION_FAILED'
    );
  }
}

/**
 * Validate RSA key format
 */
export function validateKeyFormat(key: string, type: 'public' | 'private'): boolean {
  const publicKeyPattern = /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
  const privateKeyPattern = /^-----BEGIN PRIVATE KEY-----[\s\S]*-----END PRIVATE KEY-----$/;

  if (type === 'public') {
    return publicKeyPattern.test(key.trim());
  } else {
    return privateKeyPattern.test(key.trim());
  }
}

/**
 * Extract public key from private key
 */
export function extractPublicKey(privateKey: string): string {
  // This is a simplified version - in production you might want to use crypto functions
  // For now, we assume the public key is stored separately
  throw new Error('Public key extraction not implemented - store keys separately');
}

/**
 * Generate a secure random string for key IDs or nonces
 */
export function generateSecureRandom(length: number = 32): string {
  return randomBytes(length).toString('hex');
}

/**
 * Hash a string using SHA-256
 */
export function sha256Hash(input: string): string {
  return createHash('sha256').update(input, 'utf8').digest('hex');
}

/**
 * Create a key fingerprint for identification
 */
export function createKeyFingerprint(publicKey: string): string {
  // Remove PEM headers and whitespace, then hash
  const keyContent = publicKey
    .replace(/-----BEGIN PUBLIC KEY-----/, '')
    .replace(/-----END PUBLIC KEY-----/, '')
    .replace(/\s/g, '');
  
  return sha256Hash(keyContent).substring(0, 16); // First 16 chars of hash
}

/**
 * Validate key pair consistency
 */
export function validateKeyPair(keyPair: ActorKeyPair): boolean {
  try {
    // Basic format validation
    if (!validateKeyFormat(keyPair.publicKey, 'public')) {
      return false;
    }
    
    if (!validateKeyFormat(keyPair.privateKey, 'private')) {
      return false;
    }

    // Key ID format validation
    if (!keyPair.keyId || !keyPair.keyId.includes('#')) {
      return false;
    }

    return true;

  } catch {
    return false;
  }
}

/**
 * Get key generation configuration from federation config
 */
export function getKeyGenerationConfig(): KeyGenerationOptions {
  const federationConfig = getFederationConfig();
  
  return {
    modulusLength: 2048, // Standard RSA key size for ActivityPub
    publicExponent: 65537 // Standard RSA public exponent
  };
}

/**
 * Initialize keys for a new actor if they don't exist
 */
export async function ensureActorKeys(
  userId: string,
  actorUrl: string
): Promise<ActorKeyPair> {
  // Check if keys already exist
  const existingKeys = await getActorKeys(userId);
  
  if (existingKeys && validateKeyPair(existingKeys)) {
    return existingKeys;
  }

  // Generate new keys if they don't exist or are invalid
  ActivityPubLogs.security.keyInitialization(userId, actorUrl);
  
  const keyGenerationConfig = getKeyGenerationConfig();
  return await rotateActorKeys(userId, actorUrl, keyGenerationConfig);
}
