/**
 * Security utilities for ActivityPub processing.
 * Includes HTML escaping, URL validation, and content sanitization.
 */

/**
 * Escapes HTML special characters to prevent XSS attacks.
 * 
 * @param unsafe - The unsafe string that may contain HTML special characters
 * @returns The escaped string safe for HTML insertion
 * 
 * @example
 * ```typescript
 * escapeHtml('<script>alert("xss")</script>') 
 * // Returns: '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;'
 * ```
 */
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Validates URL for security (protocol, format).
 * 
 * @param url - The URL string to validate
 * @returns true if the URL is considered secure, false otherwise
 * 
 * @example
 * ```typescript
 * isSecureUrl('https://example.com/image.png') // Returns: true
 * isSecureUrl('javascript:alert("xss")') // Returns: false
 * ```
 */
export function isSecureUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    // Only allow HTTP and HTTPS protocols
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    // Invalid URL format
    return false;
  }
}

/**
 * Validates and sanitizes a domain name.
 * 
 * @param domain - The domain string to validate
 * @returns true if the domain is valid, false otherwise
 */
export function isValidDomain(domain: string): boolean {
  // Basic domain validation regex
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return domainRegex.test(domain) && domain.length <= 253;
}

/**
 * Checks if a string contains potentially dangerous content.
 * 
 * @param content - The content to check
 * @returns true if content appears safe, false if potentially dangerous
 */
export function isSafeContent(content: string): boolean {
  // Check for common XSS patterns
  const dangerousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi, // onclick, onload, etc.
    /<iframe\b/gi,
    /<object\b/gi,
    /<embed\b/gi,
    /<form\b/gi
  ];
  
  return !dangerousPatterns.some(pattern => pattern.test(content));
}
