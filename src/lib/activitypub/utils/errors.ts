/**
 * Specialized error classes for ActivityPub operations
 */

/**
 * Base class for all ActivityPub errors
 */
export abstract class ActivityPubError extends Error {
  abstract readonly code: string;
  abstract readonly category: 'network' | 'validation' | 'processing' | 'security';
  
  constructor(
    message: string,
    public readonly context?: Record<string, any>
  ) {
    super(message);
    this.name = this.constructor.name;
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      category: this.category,
      message: this.message,
      context: this.context
    };
  }
}

/**
 * Network-related errors (fetch, timeout, etc.)
 */
export class NetworkError extends ActivityPubError {
  readonly category = 'network' as const;
  
  constructor(
    message: string,
    public readonly code: string,
    context?: Record<string, any>
  ) {
    super(message, context);
  }
}

export class FetchError extends NetworkError {
  readonly code = 'FETCH_ERROR';
  readonly status?: number;

  constructor(message: string, code: string = 'FETCH_ERROR', context?: Record<string, any>) {
    super(message, code, context);
    this.status = context?.status;
  }
}

export class TimeoutError extends NetworkError {
  readonly code = 'TIMEOUT_ERROR';
  
  constructor(url: string, timeout: number) {
    super(
      `Request to ${url} timed out after ${timeout}ms`,
      'TIMEOUT_ERROR',
      { url, timeout }
    );
  }
}

export class RateLimitError extends NetworkError {
  readonly code = 'RATE_LIMIT_ERROR';
  
  constructor(url: string, retryAfter?: number) {
    super(
      `Rate limit exceeded for ${url}${retryAfter ? `, retry after ${retryAfter}s` : ''}`,
      'RATE_LIMIT_ERROR',
      { url, retryAfter }
    );
  }
}

/**
 * Validation errors (malformed data, missing fields, etc.)
 */
export class ValidationError extends ActivityPubError {
  readonly category = 'validation' as const;
  
  constructor(
    message: string,
    public readonly code: string,
    context?: Record<string, any>
  ) {
    super(message, context);
  }
}

export class MissingFieldError extends ValidationError {
  readonly code = 'MISSING_FIELD';
  
  constructor(fieldName: string, objectType?: string) {
    super(
      `Missing required field: ${fieldName}${objectType ? ` in ${objectType}` : ''}`,
      'MISSING_FIELD',
      { fieldName, objectType }
    );
  }
}

export class InvalidTypeError extends ValidationError {
  readonly code = 'INVALID_TYPE';
  
  constructor(expectedType: string | string[], actualType: string) {
    const expected = Array.isArray(expectedType) ? expectedType.join(' or ') : expectedType;
    super(
      `Expected type ${expected}, got ${actualType}`,
      'INVALID_TYPE',
      { expectedType, actualType }
    );
  }
}

export class MalformedObjectError extends ValidationError {
  readonly code = 'MALFORMED_OBJECT';
  
  constructor(objectType: string, reason?: string) {
    super(
      `Malformed ${objectType} object${reason ? `: ${reason}` : ''}`,
      'MALFORMED_OBJECT',
      { objectType, reason }
    );
  }
}

export class InvalidUrlError extends ValidationError {
  readonly code = 'INVALID_URL';
  
  constructor(url: string, fieldName?: string) {
    super(
      `Invalid URL: ${url}${fieldName ? ` in field ${fieldName}` : ''}`,
      'INVALID_URL',
      { url, fieldName }
    );
  }
}

/**
 * Processing errors (conversion, transformation, etc.)
 */
export class ProcessingError extends ActivityPubError {
  readonly category = 'processing' as const;
  
  constructor(
    message: string,
    public readonly code: string,
    context?: Record<string, any>
  ) {
    super(message, context);
  }
}

export class ActorImportError extends ProcessingError {
  readonly code = 'ACTOR_IMPORT_ERROR';
  
  constructor(reason: string, actorId?: string) {
    super(
      `Failed to import actor${actorId ? ` ${actorId}` : ''}: ${reason}`,
      'ACTOR_IMPORT_ERROR',
      { reason, actorId }
    );
  }
}

export class PostImportError extends ProcessingError {
  readonly code = 'POST_IMPORT_ERROR';
  
  constructor(reason: string, postId?: string) {
    super(
      `Failed to import post${postId ? ` ${postId}` : ''}: ${reason}`,
      'POST_IMPORT_ERROR',
      { reason, postId }
    );
  }
}

export class WebFingerError extends ProcessingError {
  readonly code = 'WEBFINGER_ERROR';
  
  constructor(handle: string, reason?: string) {
    super(
      `WebFinger lookup failed for ${handle}${reason ? `: ${reason}` : ''}`,
      'WEBFINGER_ERROR',
      { handle, reason }
    );
  }
}

/**
 * Security errors (signature verification, etc.)
 */
export class SecurityError extends ActivityPubError {
  readonly category = 'security' as const;
  
  constructor(
    message: string,
    public readonly code: string,
    context?: Record<string, any>
  ) {
    super(message, context);
  }
}

export class SignatureVerificationError extends SecurityError {
  readonly code = 'SIGNATURE_VERIFICATION_ERROR';
  
  constructor(reason: string) {
    super(
      `Signature verification failed: ${reason}`,
      'SIGNATURE_VERIFICATION_ERROR',
      { reason }
    );
  }
}

export class UnsafeContentError extends SecurityError {
  readonly code = 'UNSAFE_CONTENT';
  
  constructor(reason: string, content?: string) {
    super(
      `Unsafe content detected: ${reason}`,
      'UNSAFE_CONTENT',
      { reason, content }
    );
  }
}

/**
 * Utility functions for error handling
 */
export function isActivityPubError(error: unknown): error is ActivityPubError {
  return error instanceof ActivityPubError;
}

export function isNetworkError(error: unknown): error is NetworkError {
  return error instanceof NetworkError;
}

export function isValidationError(error: unknown): error is ValidationError {
  return error instanceof ValidationError;
}

export function isProcessingError(error: unknown): error is ProcessingError {
  return error instanceof ProcessingError;
}

export function isSecurityError(error: unknown): error is SecurityError {
  return error instanceof SecurityError;
}

/**
 * Convert unknown error to ActivityPub error
 */
export function toActivityPubError(error: unknown, defaultMessage = 'Unknown error'): ActivityPubError {
  if (isActivityPubError(error)) {
    return error;
  }
  
  if (error instanceof Error) {
    return new ProcessingError(error.message, 'UNKNOWN_ERROR', { originalError: error.name });
  }
  
  return new ProcessingError(defaultMessage, 'UNKNOWN_ERROR', { originalError: String(error) });
}

/**
 * Error recovery strategies
 */
export interface ErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  fallbackValue?: any;
  continueOnError?: boolean;
}

export async function withErrorRecovery<T>(
  operation: () => Promise<T>,
  options: ErrorRecoveryOptions = {}
): Promise<{ success: boolean; result?: T; error?: ActivityPubError }> {
  const { maxRetries = 3, retryDelay = 1000, fallbackValue } = options;
  
  let lastError: ActivityPubError | undefined;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();
      return { success: true, result };
    } catch (error) {
      lastError = toActivityPubError(error);
      
      // Don't retry validation or security errors
      if (isValidationError(lastError) || isSecurityError(lastError)) {
        break;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retry
      if (retryDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }
  }
  
  return {
    success: false,
    result: fallbackValue,
    error: lastError
  };
}
