/**
 * Idempotency utilities for ActivityPub operations.
 * Ensures that operations can be safely retried without side effects.
 */

import { 
  checkActivityDuplication,
  createProcessingRecord,
  updateProcessingStatus,
  getProcessingRecord,
  type ProcessingStatus,
  type DeduplicationResult,
  type IdempotencyOptions
} from './deduplication';
import { ActivityPubLogs } from './logger';
import type { Activity } from '$lib/activitypub/types';

/**
 * Idempotent operation result
 */
export interface IdempotentResult<T = any> {
  success: boolean;
  result?: T;
  wasProcessed: boolean;
  processingId: string;
  error?: string;
}

/**
 * Idempotent operation function type
 */
export type IdempotentOperation<T = any> = () => Promise<T>;

/**
 * Execute an operation idempotently
 */
export async function executeIdempotent<T = any>(
  activity: Activity,
  operation: IdempotentOperation<T>,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<IdempotentResult<T>> {
  let processingId: string | undefined;
  
  try {
    // Check for existing processing
    const duplicationCheck = await checkActivityDuplication(activity, sourceInbox, options);
    
    if (duplicationCheck.isDuplicate) {
      // Activity was already processed
      ActivityPubLogs.federation.idempotentOperationSkipped(
        activity.id || 'unknown',
        activity.type,
        duplicationCheck.existingStatus || 'unknown'
      );
      
      return {
        success: duplicationCheck.existingStatus === 'completed',
        result: duplicationCheck.existingResult,
        wasProcessed: true,
        processingId: duplicationCheck.processingId!,
        error: duplicationCheck.existingStatus === 'failed' ? 'Previous processing failed' : undefined
      };
    }
    
    // Create new processing record
    processingId = await createProcessingRecord(activity, sourceInbox, options);
    
    // Mark as processing
    await updateProcessingStatus(processingId, 'processing');
    
    ActivityPubLogs.federation.idempotentOperationStarted(
      activity.id || 'unknown',
      activity.type,
      processingId
    );
    
    // Execute the operation
    const result = await operation();
    
    // Mark as completed
    await updateProcessingStatus(processingId, 'completed', result);
    
    ActivityPubLogs.federation.idempotentOperationCompleted(
      activity.id || 'unknown',
      activity.type,
      processingId
    );
    
    return {
      success: true,
      result,
      wasProcessed: false,
      processingId
    };
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    if (processingId) {
      await updateProcessingStatus(processingId, 'failed', undefined, errorMessage);
    }
    
    ActivityPubLogs.federation.idempotentOperationFailed(
      activity.id || 'unknown',
      activity.type,
      processingId || 'unknown',
      errorMessage
    );
    
    return {
      success: false,
      wasProcessed: false,
      processingId: processingId || 'unknown',
      error: errorMessage
    };
  }
}

/**
 * Check if operation was already completed successfully
 */
export async function isOperationCompleted(
  activity: Activity,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<{
  completed: boolean;
  result?: any;
  processingId?: string;
}> {
  const duplicationCheck = await checkActivityDuplication(activity, sourceInbox, options);
  
  if (duplicationCheck.isDuplicate && duplicationCheck.existingStatus === 'completed') {
    return {
      completed: true,
      result: duplicationCheck.existingResult,
      processingId: duplicationCheck.processingId
    };
  }
  
  return {
    completed: false
  };
}

/**
 * Retry failed operation idempotently
 */
export async function retryIdempotent<T = any>(
  activity: Activity,
  operation: IdempotentOperation<T>,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<IdempotentResult<T>> {
  const duplicationCheck = await checkActivityDuplication(activity, sourceInbox, options);
  
  if (duplicationCheck.isDuplicate) {
    const processingId = duplicationCheck.processingId!;
    
    // If already completed, return existing result
    if (duplicationCheck.existingStatus === 'completed') {
      return {
        success: true,
        result: duplicationCheck.existingResult,
        wasProcessed: true,
        processingId
      };
    }
    
    // If failed or pending, retry the operation
    if (duplicationCheck.existingStatus === 'failed' || duplicationCheck.existingStatus === 'pending') {
      try {
        // Mark as processing
        await updateProcessingStatus(processingId, 'processing');
        
        ActivityPubLogs.federation.idempotentOperationRetried(
          activity.id || 'unknown',
          activity.type,
          processingId
        );
        
        // Execute the operation
        const result = await operation();
        
        // Mark as completed
        await updateProcessingStatus(processingId, 'completed', result);
        
        return {
          success: true,
          result,
          wasProcessed: false,
          processingId
        };
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        await updateProcessingStatus(processingId, 'failed', undefined, errorMessage);
        
        return {
          success: false,
          wasProcessed: false,
          processingId,
          error: errorMessage
        };
      }
    }
    
    // If currently processing, return current state
    return {
      success: false,
      wasProcessed: true,
      processingId,
      error: 'Operation is currently being processed'
    };
  }
  
  // No existing record, execute normally
  return executeIdempotent(activity, operation, sourceInbox, options);
}

/**
 * Create idempotent wrapper for a function
 */
export function createIdempotentWrapper<T = any>(
  operation: IdempotentOperation<T>,
  defaultOptions: IdempotencyOptions = {}
) {
  return async (
    activity: Activity,
    sourceInbox?: string,
    options: IdempotencyOptions = {}
  ): Promise<IdempotentResult<T>> => {
    const mergedOptions = { ...defaultOptions, ...options };
    return executeIdempotent(activity, operation, sourceInbox, mergedOptions);
  };
}

/**
 * Batch idempotent operations
 */
export async function executeBatchIdempotent<T = any>(
  activities: Array<{ activity: Activity; operation: IdempotentOperation<T>; sourceInbox?: string }>,
  options: IdempotencyOptions = {}
): Promise<Array<IdempotentResult<T>>> {
  const results: Array<IdempotentResult<T>> = [];
  
  for (const { activity, operation, sourceInbox } of activities) {
    try {
      const result = await executeIdempotent(activity, operation, sourceInbox, options);
      results.push(result);
    } catch (error) {
      results.push({
        success: false,
        wasProcessed: false,
        processingId: 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
  
  return results;
}

/**
 * Get operation status
 */
export async function getOperationStatus(
  activity: Activity,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<{
  exists: boolean;
  status?: ProcessingStatus;
  result?: any;
  processingId?: string;
  startedAt?: Date;
  completedAt?: Date;
  errorMessage?: string;
}> {
  const duplicationCheck = await checkActivityDuplication(activity, sourceInbox, options);
  
  if (!duplicationCheck.isDuplicate) {
    return { exists: false };
  }
  
  const processingRecord = await getProcessingRecord(duplicationCheck.processingId!);
  
  if (!processingRecord) {
    return { exists: false };
  }
  
  return {
    exists: true,
    status: processingRecord.status,
    result: processingRecord.result,
    processingId: processingRecord.id,
    startedAt: processingRecord.startedAt,
    completedAt: processingRecord.completedAt,
    errorMessage: processingRecord.errorMessage
  };
}
