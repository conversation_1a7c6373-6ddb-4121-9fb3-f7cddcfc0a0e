/**
 * Utilities for working with ActivityPub addresses and collections
 */

import type { EntityReference, OrArray } from '$lib/activitypub/types';

/**
 * Normalize addresses to an array of strings
 */
export function normalizeAddresses(addresses: OrArray<EntityReference>): string[] {
  if (!addresses) return [];

  const addressArray = Array.isArray(addresses) ? addresses : [addresses];

  return addressArray.map(addr => {
    if (addr instanceof URL) return addr.toString();
    if (typeof addr === 'object' && addr.id) {
      return addr.id.toString();
    }
    return addr.toString();
  });
}

/**
 * Check if an address is a followers collection
 */
export function isFollowersCollection(address: string): boolean {
  try {
    const url = new URL(address);
    return url.pathname.endsWith('/followers');
  } catch {
    return false;
  }
}

/**
 * Check if an address is the ActivityStreams Public collection
 */
export function isPublicCollection(address: string): boolean {
  return address === 'https://www.w3.org/ns/activitystreams#Public';
}

/**
 * Extract all unique addresses from to and cc fields
 */
export function extractAllAddresses(
  to?: OrArray<EntityReference>, 
  cc?: OrArray<EntityReference>
): {
  toAddresses: string[];
  ccAddresses: string[];
  allAddresses: string[];
} {
  const toAddresses = normalizeAddresses(to ?? []);
  const ccAddresses = normalizeAddresses(cc ?? []);
  const allAddresses = [...new Set([...toAddresses, ...ccAddresses])];

  return {
    toAddresses,
    ccAddresses,
    allAddresses
  };
}

/**
 * Check if addresses contain a specific pattern
 */
export function addressesContain(
  addresses: string[], 
  predicate: (address: string) => boolean
): boolean {
  return addresses.some(predicate);
}

/**
 * Filter addresses by a predicate
 */
export function filterAddresses(
  addresses: string[], 
  predicate: (address: string) => boolean
): string[] {
  return addresses.filter(predicate);
}

/**
 * Common address patterns
 */
export const AddressPatterns = {
  isPublic: isPublicCollection,
  isFollowers: isFollowersCollection,
  isActorInbox: (address: string) => address.includes('/inbox'),
  isActorOutbox: (address: string) => address.includes('/outbox'),
  isSharedInbox: (address: string) => address.includes('/sharedInbox'),
} as const;
