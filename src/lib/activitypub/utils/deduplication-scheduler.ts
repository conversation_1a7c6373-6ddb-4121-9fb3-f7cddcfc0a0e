/**
 * Scheduler for automatic cleanup of deduplication and processing records.
 * Manages periodic cleanup of old processing records and statistics collection.
 */

import { cleanupOldProcessingRecords, getProcessingStats } from './deduplication';
import { ActivityPubLogs } from './logger';
import { getFederationConfig } from '$lib/activitypub/config/federation';

/**
 * Deduplication scheduler configuration
 */
export interface DeduplicationSchedulerConfig {
  enabled: boolean;
  cleanupIntervalMs: number;
  cleanupOlderThanHours: number;
  statsIntervalMs: number;
  maxProcessingRecords: number;
}

/**
 * Default scheduler configuration
 */
const DEFAULT_CONFIG: DeduplicationSchedulerConfig = {
  enabled: true,
  cleanupIntervalMs: 60 * 60 * 1000, // 1 hour
  cleanupOlderThanHours: 24, // 24 hours
  statsIntervalMs: 15 * 60 * 1000, // 15 minutes
  maxProcessingRecords: 10000
};

/**
 * Deduplication scheduler class
 */
export class DeduplicationScheduler {
  private config: DeduplicationSchedulerConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private statsTimer?: NodeJS.Timeout;
  private isRunning = false;

  constructor(config: Partial<DeduplicationSchedulerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      ActivityPubLogs.federation.schedulerAlreadyRunning();
      return;
    }

    if (!this.config.enabled) {
      ActivityPubLogs.federation.schedulerDisabled();
      return;
    }

    this.isRunning = true;

    // Start cleanup timer
    this.cleanupTimer = setInterval(
      () => this.performCleanup(),
      this.config.cleanupIntervalMs
    );

    // Start stats timer
    this.statsTimer = setInterval(
      () => this.collectStats(),
      this.config.statsIntervalMs
    );

    ActivityPubLogs.federation.schedulerStarted(this.config);

    // Perform initial cleanup and stats collection
    this.performCleanup();
    this.collectStats();
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    if (this.statsTimer) {
      clearInterval(this.statsTimer);
      this.statsTimer = undefined;
    }

    ActivityPubLogs.federation.schedulerStopped();
  }

  /**
   * Update scheduler configuration
   */
  updateConfig(newConfig: Partial<DeduplicationSchedulerConfig>): void {
    const wasRunning = this.isRunning;
    
    if (wasRunning) {
      this.stop();
    }

    this.config = { ...this.config, ...newConfig };

    ActivityPubLogs.federation.schedulerConfigUpdated(this.config);

    if (wasRunning && this.config.enabled) {
      this.start();
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): DeduplicationSchedulerConfig {
    return { ...this.config };
  }

  /**
   * Check if scheduler is running
   */
  isSchedulerRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Perform cleanup of old processing records
   */
  private async performCleanup(): Promise<void> {
    try {
      const deletedCount = await cleanupOldProcessingRecords(this.config.cleanupOlderThanHours);
      
      ActivityPubLogs.federation.schedulerCleanupCompleted(
        deletedCount,
        this.config.cleanupOlderThanHours
      );

      // Check if we need more aggressive cleanup
      const stats = await getProcessingStats();
      if (stats.total > this.config.maxProcessingRecords) {
        // Perform more aggressive cleanup (older records)
        const aggressiveHours = Math.max(1, this.config.cleanupOlderThanHours / 2);
        const additionalDeleted = await cleanupOldProcessingRecords(aggressiveHours);
        
        ActivityPubLogs.federation.schedulerCleanupCompleted(
          additionalDeleted,
          aggressiveHours
        );
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.federation.schedulerCleanupError(errorMessage);
    }
  }

  /**
   * Collect and log processing statistics
   */
  private async collectStats(): Promise<void> {
    try {
      const stats = await getProcessingStats();
      ActivityPubLogs.federation.schedulerStats(stats);

      // Log warnings for concerning statistics
      if (stats.failed > stats.completed * 0.1) {
        ActivityPubLogs.federation.schedulerProcessingError(
          `High failure rate: ${stats.failed} failed vs ${stats.completed} completed`
        );
      }

      if (stats.processing > 100) {
        ActivityPubLogs.federation.schedulerProcessingError(
          `High number of processing records: ${stats.processing}`
        );
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.federation.schedulerStatsError(errorMessage);
    }
  }

  /**
   * Force cleanup now
   */
  async forceCleanup(): Promise<number> {
    return cleanupOldProcessingRecords(this.config.cleanupOlderThanHours);
  }

  /**
   * Get current statistics
   */
  async getCurrentStats(): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    duplicate: number;
    total: number;
  }> {
    return getProcessingStats();
  }
}

/**
 * Global scheduler instance
 */
let globalScheduler: DeduplicationScheduler | null = null;

/**
 * Initialize global deduplication scheduler
 */
export function initializeDeduplicationScheduler(
  config: Partial<DeduplicationSchedulerConfig> = {}
): DeduplicationScheduler {
  if (globalScheduler) {
    globalScheduler.updateConfig(config);
    return globalScheduler;
  }

  // Get configuration from federation config
  const federationConfig = getFederationConfig();
  const defaultConfig: Partial<DeduplicationSchedulerConfig> = {
    cleanupOlderThanHours: federationConfig.processing.deduplicationWindow / (1000 * 60 * 60), // Convert ms to hours
  };

  globalScheduler = new DeduplicationScheduler({ ...defaultConfig, ...config });
  return globalScheduler;
}

/**
 * Start global deduplication scheduler
 */
export function startDeduplicationScheduler(
  config: Partial<DeduplicationSchedulerConfig> = {}
): void {
  const scheduler = initializeDeduplicationScheduler(config);
  scheduler.start();
}

/**
 * Stop global deduplication scheduler
 */
export function stopDeduplicationScheduler(): void {
  if (globalScheduler) {
    globalScheduler.stop();
  }
}

/**
 * Get global scheduler instance
 */
export function getDeduplicationScheduler(): DeduplicationScheduler | null {
  return globalScheduler;
}

/**
 * Update global scheduler configuration
 */
export function updateDeduplicationSchedulerConfig(
  config: Partial<DeduplicationSchedulerConfig>
): void {
  if (globalScheduler) {
    globalScheduler.updateConfig(config);
  }
}

/**
 * Get deduplication statistics
 */
export async function getDeduplicationStats(): Promise<{
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  duplicate: number;
  total: number;
}> {
  return getProcessingStats();
}

/**
 * Force cleanup of old processing records
 */
export async function forceDeduplicationCleanup(
  olderThanHours?: number
): Promise<number> {
  if (globalScheduler) {
    return globalScheduler.forceCleanup();
  }
  
  return cleanupOldProcessingRecords(olderThanHours);
}

/**
 * Check scheduler health
 */
export function getSchedulerHealth(): {
  isRunning: boolean;
  config: DeduplicationSchedulerConfig | null;
  uptime?: number;
} {
  if (!globalScheduler) {
    return {
      isRunning: false,
      config: null
    };
  }

  return {
    isRunning: globalScheduler.isSchedulerRunning(),
    config: globalScheduler.getConfig()
  };
}
