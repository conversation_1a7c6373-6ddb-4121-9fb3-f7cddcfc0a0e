/**
 * Утилиты для работы с сервисным аккаунтом
 */

import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { eq, and, isNull } from 'drizzle-orm';
import type { ActorKeyPair } from './crypto';
import { ActivityPubLogs } from './logger';

/**
 * Получить ключи сервисного аккаунта для подписи запросов
 */
export async function getServiceAccountKeys(): Promise<ActorKeyPair | null> {
  try {
    const serviceAccount = await db
      .select({
        id: user.id,
        publicKey: user.publicKey,
        privateKey: user.privateKey,
        uri: user.uri
      })
      .from(user)
      .where(and(
        eq(user.username, 'service'),
        isNull(user.domain)
      ))
      .limit(1);

    if (!serviceAccount[0] || !serviceAccount[0].publicKey || !serviceAccount[0].privateKey) {
      ActivityPubLogs.security.keyRetrievalFailed('service', 'Service account not found or missing keys');
      return null;
    }

    const account = serviceAccount[0];
    
    // Динамически формируем URI для сервисного аккаунта
    const federationConfig = await import('$lib/activitypub/config/federation').then(m => m.getFederationConfig());
    const serviceUri = `${federationConfig.baseUrl}/users/service`;

    return {
      publicKey: account.publicKey,
      privateKey: account.privateKey,
      keyId: `${serviceUri}#main-key`
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.security.keyRetrievalFailed('service', errorMessage);
    return null;
  }
}

/**
 * Проверить, существует ли сервисный аккаунт
 */
export async function serviceAccountExists(): Promise<boolean> {
  try {
    const serviceAccount = await db
      .select({ id: user.id })
      .from(user)
      .where(and(
        eq(user.username, 'service'),
        isNull(user.domain)
      ))
      .limit(1);

    return serviceAccount.length > 0;
  } catch {
    return false;
  }
}

/**
 * Получить ID сервисного аккаунта
 */
export async function getServiceAccountId(): Promise<string | null> {
  try {
    const serviceAccount = await db
      .select({ id: user.id })
      .from(user)
      .where(and(
        eq(user.username, 'service'),
        isNull(user.domain)
      ))
      .limit(1);

    return serviceAccount[0]?.id || null;
  } catch {
    return null;
  }
}
