/**
 * Public key caching system for ActivityPub HTTP signature verification.
 * Provides efficient caching of remote actor public keys with TTL and validation.
 */

import { MemoryCache } from './cache';
import { fetchAndProcessAPObject } from './fetch';
import { validateUrl } from './links';
import { ActivityPubLogs } from './logger';
import { SecurityError, ValidationError, FetchError } from './errors';
import type { Actor } from '$lib/activitypub/types';

/**
 * Cached public key entry
 */
export interface CachedPublicKey {
  keyId: string;
  actorUrl: string;
  publicKeyPem: string;
  algorithm: string;
  owner: string;
  fetchedAt: number;
  expiresAt: number;
}

/**
 * Key cache configuration
 */
export interface KeyCacheConfig {
  maxSize: number;        // Maximum cache size in bytes
  defaultTtl: number;     // Default TTL in milliseconds
  maxEntries: number;     // Maximum number of cached keys
  refreshThreshold: number; // Refresh keys when TTL < threshold (ms)
}

/**
 * Public key cache for HTTP signature verification
 */
export class PublicKeyCache {
  private cache: MemoryCache<CachedPublicKey>;
  private config: KeyCacheConfig;
  private refreshPromises: Map<string, Promise<CachedPublicKey | null>>;

  constructor(config: Partial<KeyCacheConfig> = {}) {
    this.config = {
      maxSize: 10 * 1024 * 1024, // 10MB
      defaultTtl: 3600000,       // 1 hour
      maxEntries: 1000,
      refreshThreshold: 300000,   // 5 minutes
      ...config
    };

    this.cache = new MemoryCache<CachedPublicKey>({
      maxSize: this.config.maxSize,
      defaultTtl: this.config.defaultTtl,
      maxEntries: this.config.maxEntries
    });

    this.refreshPromises = new Map();
  }

  /**
   * Get public key from cache or fetch if not cached
   */
  async getPublicKey(keyId: string): Promise<string | null> {
    try {
      // Check cache first
      const cached = this.cache.get(keyId);
      
      if (cached) {
        // Check if key needs refresh
        const now = Date.now();
        const timeUntilExpiry = cached.expiresAt - now;
        
        if (timeUntilExpiry > this.config.refreshThreshold) {
          ActivityPubLogs.security.keyRetrieved(keyId, 'cache_hit');
          return cached.publicKeyPem;
        }
        
        // Key is close to expiry, refresh in background
        this.refreshKeyInBackground(keyId);
        return cached.publicKeyPem;
      }

      // Not in cache, fetch it
      const fetchedKey = await this.fetchPublicKey(keyId);
      return fetchedKey?.publicKeyPem || null;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.security.keyRetrievalFailed(keyId, errorMessage);
      return null;
    }
  }

  /**
   * Fetch public key from remote actor
   */
  private async fetchPublicKey(keyId: string): Promise<CachedPublicKey | null> {
    // Prevent duplicate fetches
    const existingPromise = this.refreshPromises.get(keyId);
    if (existingPromise) {
      return existingPromise;
    }

    const fetchPromise = this.doFetchPublicKey(keyId);
    this.refreshPromises.set(keyId, fetchPromise);

    try {
      const result = await fetchPromise;
      return result;
    } finally {
      this.refreshPromises.delete(keyId);
    }
  }

  /**
   * Actually fetch the public key from remote actor
   */
  private async doFetchPublicKey(keyId: string): Promise<CachedPublicKey | null> {
    try {
      // Extract actor URL from keyId (usually keyId is actorUrl#main-key)
      const actorUrl = keyId.split('#')[0];
      
      if (!validateUrl(actorUrl)) {
        throw new ValidationError(`Invalid actor URL in keyId: ${keyId}`, 'INVALID_ACTOR_URL');
      }

      ActivityPubLogs.security.keyFetchStarted(keyId, actorUrl);

      // Fetch actor object
      const result = await fetchAndProcessAPObject<Actor>(actorUrl, {
        timeout: 10000
      });

      if (!result.success || !result.data) {
        throw new FetchError(
          `Failed to fetch actor: ${result.error}`,
          'ACTOR_FETCH_ERROR',
          { url: actorUrl, status: result.statusCode }
        );
      }

      const actor = result.data;

      // Extract public key
      let publicKeyPem: string | null = null;
      let owner: string | null = null;

      if (actor.publicKey) {
        if (typeof actor.publicKey === 'string') {
          publicKeyPem = actor.publicKey;
          owner = actor.id?.toString() || actorUrl;
        } else if (actor.publicKey.publicKeyPem) {
          publicKeyPem = actor.publicKey.publicKeyPem;
          owner = actor.publicKey.owner || actor.id?.toString() || actorUrl;
        }
      }

      if (!publicKeyPem) {
        throw new SecurityError(`No public key found for actor: ${actorUrl}`, 'PUBLIC_KEY_NOT_FOUND');
      }

      // Create cached entry
      const now = Date.now();
      const cachedKey: CachedPublicKey = {
        keyId,
        actorUrl,
        publicKeyPem,
        algorithm: 'rsa-sha256', // Default algorithm
        owner: owner || actorUrl,
        fetchedAt: now,
        expiresAt: now + this.config.defaultTtl
      };

      // Store in cache
      this.cache.set(keyId, cachedKey);

      ActivityPubLogs.security.keyFetchCompleted(keyId, actorUrl);

      return cachedKey;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.security.keyFetchFailed(keyId, errorMessage);
      
      // Don't throw here, return null to indicate failure
      return null;
    }
  }

  /**
   * Refresh key in background without blocking
   */
  private refreshKeyInBackground(keyId: string): void {
    // Don't await this - it runs in background
    this.fetchPublicKey(keyId).catch(error => {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      ActivityPubLogs.security.keyRefreshFailed(keyId, errorMessage);
    });
  }

  /**
   * Manually invalidate a key from cache
   */
  invalidateKey(keyId: string): void {
    this.cache.delete(keyId);
    ActivityPubLogs.security.keyInvalidated(keyId);
  }

  /**
   * Clear all cached keys
   */
  clearCache(): void {
    this.cache.clear();
    ActivityPubLogs.security.keyCacheCleared();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      cache: this.cache.getStats(),
      pendingFetches: this.refreshPromises.size
    };
  }

  /**
   * Check if a key is cached and valid
   */
  hasValidKey(keyId: string): boolean {
    const cached = this.cache.get(keyId);
    if (!cached) return false;
    
    return cached.expiresAt > Date.now();
  }

  /**
   * Preload a public key into cache
   */
  async preloadKey(keyId: string): Promise<boolean> {
    try {
      const key = await this.fetchPublicKey(keyId);
      return key !== null;
    } catch {
      return false;
    }
  }
}

/**
 * Global public key cache instance
 */
export const globalKeyCache = new PublicKeyCache();

/**
 * Convenience function to get public key using global cache
 */
export async function getPublicKey(keyId: string): Promise<string | null> {
  return globalKeyCache.getPublicKey(keyId);
}

/**
 * Convenience function to invalidate key using global cache
 */
export function invalidatePublicKey(keyId: string): void {
  globalKeyCache.invalidateKey(keyId);
}
