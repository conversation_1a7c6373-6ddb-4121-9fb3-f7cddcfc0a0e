/**
 * Metrics and monitoring system for ActivityPub operations
 */

import { logger } from './logger';

/**
 * Metric types
 */
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
  TIMER = 'timer'
}

/**
 * Metric data point
 */
export interface MetricPoint {
  name: string;
  type: MetricType;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
}

/**
 * Histogram bucket
 */
export interface HistogramBucket {
  le: number; // less than or equal
  count: number;
}

/**
 * Histogram data
 */
export interface HistogramData {
  buckets: HistogramBucket[];
  sum: number;
  count: number;
}

/**
 * Timer data
 */
export interface TimerData {
  count: number;
  sum: number;
  min: number;
  max: number;
  avg: number;
  p50: number;
  p95: number;
  p99: number;
}

/**
 * Health check status
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown'
}

/**
 * Health check result
 */
export interface HealthCheck {
  name: string;
  status: HealthStatus;
  message?: string;
  duration?: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

/**
 * Metrics collector
 */
export class MetricsCollector {
  private counters = new Map<string, number>();
  private gauges = new Map<string, number>();
  private histograms = new Map<string, HistogramData>();
  private timers = new Map<string, number[]>();
  private labels = new Map<string, Record<string, string>>();

  /**
   * Increment a counter
   */
  incrementCounter(name: string, value: number = 1, labels?: Record<string, string>): void {
    const key = this.getKey(name, labels);
    const current = this.counters.get(key) || 0;
    this.counters.set(key, current + value);
    
    if (labels) {
      this.labels.set(key, labels);
    }
  }

  /**
   * Set a gauge value
   */
  setGauge(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getKey(name, labels);
    this.gauges.set(key, value);
    
    if (labels) {
      this.labels.set(key, labels);
    }
  }

  /**
   * Record a histogram value
   */
  recordHistogram(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getKey(name, labels);
    let histogram = this.histograms.get(key);
    
    if (!histogram) {
      histogram = {
        buckets: this.createDefaultBuckets(),
        sum: 0,
        count: 0
      };
      this.histograms.set(key, histogram);
    }

    histogram.sum += value;
    histogram.count++;

    // Update buckets
    for (const bucket of histogram.buckets) {
      if (value <= bucket.le) {
        bucket.count++;
      }
    }

    if (labels) {
      this.labels.set(key, labels);
    }
  }

  /**
   * Record a timer value
   */
  recordTimer(name: string, value: number, labels?: Record<string, string>): void {
    const key = this.getKey(name, labels);
    let values = this.timers.get(key);
    
    if (!values) {
      values = [];
      this.timers.set(key, values);
    }

    values.push(value);

    // Keep only last 1000 values to prevent memory issues
    if (values.length > 1000) {
      values.shift();
    }

    if (labels) {
      this.labels.set(key, labels);
    }
  }

  /**
   * Get counter value
   */
  getCounter(name: string, labels?: Record<string, string>): number {
    const key = this.getKey(name, labels);
    return this.counters.get(key) || 0;
  }

  /**
   * Get gauge value
   */
  getGauge(name: string, labels?: Record<string, string>): number {
    const key = this.getKey(name, labels);
    return this.gauges.get(key) || 0;
  }

  /**
   * Get histogram data
   */
  getHistogram(name: string, labels?: Record<string, string>): HistogramData | null {
    const key = this.getKey(name, labels);
    return this.histograms.get(key) || null;
  }

  /**
   * Get timer data
   */
  getTimer(name: string, labels?: Record<string, string>): TimerData | null {
    const key = this.getKey(name, labels);
    const values = this.timers.get(key);
    
    if (!values || values.length === 0) {
      return null;
    }

    const sorted = [...values].sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);
    
    return {
      count: values.length,
      sum,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: sum / values.length,
      p50: this.percentile(sorted, 0.5),
      p95: this.percentile(sorted, 0.95),
      p99: this.percentile(sorted, 0.99)
    };
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): MetricPoint[] {
    const metrics: MetricPoint[] = [];
    const timestamp = Date.now();

    // Counters
    for (const [key, value] of this.counters) {
      metrics.push({
        name: this.getNameFromKey(key),
        type: MetricType.COUNTER,
        value,
        timestamp,
        labels: this.labels.get(key)
      });
    }

    // Gauges
    for (const [key, value] of this.gauges) {
      metrics.push({
        name: this.getNameFromKey(key),
        type: MetricType.GAUGE,
        value,
        timestamp,
        labels: this.labels.get(key)
      });
    }

    return metrics;
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
    this.timers.clear();
    this.labels.clear();
  }

  private getKey(name: string, labels?: Record<string, string>): string {
    if (!labels || Object.keys(labels).length === 0) {
      return name;
    }
    
    const labelStr = Object.entries(labels)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}=${v}`)
      .join(',');
    
    return `${name}{${labelStr}}`;
  }

  private getNameFromKey(key: string): string {
    const braceIndex = key.indexOf('{');
    return braceIndex === -1 ? key : key.substring(0, braceIndex);
  }

  private createDefaultBuckets(): HistogramBucket[] {
    const buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, Infinity];
    return buckets.map(le => ({ le, count: 0 }));
  }

  private percentile(sorted: number[], p: number): number {
    const index = Math.ceil(sorted.length * p) - 1;
    return sorted[Math.max(0, index)];
  }
}

/**
 * Health checker
 */
export class HealthChecker {
  private checks = new Map<string, () => Promise<HealthCheck>>();

  /**
   * Register a health check
   */
  register(name: string, check: () => Promise<HealthCheck>): void {
    this.checks.set(name, check);
  }

  /**
   * Run all health checks
   */
  async runAll(): Promise<HealthCheck[]> {
    const results: HealthCheck[] = [];
    
    for (const [name, check] of this.checks) {
      try {
        const result = await check();
        results.push(result);
      } catch (error) {
        results.push({
          name,
          status: HealthStatus.UNHEALTHY,
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now()
        });
      }
    }

    return results;
  }

  /**
   * Run a specific health check
   */
  async run(name: string): Promise<HealthCheck | null> {
    const check = this.checks.get(name);
    if (!check) return null;

    try {
      return await check();
    } catch (error) {
      return {
        name,
        status: HealthStatus.UNHEALTHY,
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get overall health status
   */
  async getOverallHealth(): Promise<{ status: HealthStatus; checks: HealthCheck[] }> {
    const checks = await this.runAll();
    
    const hasUnhealthy = checks.some(check => check.status === HealthStatus.UNHEALTHY);
    const hasDegraded = checks.some(check => check.status === HealthStatus.DEGRADED);
    
    let status: HealthStatus;
    if (hasUnhealthy) {
      status = HealthStatus.UNHEALTHY;
    } else if (hasDegraded) {
      status = HealthStatus.DEGRADED;
    } else {
      status = HealthStatus.HEALTHY;
    }

    return { status, checks };
  }
}

/**
 * Performance monitor
 */
export class PerformanceMonitor {
  private metrics: MetricsCollector;

  constructor(metrics: MetricsCollector) {
    this.metrics = metrics;
  }

  /**
   * Time a function execution
   */
  async time<T>(name: string, fn: () => Promise<T>, labels?: Record<string, string>): Promise<T> {
    const start = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - start;
      
      this.metrics.recordTimer(name, duration, labels);
      this.metrics.incrementCounter(`${name}_total`, 1, { ...labels, status: 'success' });
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      
      this.metrics.recordTimer(name, duration, labels);
      this.metrics.incrementCounter(`${name}_total`, 1, { ...labels, status: 'error' });
      
      throw error;
    }
  }

  /**
   * Create a timer
   */
  createTimer(name: string, labels?: Record<string, string>) {
    const start = Date.now();
    
    return {
      end: () => {
        const duration = Date.now() - start;
        this.metrics.recordTimer(name, duration, labels);
        return duration;
      }
    };
  }
}

/**
 * Global metrics instances
 */
export const globalMetrics = new MetricsCollector();
export const globalHealthChecker = new HealthChecker();
export const globalPerformanceMonitor = new PerformanceMonitor(globalMetrics);

/**
 * ActivityPub specific metrics
 */
export const ActivityPubMetrics = {
  /**
   * HTTP request metrics
   */
  http: {
    requestsTotal: (method: string, status: number, hostname?: string) => {
      const labels: Record<string, string> = { method, status: status.toString() };
      if (hostname) labels.hostname = hostname;
      globalMetrics.incrementCounter('ap_http_requests_total', 1, labels);
    },

    requestDuration: (duration: number, method: string, hostname?: string) => {
      const labels: Record<string, string> = { method };
      if (hostname) labels.hostname = hostname;
      globalMetrics.recordHistogram('ap_http_request_duration_seconds', duration / 1000, labels);
    },
    
    requestsInFlight: (count: number) =>
      globalMetrics.setGauge('ap_http_requests_in_flight', count),
    
    rateLimitHits: (hostname: string) =>
      globalMetrics.incrementCounter('ap_rate_limit_hits_total', 1, { hostname }),
    
    retryAttempts: (hostname: string, attempt: number) =>
      globalMetrics.incrementCounter('ap_retry_attempts_total', 1, { hostname, attempt: attempt.toString() })
  },

  /**
   * Object processing metrics
   */
  objects: {
    processed: (type: string, status: 'success' | 'error') =>
      globalMetrics.incrementCounter('ap_objects_processed_total', 1, { type, status }),
    
    processingDuration: (duration: number, type: string) =>
      globalMetrics.recordHistogram('ap_object_processing_duration_seconds', duration / 1000, { type }),
    
    validationErrors: (type: string, error: string) =>
      globalMetrics.incrementCounter('ap_validation_errors_total', 1, { type, error }),
    
    cacheHits: (type: string) =>
      globalMetrics.incrementCounter('ap_cache_hits_total', 1, { type }),
    
    cacheMisses: (type: string) =>
      globalMetrics.incrementCounter('ap_cache_misses_total', 1, { type })
  },

  /**
   * Activity metrics
   */
  activities: {
    received: (type: string, actor?: string) => {
      const labels: Record<string, string> = { type };
      if (actor) labels.actor = actor;
      globalMetrics.incrementCounter('ap_activities_received_total', 1, labels);
    },
    
    processed: (type: string, status: 'success' | 'error') =>
      globalMetrics.incrementCounter('ap_activities_processed_total', 1, { type, status }),
    
    queueSize: (size: number) =>
      globalMetrics.setGauge('ap_activity_queue_size', size),
    
    processingTime: (duration: number, type: string) =>
      globalMetrics.recordTimer('ap_activity_processing_time', duration, { type })
  },

  /**
   * Federation metrics
   */
  federation: {
    domainsConnected: (count: number) =>
      globalMetrics.setGauge('ap_federation_domains_connected', count),
    
    deliveryAttempts: (hostname: string, status: 'success' | 'failure') =>
      globalMetrics.incrementCounter('ap_delivery_attempts_total', 1, { hostname, status }),
    
    deliveryLatency: (duration: number, hostname: string) =>
      globalMetrics.recordHistogram('ap_delivery_latency_seconds', duration / 1000, { hostname })
  }
} as const;

/**
 * Default health checks for ActivityPub services
 */
export function registerDefaultHealthChecks(): void {
  // Database connectivity check
  globalHealthChecker.register('database', async (): Promise<HealthCheck> => {
    const start = Date.now();

    try {
      // This would be replaced with actual database check
      // For now, we'll simulate a check
      await new Promise(resolve => setTimeout(resolve, 10));

      return {
        name: 'database',
        status: HealthStatus.HEALTHY,
        message: 'Database connection is healthy',
        duration: Date.now() - start,
        timestamp: Date.now()
      };
    } catch (error) {
      return {
        name: 'database',
        status: HealthStatus.UNHEALTHY,
        message: error instanceof Error ? error.message : 'Database connection failed',
        duration: Date.now() - start,
        timestamp: Date.now()
      };
    }
  });

  // Rate limiter health check
  globalHealthChecker.register('rate_limiter', async (): Promise<HealthCheck> => {
    const start = Date.now();

    try {
      const globalMetrics = await import('../rateLimiter').then(m => m.getGlobalMetrics());
      const healthScore = globalMetrics.totalRequests > 0
        ? 1 - (globalMetrics.totalRateLimitHits / globalMetrics.totalRequests)
        : 1;

      let status: HealthStatus;
      if (healthScore >= 0.9) {
        status = HealthStatus.HEALTHY;
      } else if (healthScore >= 0.7) {
        status = HealthStatus.DEGRADED;
      } else {
        status = HealthStatus.UNHEALTHY;
      }

      return {
        name: 'rate_limiter',
        status,
        message: `Rate limiter health score: ${(healthScore * 100).toFixed(1)}%`,
        duration: Date.now() - start,
        timestamp: Date.now(),
        metadata: globalMetrics
      };
    } catch (error) {
      return {
        name: 'rate_limiter',
        status: HealthStatus.UNKNOWN,
        message: 'Unable to check rate limiter status',
        duration: Date.now() - start,
        timestamp: Date.now()
      };
    }
  });

  // Memory usage check
  globalHealthChecker.register('memory', async (): Promise<HealthCheck> => {
    const start = Date.now();

    try {
      // In Node.js environment, we could use process.memoryUsage()
      // For now, we'll simulate a memory check
      const memoryUsage = {
        used: 100 * 1024 * 1024, // 100MB
        total: 512 * 1024 * 1024  // 512MB
      };

      const usagePercent = (memoryUsage.used / memoryUsage.total) * 100;

      let status: HealthStatus;
      if (usagePercent < 70) {
        status = HealthStatus.HEALTHY;
      } else if (usagePercent < 85) {
        status = HealthStatus.DEGRADED;
      } else {
        status = HealthStatus.UNHEALTHY;
      }

      return {
        name: 'memory',
        status,
        message: `Memory usage: ${usagePercent.toFixed(1)}%`,
        duration: Date.now() - start,
        timestamp: Date.now(),
        metadata: { usagePercent, ...memoryUsage }
      };
    } catch (error) {
      return {
        name: 'memory',
        status: HealthStatus.UNKNOWN,
        message: 'Unable to check memory usage',
        duration: Date.now() - start,
        timestamp: Date.now()
      };
    }
  });

  // External connectivity check
  globalHealthChecker.register('external_connectivity', async (): Promise<HealthCheck> => {
    const start = Date.now();

    try {
      // Test connectivity to a well-known ActivityPub server
      const testUrl = 'https://mastodon.social/.well-known/nodeinfo';
      const response = await fetch(testUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });

      const status = response.ok ? HealthStatus.HEALTHY : HealthStatus.DEGRADED;

      return {
        name: 'external_connectivity',
        status,
        message: `External connectivity test: ${response.status}`,
        duration: Date.now() - start,
        timestamp: Date.now(),
        metadata: { testUrl, status: response.status }
      };
    } catch (error) {
      return {
        name: 'external_connectivity',
        status: HealthStatus.UNHEALTHY,
        message: 'External connectivity test failed',
        duration: Date.now() - start,
        timestamp: Date.now(),
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  });
}

/**
 * Metrics middleware for tracking HTTP requests
 */
export function createMetricsMiddleware() {
  return {
    onRequest: (url: string, method: string) => {
      const timer = globalPerformanceMonitor.createTimer('ap_http_request', { method });
      const hostname = new URL(url).hostname;

      ActivityPubMetrics.http.requestsInFlight(
        globalMetrics.getGauge('ap_http_requests_in_flight') + 1
      );

      return {
        timer,
        hostname,
        onResponse: (status: number) => {
          const duration = timer.end();

          ActivityPubMetrics.http.requestsTotal(method, status, hostname);
          ActivityPubMetrics.http.requestDuration(duration, method, hostname);
          ActivityPubMetrics.http.requestsInFlight(
            globalMetrics.getGauge('ap_http_requests_in_flight') - 1
          );
        },
        onError: (error: Error) => {
          timer.end();

          ActivityPubMetrics.http.requestsTotal(method, 0, hostname);
          ActivityPubMetrics.http.requestsInFlight(
            globalMetrics.getGauge('ap_http_requests_in_flight') - 1
          );

          logger.error('HTTP request failed', {
            operation: 'http.request',
            url,
            hostname,
            error: error.message
          });
        }
      };
    }
  };
}

/**
 * Initialize metrics system
 */
export function initializeMetrics(): void {
  registerDefaultHealthChecks();

  // Log metrics periodically
  setInterval(() => {
    const metrics = globalMetrics.getAllMetrics();
    if (metrics.length > 0) {
      logger.debug('Metrics snapshot', {
        operation: 'metrics.snapshot',
        metadata: { metricsCount: metrics.length }
      });
    }
  }, 60000); // Every minute
}

/**
 * Get metrics summary for monitoring dashboards
 */
export function getMetricsSummary(): Record<string, any> {
  const allMetrics = globalMetrics.getAllMetrics();
  const summary: Record<string, any> = {};

  for (const metric of allMetrics) {
    const key = metric.labels
      ? `${metric.name}[${Object.entries(metric.labels).map(([k, v]) => `${k}=${v}`).join(',')}]`
      : metric.name;

    summary[key] = {
      type: metric.type,
      value: metric.value,
      timestamp: metric.timestamp
    };
  }

  return summary;
}
