/**
 * Safe processing utilities for ActivityPub operations
 */

import { DEFAULT_PROCESSING_LIMITS } from './constants';

export interface ProcessingContext {
  visitedUrls: Set<string>;
  currentDepth: number;
  maxDepth: number;
  maxCollectionSize: number;
  timeout: number;
  startTime: number;
}

export interface ProcessingResult {
  processed: number;
  errors: number;
  skipped: number;
  warnings: string[];
}

export interface SafetyCheckResult {
  safe: boolean;
  reason?: string;
}

/**
 * Create a new processing context with default values
 */
export function createProcessingContext(overrides: Partial<ProcessingContext> = {}): ProcessingContext {
  return {
    visitedUrls: new Set<string>(),
    currentDepth: 0,
    maxDepth: DEFAULT_PROCESSING_LIMITS.maxDepth,
    maxCollectionSize: DEFAULT_PROCESSING_LIMITS.maxCollectionSize,
    timeout: DEFAULT_PROCESSING_LIMITS.timeout,
    startTime: Date.now(),
    ...overrides
  };
}

/**
 * Check if processing is safe to continue
 */
export function checkProcessingSafety(url: string, context: ProcessingContext): SafetyCheckResult {
  // Check for circular references
  if (context.visitedUrls.has(url)) {
    return { safe: false, reason: `Circular reference detected: ${url}` };
  }
  
  // Check recursion depth
  if (context.currentDepth >= context.maxDepth) {
    return { safe: false, reason: `Maximum depth ${context.maxDepth} exceeded` };
  }
  
  // Check global timeout
  if (Date.now() - context.startTime > context.timeout) {
    return { safe: false, reason: 'Global timeout exceeded' };
  }
  
  return { safe: true };
}

/**
 * Process items with timeout and error handling
 */
export async function processWithTimeout<T>(
  operation: () => Promise<T>,
  timeout: number = DEFAULT_PROCESSING_LIMITS.itemTimeout
): Promise<{ success: boolean; result?: T; error?: string }> {
  try {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Operation timeout')), timeout);
    });
    
    const result = await Promise.race([operation(), timeoutPromise]);
    
    return { success: true, result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Process items in batches with error handling
 */
export async function processItemsBatch<T, R>(
  items: T[],
  processor: (item: T, index: number) => Promise<R>,
  options: {
    batchSize?: number;
    continueOnError?: boolean;
    timeout?: number;
  } = {}
): Promise<{
  results: Array<{ success: boolean; result?: R; error?: string; index: number }>;
  summary: ProcessingResult;
}> {
  const {
    batchSize = 10,
    continueOnError = true,
    timeout = DEFAULT_PROCESSING_LIMITS.itemTimeout
  } = options;

  const results: Array<{ success: boolean; result?: R; error?: string; index: number }> = [];
  const summary: ProcessingResult = {
    processed: 0,
    errors: 0,
    skipped: 0,
    warnings: []
  };

  // Process items in batches
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (item, batchIndex) => {
      const globalIndex = i + batchIndex;
      
      const result = await processWithTimeout(
        () => processor(item, globalIndex),
        timeout
      );
      
      return {
        ...result,
        index: globalIndex
      };
    });

    try {
      const batchResults = await Promise.allSettled(batchPromises);
      
      for (const settledResult of batchResults) {
        if (settledResult.status === 'fulfilled') {
          const result = settledResult.value;
          results.push(result);
          
          if (result.success) {
            summary.processed++;
          } else {
            summary.errors++;
            if (result.error) {
              summary.warnings.push(`Item ${result.index}: ${result.error}`);
            }
          }
        } else {
          const error = settledResult.reason;
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Promise rejected',
            index: i + results.length
          });
          summary.errors++;
          summary.warnings.push(`Batch processing failed: ${error}`);
        }
      }
    } catch (error) {
      if (!continueOnError) {
        throw error;
      }
      
      // Mark entire batch as failed
      for (let j = 0; j < batch.length; j++) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Batch failed',
          index: i + j
        });
        summary.errors++;
      }
      
      summary.warnings.push(`Batch ${Math.floor(i / batchSize)} failed: ${error}`);
    }
  }

  return { results, summary };
}

/**
 * Merge multiple processing results
 */
export function mergeProcessingResults(...results: ProcessingResult[]): ProcessingResult {
  return results.reduce((merged, result) => ({
    processed: merged.processed + result.processed,
    errors: merged.errors + result.errors,
    skipped: merged.skipped + result.skipped,
    warnings: [...merged.warnings, ...result.warnings]
  }), {
    processed: 0,
    errors: 0,
    skipped: 0,
    warnings: []
  });
}
