/**
 * Utilities for handling quote posts and references
 */

import { narrowByType } from '$lib/activitypub/types';
import { getUrlFromAPObjectSafe } from './links';
import type { Notelike, Note, Question } from '$lib/activitypub/types';
import type { LinkEntity } from '$lib/activitypub/types/Core/Link';

/**
 * Quote field names used by different ActivityPub implementations
 */
export const QUOTE_FIELDS = ['quoteUri', 'quoteUrl', '_misskey_quote'] as const;

export type QuoteField = typeof QUOTE_FIELDS[number];

/**
 * Resolve quote URL from a post object
 */
export function resolveQuoteUrl(post: Notelike): string | null {
  // Check standard quote fields for Note and Question types
  if (narrowByType<Note | Question>(post, ['Note', 'Question'])) {
    for (const field of QUOTE_FIELDS) {
      if (post[field]) {
        const url = getUrlFromAPObjectSafe(post[field]);
        if (url) {
          return url;
        }
      }
    }
  }

  // Check for quote in tags (used by some implementations)
  const quoteFromTags = resolveQuoteFromTags(post);
  if (quoteFromTags) {
    return quoteFromTags;
  }

  return null;
}

/**
 * Resolve quote URL from post tags
 */
export function resolveQuoteFromTags(post: Notelike): string | null {
  if (!post.tag || !Array.isArray(post.tag)) {
    return null;
  }

  for (const tag of post.tag) {
    if (!('type' in tag)) {
      continue;
    }

    // Look for Link tags with ActivityStreams JSON-LD media type
    if (
      narrowByType<LinkEntity>(tag, ['Link']) &&
      tag.href &&
      tag.mediaType &&
      /^application\/ld\+json/.test(tag.mediaType)
    ) {
      const url = getUrlFromAPObjectSafe(tag);
      if (url) {
        return url;
      }
    }
  }

  return null;
}

/**
 * Check if a post is a quote post
 */
export function isQuotePost(post: Notelike): boolean {
  return resolveQuoteUrl(post) !== null;
}

/**
 * Extract all quote references from a post
 */
export function extractQuoteReferences(post: Notelike): {
  primaryQuote: string | null;
  allQuotes: string[];
  quoteFields: Array<{ field: string; url: string }>;
} {
  const allQuotes: string[] = [];
  const quoteFields: Array<{ field: string; url: string }> = [];

  // Check standard quote fields
  if (narrowByType<Note | Question>(post, ['Note', 'Question'])) {
    for (const field of QUOTE_FIELDS) {
      if (post[field]) {
        const url = getUrlFromAPObjectSafe(post[field]);
        if (url) {
          allQuotes.push(url);
          quoteFields.push({ field, url });
        }
      }
    }
  }

  // Check tags
  const tagQuote = resolveQuoteFromTags(post);
  if (tagQuote) {
    allQuotes.push(tagQuote);
    quoteFields.push({ field: 'tag', url: tagQuote });
  }

  // Remove duplicates
  const uniqueQuotes = [...new Set(allQuotes)];

  return {
    primaryQuote: uniqueQuotes[0] || null,
    allQuotes: uniqueQuotes,
    quoteFields
  };
}

/**
 * Validate quote URL
 */
export function isValidQuoteUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'https:' || urlObj.protocol === 'http:';
  } catch {
    return false;
  }
}

/**
 * Normalize quote URL (remove fragments, normalize protocol, etc.)
 */
export function normalizeQuoteUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    // Remove fragment
    urlObj.hash = '';
    
    // Normalize protocol to https if possible
    if (urlObj.protocol === 'http:' && urlObj.hostname !== 'localhost') {
      urlObj.protocol = 'https:';
    }
    
    return urlObj.toString();
  } catch {
    return null;
  }
}

/**
 * Check if quote URL points to the same domain as the post
 */
export function isSelfQuote(post: Notelike, quoteUrl: string): boolean {
  if (!post.id) {
    return false;
  }

  try {
    const postUrl = new URL(post.id.toString());
    const quoteUrlObj = new URL(quoteUrl);
    
    return postUrl.hostname === quoteUrlObj.hostname;
  } catch {
    return false;
  }
}

/**
 * Extract quote context (surrounding text around quote references)
 */
export function extractQuoteContext(post: Notelike): {
  hasQuoteContext: boolean;
  contextBefore: string | null;
  contextAfter: string | null;
  fullContent: string | null;
} {
  const content = post.content || post.name || '';
  const quoteUrl = resolveQuoteUrl(post);

  if (!quoteUrl || !content) {
    return {
      hasQuoteContext: false,
      contextBefore: null,
      contextAfter: null,
      fullContent: content || null
    };
  }

  // Try to find the quote URL in the content
  const quoteIndex = content.indexOf(quoteUrl);
  
  if (quoteIndex === -1) {
    return {
      hasQuoteContext: true,
      contextBefore: content,
      contextAfter: null,
      fullContent: content
    };
  }

  const contextBefore = content.substring(0, quoteIndex).trim();
  const contextAfter = content.substring(quoteIndex + quoteUrl.length).trim();

  return {
    hasQuoteContext: true,
    contextBefore: contextBefore || null,
    contextAfter: contextAfter || null,
    fullContent: content
  };
}
