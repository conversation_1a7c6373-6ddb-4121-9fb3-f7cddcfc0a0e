/**
 * Activity delivery system for ActivityPub federation.
 * Handles reliable delivery of activities to remote inboxes with retry logic and status tracking.
 */

import { db } from '$lib/server/db';
import { activityDelivery, activity as activityTable } from '$lib/server/db/schema';
import { eq, and, lte, isNull, or } from 'drizzle-orm';
import { signedPost } from './http-client';
import { getActorKeysForSigning } from './actor-keys';
import { getFederationConfig } from '$lib/activitypub/config/federation';
import { withRetry, RetryStrategies } from './retry';
import { ActivityPubLogs } from './logger';
import { ActivityPubMetrics } from './metrics';
import type { 
  Activity, 
  ActivityDeliveryStatus, 
  DeliveryResult, 
  DeliveryAttempt 
} from '$lib/activitypub/types';

/**
 * Delivery priority levels
 */
export enum DeliveryPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  URGENT = 3
}

/**
 * Activity type to priority mapping
 */
const ACTIVITY_PRIORITIES: Record<string, DeliveryPriority> = {
  'Delete': DeliveryPriority.URGENT,
  'Block': DeliveryPriority.URGENT,
  'Undo': DeliveryPriority.HIGH,
  'Accept': DeliveryPriority.HIGH,
  'Reject': DeliveryPriority.HIGH,
  'Follow': DeliveryPriority.NORMAL,
  'Create': DeliveryPriority.NORMAL,
  'Update': DeliveryPriority.NORMAL,
  'Like': DeliveryPriority.LOW,
  'Announce': DeliveryPriority.LOW,
  'Add': DeliveryPriority.LOW,
  'Remove': DeliveryPriority.LOW
};

/**
 * Delivery options
 */
export interface DeliveryOptions {
  priority?: DeliveryPriority;
  maxRetries?: number;
  timeout?: number;
  delayMs?: number;
}

/**
 * Delivery queue item
 */
export interface DeliveryQueueItem {
  id: string;
  activityId: string;
  recipientUri: string;
  recipientActorUri: string;
  priority: DeliveryPriority;
  attempts: number;
  maxAttempts: number;
  nextAttemptAt: Date | null;
  lastError?: string;
}

/**
 * Delivery statistics
 */
export interface DeliveryStats {
  pending: number;
  delivered: number;
  failed: number;
  permanentFailures: number;
  totalAttempts: number;
  averageAttempts: number;
}

/**
 * Schedule activity for delivery to a recipient inbox
 */
export async function scheduleDelivery(
  activityId: string,
  recipientInbox: string,
  recipientActor: string,
  options: DeliveryOptions = {}
): Promise<string> {
  const federationConfig = getFederationConfig();
  
  const deliveryRecord = {
    activityId,
    recipientUri: recipientInbox,
    recipientActorUri: recipientActor,
    status: 'pending' as ActivityDeliveryStatus,
    attempts: 0,
    maxAttempts: options.maxRetries || federationConfig.delivery.maxRetries,
    nextAttemptAt: options.delayMs ? new Date(Date.now() + options.delayMs) : new Date(),
    lastAttemptAt: null,
    lastHttpStatus: null,
    lastErrorMessage: null
  };

  const result = await db.insert(activityDelivery).values(deliveryRecord).returning();
  
  if (!result[0]) {
    throw new Error('Failed to schedule delivery');
  }

  ActivityPubLogs.federation.deliveryScheduled(
    activityId,
    recipientInbox,
    options.priority || DeliveryPriority.NORMAL
  );

  return result[0].id;
}

/**
 * Schedule activity delivery to multiple recipients
 */
export async function scheduleMultipleDeliveries(
  activityId: string,
  recipients: Array<{ inbox: string; actor: string }>,
  options: DeliveryOptions = {}
): Promise<string[]> {
  const deliveryIds: string[] = [];
  
  for (const recipient of recipients) {
    try {
      const deliveryId = await scheduleDelivery(
        activityId,
        recipient.inbox,
        recipient.actor,
        options
      );
      deliveryIds.push(deliveryId);
    } catch (error) {
      ActivityPubLogs.federation.deliverySchedulingFailed(
        activityId,
        recipient.inbox,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  return deliveryIds;
}

/**
 * Get pending deliveries ready for processing
 */
export async function getPendingDeliveries(
  limit: number = 50,
  priority?: DeliveryPriority
): Promise<DeliveryQueueItem[]> {
  const now = new Date();
  
  let query = db
    .select({
      id: activityDelivery.id,
      activityId: activityDelivery.activityId,
      recipientUri: activityDelivery.recipientUri,
      recipientActorUri: activityDelivery.recipientActorUri,
      attempts: activityDelivery.attempts,
      maxAttempts: activityDelivery.maxAttempts,
      nextAttemptAt: activityDelivery.nextAttemptAt,
      lastErrorMessage: activityDelivery.lastErrorMessage
    })
    .from(activityDelivery)
    .where(
      and(
        eq(activityDelivery.status, 'pending'),
        or(
          isNull(activityDelivery.nextAttemptAt),
          lte(activityDelivery.nextAttemptAt, now)
        )
      )
    )
    .limit(limit);

  const results = await query;

  return results.map(row => ({
    id: row.id,
    activityId: row.activityId,
    recipientUri: row.recipientUri,
    recipientActorUri: row.recipientActorUri,
    priority: DeliveryPriority.NORMAL, // TODO: Add priority to schema
    attempts: row.attempts,
    maxAttempts: row.maxAttempts,
    nextAttemptAt: row.nextAttemptAt,
    lastError: row.lastErrorMessage || undefined
  }));
}

/**
 * Attempt to deliver an activity
 */
export async function attemptDelivery(
  deliveryItem: DeliveryQueueItem,
  userId: string
): Promise<DeliveryResult> {
  const startTime = Date.now();
  
  try {
    // Get activity data
    const activityRecord = await db
      .select()
      .from(activityTable)
      .where(eq(activityTable.id, deliveryItem.activityId))
      .limit(1);

    if (!activityRecord[0]) {
      throw new Error(`Activity not found: ${deliveryItem.activityId}`);
    }

    const activity = activityRecord[0].activityPubObject as Activity;

    // Get actor keys for signing
    const keyPair = await getActorKeysForSigning(userId);
    if (!keyPair) {
      throw new Error(`No keys found for user: ${userId}`);
    }

    // Attempt delivery with retry
    const deliveryResult = await withRetry(
      async () => {
        ActivityPubLogs.federation.deliveryAttemptStarted(
          deliveryItem.id,
          deliveryItem.recipientUri,
          deliveryItem.attempts + 1
        );

        const response = await signedPost(
          deliveryItem.recipientUri,
          activity,
          keyPair.privateKey,
          keyPair.keyId
        );

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      },
      RetryStrategies.conservative
    );

    const duration = Date.now() - startTime;
    const hostname = new URL(deliveryItem.recipientUri).hostname;

    if (deliveryResult.success) {
      // Update delivery status to delivered
      await updateDeliveryStatus(
        deliveryItem.id,
        'delivered',
        deliveryItem.attempts + 1,
        200,
        null
      );

      ActivityPubLogs.federation.deliverySucceeded(
        deliveryItem.id,
        deliveryItem.recipientUri,
        duration
      );

      ActivityPubMetrics.federation.deliveryAttempts(hostname, 'success');
      ActivityPubMetrics.federation.deliveryLatency(duration, hostname);

      return {
        recipientInbox: deliveryItem.recipientUri,
        status: 'delivered',
        attempts: [{
          recipientInbox: deliveryItem.recipientUri,
          recipientActor: deliveryItem.recipientActorUri,
          attempt: deliveryItem.attempts + 1,
          httpStatus: 200,
          timestamp: new Date()
        }]
      };
    } else {
      throw deliveryResult.error || new Error('Delivery failed');
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const duration = Date.now() - startTime;
    const hostname = new URL(deliveryItem.recipientUri).hostname;

    ActivityPubLogs.federation.deliveryFailed(
      deliveryItem.id,
      deliveryItem.recipientUri,
      errorMessage,
      deliveryItem.attempts + 1
    );

    ActivityPubMetrics.federation.deliveryAttempts(hostname, 'failure');

    // Determine if this is a permanent failure
    const isPermanent = isPermanentFailure(error);
    const newAttempts = deliveryItem.attempts + 1;
    const maxReached = newAttempts >= deliveryItem.maxAttempts;

    if (isPermanent || maxReached) {
      // Mark as permanent failure
      await updateDeliveryStatus(
        deliveryItem.id,
        'permanent_failure',
        newAttempts,
        null,
        errorMessage
      );

      return {
        recipientInbox: deliveryItem.recipientUri,
        status: 'permanent_failure',
        attempts: [{
          recipientInbox: deliveryItem.recipientUri,
          recipientActor: deliveryItem.recipientActorUri,
          attempt: newAttempts,
          error: errorMessage,
          timestamp: new Date()
        }],
        finalError: errorMessage
      };
    } else {
      // Schedule retry
      const federationConfig = getFederationConfig();
      const retryDelay = federationConfig.delivery.retryDelays[
        Math.min(newAttempts - 1, federationConfig.delivery.retryDelays.length - 1)
      ];
      const nextAttemptAt = new Date(Date.now() + retryDelay);

      await updateDeliveryStatus(
        deliveryItem.id,
        'failed',
        newAttempts,
        null,
        errorMessage,
        nextAttemptAt
      );

      return {
        recipientInbox: deliveryItem.recipientUri,
        status: 'failed',
        attempts: [{
          recipientInbox: deliveryItem.recipientUri,
          recipientActor: deliveryItem.recipientActorUri,
          attempt: newAttempts,
          error: errorMessage,
          timestamp: new Date()
        }],
        nextRetryAt: nextAttemptAt
      };
    }
  }
}

/**
 * Update delivery status in database
 */
async function updateDeliveryStatus(
  deliveryId: string,
  status: ActivityDeliveryStatus,
  attempts: number,
  httpStatus: number | null,
  errorMessage: string | null,
  nextAttemptAt?: Date
): Promise<void> {
  await db
    .update(activityDelivery)
    .set({
      status,
      attempts,
      lastAttemptAt: new Date(),
      lastHttpStatus: httpStatus,
      lastErrorMessage: errorMessage,
      nextAttemptAt: nextAttemptAt || null
    })
    .where(eq(activityDelivery.id, deliveryId));
}

/**
 * Check if an error represents a permanent failure
 */
function isPermanentFailure(error: unknown): boolean {
  if (!(error instanceof Error)) return false;

  const message = error.message.toLowerCase();

  // HTTP status codes that indicate permanent failures
  if (message.includes('http 4')) {
    const statusMatch = message.match(/http (\d+)/);
    if (statusMatch) {
      const status = parseInt(statusMatch[1]);
      // 4xx errors except 408 (timeout), 429 (rate limit)
      return status >= 400 && status < 500 && status !== 408 && status !== 429;
    }
  }

  // DNS or network errors that might be permanent
  if (message.includes('dns') || message.includes('not found')) {
    return true;
  }

  return false;
}

/**
 * Process delivery queue in batches
 */
export async function processDeliveryQueue(
  userId: string,
  batchSize: number = 10,
  maxConcurrency: number = 5
): Promise<{
  processed: number;
  delivered: number;
  failed: number;
  permanentFailures: number;
  errors: string[];
}> {
  const federationConfig = getFederationConfig();
  const concurrency = Math.min(maxConcurrency, federationConfig.delivery.concurrency);

  let totalProcessed = 0;
  let totalDelivered = 0;
  let totalFailed = 0;
  let totalPermanentFailures = 0;
  const errors: string[] = [];

  ActivityPubLogs.federation.deliveryBatchStarted(batchSize, concurrency);

  try {
    // Get pending deliveries
    const pendingDeliveries = await getPendingDeliveries(batchSize);

    if (pendingDeliveries.length === 0) {
      ActivityPubLogs.federation.deliveryBatchCompleted(0, 0, 0, 0);
      return {
        processed: 0,
        delivered: 0,
        failed: 0,
        permanentFailures: 0,
        errors: []
      };
    }

    // Process deliveries with controlled concurrency
    const semaphore = new Array(concurrency).fill(null);
    const deliveryPromises: Promise<void>[] = [];

    for (const delivery of pendingDeliveries) {
      const deliveryPromise = (async () => {
        // Wait for available slot
        await Promise.race(semaphore.map((_, index) =>
          semaphore[index] || Promise.resolve(index)
        ));

        try {
          const result = await attemptDelivery(delivery, userId);
          totalProcessed++;

          switch (result.status) {
            case 'delivered':
              totalDelivered++;
              break;
            case 'failed':
              totalFailed++;
              break;
            case 'permanent_failure':
              totalPermanentFailures++;
              if (result.finalError) {
                errors.push(`${delivery.recipientUri}: ${result.finalError}`);
              }
              break;
          }
        } catch (error) {
          totalProcessed++;
          totalFailed++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`${delivery.recipientUri}: ${errorMessage}`);

          ActivityPubLogs.federation.deliveryFailed(
            delivery.id,
            delivery.recipientUri,
            errorMessage,
            delivery.attempts + 1
          );
        }
      })();

      deliveryPromises.push(deliveryPromise);
    }

    // Wait for all deliveries to complete
    await Promise.allSettled(deliveryPromises);

    ActivityPubLogs.federation.deliveryBatchCompleted(
      totalProcessed,
      totalDelivered,
      totalFailed,
      totalPermanentFailures
    );

    return {
      processed: totalProcessed,
      delivered: totalDelivered,
      failed: totalFailed,
      permanentFailures: totalPermanentFailures,
      errors
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    ActivityPubLogs.federation.deliveryBatchFailed(errorMessage);

    return {
      processed: totalProcessed,
      delivered: totalDelivered,
      failed: totalFailed,
      permanentFailures: totalPermanentFailures,
      errors: [errorMessage]
    };
  }
}

/**
 * Get delivery statistics
 */
export async function getDeliveryStats(): Promise<DeliveryStats> {
  const stats = await db
    .select({
      status: activityDelivery.status,
      attempts: activityDelivery.attempts
    })
    .from(activityDelivery);

  const pending = stats.filter(s => s.status === 'pending').length;
  const delivered = stats.filter(s => s.status === 'delivered').length;
  const failed = stats.filter(s => s.status === 'failed').length;
  const permanentFailures = stats.filter(s => s.status === 'permanent_failure').length;

  const totalAttempts = stats.reduce((sum, s) => sum + s.attempts, 0);
  const averageAttempts = stats.length > 0 ? totalAttempts / stats.length : 0;

  return {
    pending,
    delivered,
    failed,
    permanentFailures,
    totalAttempts,
    averageAttempts
  };
}

/**
 * Clean up old delivery records
 */
export async function cleanupOldDeliveries(
  olderThanDays: number = 30
): Promise<number> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

  const result = await db
    .delete(activityDelivery)
    .where(
      and(
        or(
          eq(activityDelivery.status, 'delivered'),
          eq(activityDelivery.status, 'permanent_failure')
        ),
        lte(activityDelivery.lastAttemptAt, cutoffDate)
      )
    )
    .returning();

  ActivityPubLogs.federation.deliveryCleanupCompleted(result.length, olderThanDays);

  return result.length;
}

/**
 * Get priority for activity type
 */
export function getActivityPriority(activityType: string): DeliveryPriority {
  return ACTIVITY_PRIORITIES[activityType] || DeliveryPriority.NORMAL;
}
