import { describe, it, expect, vi, beforeEach } from 'vitest';
import { validateInboxRequest, createInboxProcessingContext } from './inbox-validation.js';
import {
  createTestCreateActivity,
  createTestPerson,
  mockValidationSuccess
} from '../../../test/activitypub-helpers.js';

// Mock dependencies
vi.mock('./http-signature', () => ({
  validateIncomingSignature: vi.fn(),
  extractSignature: vi.fn()
}));

vi.mock('../config/federation', () => ({
  getFederationConfig: vi.fn()
}));

vi.mock('./validation', () => ({
  isValidActivityPubObject: vi.fn(),
  isValidActivity: vi.fn()
}));

vi.mock('./security', () => ({
  isSafeContent: vi.fn(),
  isSecureUrl: vi.fn()
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    security: {
      blockedRequest: vi.fn(),
      signatureVerificationFailed: vi.fn(),
      suspiciousActivity: vi.fn()
    }
  }
}));

vi.mock('./activities', () => ({
  extractActivityActor: vi.fn()
}));

import { validateIncomingSignature, extractSignature } from './http-signature';
import { getFederationConfig } from '../config/federation';
import { isValidActivityPubObject, isValidActivity } from './validation';
import { isSafeContent, isSecureUrl } from './security';
import { ActivityPubLogs } from './logger';
import { extractActivityActor } from './activities';

describe('Inbox Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mocks
    vi.mocked(getFederationConfig).mockReturnValue({
      security: {
        maxActivitySize: 1048576,
        requireHttpSignature: false,
        allowedActorTypes: ['Person', 'Service', 'Application'],
        blockedDomains: [],
        allowedDomains: undefined,
        maxAttachmentSize: 10485760
      }
    } as any);
    
    vi.mocked(isValidActivityPubObject).mockReturnValue(true);
    vi.mocked(isValidActivity).mockReturnValue(true);
    vi.mocked(isSafeContent).mockReturnValue(true);
    vi.mocked(isSecureUrl).mockReturnValue(true);
    vi.mocked(extractActivityActor).mockReturnValue(new URL('https://example.com/users/testuser'));
    vi.mocked(validateIncomingSignature).mockResolvedValue({ valid: true });
    vi.mocked(extractSignature).mockReturnValue({
      keyId: 'https://example.com/users/testuser#main-key',
      algorithm: 'rsa-sha256',
      headers: ['(request-target)', 'host', 'date', 'digest'],
      signature: 'test-signature'
    });
  });

  describe('validateInboxRequest', () => {
    it('should validate a correct ActivityPub request', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = {
        'content-type': 'application/activity+json',
        'signature': 'keyId="test",signature="test"'
      };

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody,
        '***********'
      );

      // Assert
      expect(result.valid).toBe(true);
      expect(result.activity).toEqual(activity);
      expect(result.signature).toBeDefined();
    });

    it('should reject requests that are too large', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = { 'content-type': 'application/activity+json' };

      vi.mocked(getFederationConfig).mockReturnValue({
        security: { maxActivitySize: 10 }
      } as any);

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(false);
      expect(result.statusCode).toBe(413);
      expect(result.error).toBe('Activity size exceeds maximum allowed');
      expect(ActivityPubLogs.security.blockedRequest).toHaveBeenCalledWith(
        'https://test.example.com/users/testuser/inbox',
        'Activity size too large'
      );
    });

    it('should reject malformed JSON', async () => {
      // Arrange
      const rawBody = '{ invalid json';
      const headers = { 'content-type': 'application/activity+json' };

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(false);
      expect(result.statusCode).toBe(400);
      expect(result.error).toContain('Invalid JSON');
    });

    it('should reject invalid ActivityPub objects', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = { 'content-type': 'application/activity+json' };

      vi.mocked(isValidActivityPubObject).mockReturnValue(false);

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(false);
      expect(result.statusCode).toBe(400);
      expect(result.error).toBe('Invalid ActivityPub object');
    });

    it('should reject unsafe content', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = { 'content-type': 'application/activity+json' };

      vi.mocked(isSafeContent).mockReturnValue(false);

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(false);
      expect(result.statusCode).toBe(403);
      expect(result.error).toContain('Unsafe content detected');
    });

    it('should require HTTP signature when configured', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = { 'content-type': 'application/activity+json' };

      vi.mocked(getFederationConfig).mockReturnValue({
        security: { 
          maxActivitySize: 1048576,
          requireHttpSignature: true 
        }
      } as any);
      
      vi.mocked(validateIncomingSignature).mockResolvedValue({ 
        valid: false, 
        error: 'No signature provided' 
      });

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(false);
      expect(result.statusCode).toBe(401);
      expect(result.error).toBe('No signature provided');
    });

    it('should warn about missing signature when not required', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const rawBody = JSON.stringify(activity);
      const headers = { 'content-type': 'application/activity+json' };

      vi.mocked(validateIncomingSignature).mockResolvedValue({ 
        valid: false, 
        error: 'No signature provided' 
      });

      // Act
      const result = await validateInboxRequest(
        'POST',
        'https://test.example.com/users/testuser/inbox',
        headers,
        rawBody
      );

      // Assert
      expect(result.valid).toBe(true);
      expect(ActivityPubLogs.security.signatureVerificationFailed).toHaveBeenCalledWith(
        'unknown',
        'https://test.example.com/users/testuser/inbox',
        'No signature provided'
      );
    });
  });

  describe('createInboxProcessingContext', () => {
    it('should create processing context with all required fields', () => {
      // Arrange
      const activity = createTestCreateActivity();
      const signature = {
        keyId: 'https://example.com/users/testuser#main-key',
        algorithm: 'rsa-sha256',
        headers: ['(request-target)', 'host', 'date', 'digest'],
        signature: 'test-signature'
      };
      const rawActivity = JSON.stringify(activity);
      const sourceIp = '***********';

      // Act
      const context = createInboxProcessingContext(activity, signature, rawActivity, sourceIp);

      // Assert
      expect(context.activity).toBe(activity);
      expect(context.direction).toBe('inbound');
      expect(context.signature).toBe(signature);
      expect(context.rawActivity).toBe(rawActivity);
      expect(context.receivedAt).toBeInstanceOf(Date);
    });

    it('should use stringified activity as fallback for rawActivity', () => {
      // Arrange
      const activity = createTestCreateActivity();

      // Act
      const context = createInboxProcessingContext(activity);

      // Assert
      expect(context.rawActivity).toBe(JSON.stringify(activity));
    });
  });
});
