/**
 * Utilities for safe URL handling and validation
 */

import type { LinkReference } from '$lib/activitypub/types/Core/Link';
import type { CoreObject } from '$lib/activitypub/types/Core';
import type { OrArray } from '$lib/activitypub/types/util';
import type { Collection, OrderedCollection } from '$lib/activitypub/types';
import { InvalidUrlError, UnsafeContentError } from './errors';

/**
 * Dangerous URL patterns to block
 */
const DANGEROUS_PROTOCOLS = [
  'javascript:',
  'data:',
  'vbscript:',
  'file:',
  'ftp:'
];

const DANGEROUS_DOMAINS = [
  'localhost',
  '127.0.0.1',
  '0.0.0.0',
  '::1'
];

const PRIVATE_IP_RANGES = [
  /^10\./,
  /^172\.(1[6-9]|2[0-9]|3[01])\./,
  /^192\.168\./,
  /^169\.254\./, // Link-local
  /^224\./, // Multicast
  /^240\./ // Reserved
];

/**
 * URL validation options
 */
export interface UrlValidationOptions {
  allowHttp?: boolean;
  allowPrivateIPs?: boolean;
  allowLocalhost?: boolean;
  maxLength?: number;
  allowedDomains?: string[];
  blockedDomains?: string[];
}

/**
 * Default validation options for ActivityPub URLs
 */
export const DEFAULT_URL_OPTIONS: UrlValidationOptions = {
  allowHttp: false, // Only HTTPS by default
  allowPrivateIPs: false,
  allowLocalhost: false,
  maxLength: 2048,
  allowedDomains: [],
  blockedDomains: DANGEROUS_DOMAINS
};

/**
 * Validate URL for security and format
 */
export function validateUrl(url: string, options: UrlValidationOptions = {}): {
  isValid: boolean;
  url?: URL;
  errors: string[];
} {
  const opts = { ...DEFAULT_URL_OPTIONS, ...options };
  const errors: string[] = [];

  // Length check
  if (url.length > opts.maxLength!) {
    errors.push(`URL too long: ${url.length} > ${opts.maxLength}`);
  }

  // Parse URL
  let urlObj: URL;
  try {
    urlObj = new URL(url);
  } catch {
    errors.push('Invalid URL format');
    return { isValid: false, errors };
  }

  // Protocol validation
  if (DANGEROUS_PROTOCOLS.includes(urlObj.protocol)) {
    errors.push(`Dangerous protocol: ${urlObj.protocol}`);
  }

  if (!opts.allowHttp && urlObj.protocol === 'http:') {
    errors.push('HTTP not allowed, use HTTPS');
  }

  if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
    errors.push(`Unsupported protocol: ${urlObj.protocol}`);
  }

  // Domain validation
  const hostname = urlObj.hostname.toLowerCase();

  if (!opts.allowLocalhost && DANGEROUS_DOMAINS.includes(hostname)) {
    errors.push(`Blocked domain: ${hostname}`);
  }

  if (opts.blockedDomains?.includes(hostname)) {
    errors.push(`Domain in blocklist: ${hostname}`);
  }

  if (opts.allowedDomains?.length && !opts.allowedDomains.includes(hostname)) {
    errors.push(`Domain not in allowlist: ${hostname}`);
  }

  // Private IP validation
  if (!opts.allowPrivateIPs && isPrivateIP(hostname)) {
    errors.push(`Private IP address not allowed: ${hostname}`);
  }

  // Port validation
  if (urlObj.port) {
    const port = parseInt(urlObj.port);
    if (port < 1 || port > 65535) {
      errors.push(`Invalid port: ${port}`);
    }
    
    // Block dangerous ports
    const dangerousPorts = [22, 23, 25, 53, 110, 143, 993, 995];
    if (dangerousPorts.includes(port)) {
      errors.push(`Dangerous port: ${port}`);
    }
  }

  return {
    isValid: errors.length === 0,
    url: urlObj,
    errors
  };
}

/**
 * Check if hostname is a private IP address
 */
function isPrivateIP(hostname: string): boolean {
  // IPv6 private ranges
  if (hostname.includes(':')) {
    return hostname.startsWith('fc') || hostname.startsWith('fd') || hostname === '::1';
  }

  // IPv4 private ranges
  return PRIVATE_IP_RANGES.some(range => range.test(hostname));
}

/**
 * Safe URL extraction with validation
 */
export function getUrlFromAPObjectSafe(
  object: OrArray<CoreObject | LinkReference | string | URL>,
  options: UrlValidationOptions = {}
): string | null {
  try {
    const url = getUrlFromAPObjectUnsafe(object);
    if (!url) {
      return null;
    }

    const validation = validateUrl(url, options);
    if (!validation.isValid) {
      console.warn(`Invalid URL rejected: ${url}`, validation.errors);
      return null;
    }

    return url;
  } catch (error) {
    console.warn('Error extracting URL:', error);
    return null;
  }
}

/**
 * Unsafe URL extraction (original logic, for internal use)
 */
function getUrlFromAPObjectUnsafe(
  object: OrArray<CoreObject | LinkReference | string | URL>,
  depth = 0
): string | null {
  // Prevent infinite recursion
  if (depth > 10) {
    throw new Error('Maximum recursion depth exceeded');
  }

  if (typeof object === 'string') {
    return object;
  }

  if (object instanceof URL) {
    return object.toString();
  }

  if (Array.isArray(object)) {
    if (object.length === 0) {
      return null;
    }
    return getUrlFromAPObjectUnsafe(object[0], depth + 1);
  }

  if (!object || typeof object !== 'object') {
    return null;
  }

  // Try href first (Link objects)
  if ('href' in object && object.href) {
    return getUrlFromAPObjectUnsafe(object.href, depth + 1);
  }

  // Try url property
  if ('url' in object && object.url) {
    return getUrlFromAPObjectUnsafe(object.url, depth + 1);
  }

  // Try id property
  if ('id' in object && object.id) {
    return getUrlFromAPObjectUnsafe(object.id, depth + 1);
  }

  // Handle collections
  if ('type' in object && (object.type === 'OrderedCollection' || object.type === 'Collection')) {
    const collection = object as Collection | OrderedCollection;
    
    if ('orderedItems' in collection && collection.orderedItems) {
      return getUrlFromAPObjectUnsafe(collection.orderedItems, depth + 1);
    }
    
    if ('items' in collection && collection.items) {
      return getUrlFromAPObjectUnsafe(collection.items, depth + 1);
    }
  }

  return null;
}

/**
 * Extract URL with error throwing for required URLs
 */
export function getUrlFromAPObjectRequired(
  object: OrArray<CoreObject | LinkReference | string | URL>,
  fieldName: string,
  options: UrlValidationOptions = {}
): string {
  const url = getUrlFromAPObjectSafe(object, options);
  
  if (!url) {
    throw new InvalidUrlError('null', fieldName);
  }
  
  return url;
}

/**
 * Extract multiple URLs from an array or single object
 */
export function getUrlsFromAPObjects(
  objects: OrArray<CoreObject | LinkReference | string | URL>,
  options: UrlValidationOptions = {}
): string[] {
  if (!objects) {
    return [];
  }

  const objectArray = Array.isArray(objects) ? objects : [objects];
  const urls: string[] = [];

  for (const obj of objectArray) {
    const url = getUrlFromAPObjectSafe(obj, options);
    if (url) {
      urls.push(url);
    }
  }

  return [...new Set(urls)]; // Remove duplicates
}

/**
 * Normalize URL (remove fragments, normalize case, etc.)
 */
export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // Remove fragment
    urlObj.hash = '';
    
    // Normalize hostname to lowercase
    urlObj.hostname = urlObj.hostname.toLowerCase();
    
    // Remove default ports
    if ((urlObj.protocol === 'https:' && urlObj.port === '443') ||
        (urlObj.protocol === 'http:' && urlObj.port === '80')) {
      urlObj.port = '';
    }
    
    // Normalize path
    urlObj.pathname = urlObj.pathname.replace(/\/+/g, '/');
    if (urlObj.pathname.endsWith('/') && urlObj.pathname.length > 1) {
      urlObj.pathname = urlObj.pathname.slice(0, -1);
    }
    
    return urlObj.toString();
  } catch {
    return url; // Return original if normalization fails
  }
}

/**
 * Check if URL is from the same domain
 */
export function isSameDomain(url1: string, url2: string): boolean {
  try {
    const domain1 = new URL(url1).hostname.toLowerCase();
    const domain2 = new URL(url2).hostname.toLowerCase();
    return domain1 === domain2;
  } catch {
    return false;
  }
}

/**
 * Check if URL is external (different domain from base)
 */
export function isExternalUrl(url: string, baseUrl: string): boolean {
  return !isSameDomain(url, baseUrl);
}

/**
 * Create safe URL validation options for different contexts
 */
export const UrlValidationPresets = {
  // Strict validation for external ActivityPub objects
  activityPub: {
    allowHttp: false,
    allowPrivateIPs: false,
    allowLocalhost: false,
    maxLength: 2048
  } as UrlValidationOptions,

  // Relaxed validation for development
  development: {
    allowHttp: true,
    allowPrivateIPs: true,
    allowLocalhost: true,
    maxLength: 4096
  } as UrlValidationOptions,

  // Validation for media URLs
  media: {
    allowHttp: false,
    allowPrivateIPs: false,
    allowLocalhost: false,
    maxLength: 1024
  } as UrlValidationOptions,

  // Validation for actor URLs
  actor: {
    allowHttp: false,
    allowPrivateIPs: false,
    allowLocalhost: false,
    maxLength: 512
  } as UrlValidationOptions
} as const;
