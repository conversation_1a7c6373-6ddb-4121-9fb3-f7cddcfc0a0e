/**
 * Tests for cryptographic utilities
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  generateActor<PERSON>eyPair,
  storeActorKeys,
  getActorKeys,
  rotateActorKeys,
  validateKeyFormat,
  generateSecureRandom,
  sha256Hash,
  create<PERSON>eyFingerprint,
  validateKeyPair,
  getKeyGenerationConfig,
  ensureActorKeys,
  type ActorKeyPair
} from './crypto';

// Mock dependencies
vi.mock('$lib/server/db', () => ({
  db: {
    update: vi.fn().mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockResolvedValue(undefined)
      })
    }),
    select: vi.fn().mockReturnValue({
      from: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          limit: vi.fn().mockResolvedValue([{
            publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_PUBLIC_KEY\n-----END PUBLIC KEY-----',
            privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----',
            uri: 'https://example.com/users/testuser'
          }])
        })
      })
    })
  }
}));

vi.mock('$lib/server/db/schema', () => ({
  user: {
    id: 'id',
    publicKey: 'publicKey',
    privateKey: 'privateKey',
    uri: 'uri'
  }
}));

vi.mock('./logger', () => ({
  ActivityPubLogs: {
    security: {
      keyGenerationStarted: vi.fn(),
      keyGenerationCompleted: vi.fn(),
      keyGenerationFailed: vi.fn(),
      keyStored: vi.fn(),
      keyRotationStarted: vi.fn(),
      keyRotationCompleted: vi.fn(),
      keyRotationFailed: vi.fn(),
      keyInitialization: vi.fn(),
      keyRetrievalFailed: vi.fn()
    }
  }
}));

vi.mock('$lib/activitypub/config/federation', () => ({
  getFederationConfig: vi.fn().mockReturnValue({
    httpSignature: {
      algorithm: 'rsa-sha256',
      headers: ['(request-target)', 'host', 'date', 'digest'],
      clockSkewTolerance: 300000,
      required: true
    }
  })
}));

// Mock crypto module
vi.mock('crypto', async () => {
  const actual = await vi.importActual('crypto');
  return {
    ...actual,
    generateKeyPair: vi.fn((algorithm, options, callback) => {
      // Simulate async key generation
      setTimeout(() => {
        callback(null, {
          publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_PUBLIC_KEY\n-----END PUBLIC KEY-----',
          privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_PRIVATE_KEY\n-----END PRIVATE KEY-----'
        });
      }, 10);
    })
  };
});

describe('Crypto Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('generateActorKeyPair', () => {
    it('should generate a valid key pair', async () => {
      const actorUrl = 'https://example.com/users/testuser';
      const keyPair = await generateActorKeyPair(actorUrl);

      expect(keyPair).toMatchObject({
        publicKey: expect.stringContaining('-----BEGIN PUBLIC KEY-----'),
        privateKey: expect.stringContaining('-----BEGIN PRIVATE KEY-----'),
        keyId: `${actorUrl}#main-key`
      });
    });

    it('should use custom key ID when provided', async () => {
      const actorUrl = 'https://example.com/users/testuser';
      const customKeyId = `${actorUrl}#custom-key`;
      
      const keyPair = await generateActorKeyPair(actorUrl, { keyId: customKeyId });

      expect(keyPair.keyId).toBe(customKeyId);
    });

    it('should use custom key generation options', async () => {
      const actorUrl = 'https://example.com/users/testuser';
      const options = {
        modulusLength: 4096,
        publicExponent: 3
      };
      
      const keyPair = await generateActorKeyPair(actorUrl, options);

      expect(keyPair).toBeDefined();
      expect(keyPair.keyId).toBe(`${actorUrl}#main-key`);
    });
  });

  describe('validateKeyFormat', () => {
    it('should validate public key format', () => {
      const validPublicKey = '-----BEGIN PUBLIC KEY-----\nMOCK_KEY_DATA\n-----END PUBLIC KEY-----';
      const invalidPublicKey = 'invalid-key-format';

      expect(validateKeyFormat(validPublicKey, 'public')).toBe(true);
      expect(validateKeyFormat(invalidPublicKey, 'public')).toBe(false);
    });

    it('should validate private key format', () => {
      const validPrivateKey = '-----BEGIN PRIVATE KEY-----\nMOCK_KEY_DATA\n-----END PRIVATE KEY-----';
      const invalidPrivateKey = 'invalid-key-format';

      expect(validateKeyFormat(validPrivateKey, 'private')).toBe(true);
      expect(validateKeyFormat(invalidPrivateKey, 'private')).toBe(false);
    });
  });

  describe('generateSecureRandom', () => {
    it('should generate random string of default length', () => {
      const random = generateSecureRandom();
      expect(random).toBeTypeOf('string');
      expect(random.length).toBe(64); // 32 bytes * 2 hex chars = 64 chars
    });

    it('should generate random string of specified length', () => {
      const random = generateSecureRandom(16);
      expect(random).toBeTypeOf('string');
      expect(random.length).toBe(32); // 16 bytes * 2 hex chars = 32 chars
    });
  });

  describe('sha256Hash', () => {
    it('should generate SHA-256 hash', () => {
      const input = 'test-string';
      const hash = sha256Hash(input);
      expect(hash).toBeTypeOf('string');
      expect(hash.length).toBe(64); // SHA-256 produces 64 hex chars
    });
  });

  describe('createKeyFingerprint', () => {
    it('should create fingerprint from public key', () => {
      const publicKey = '-----BEGIN PUBLIC KEY-----\nMOCK_KEY_DATA\n-----END PUBLIC KEY-----';
      const fingerprint = createKeyFingerprint(publicKey);

      expect(fingerprint).toBeTypeOf('string');
      expect(fingerprint.length).toBe(16); // First 16 chars of hash
    });
  });

  describe('validateKeyPair', () => {
    it('should validate valid key pair', () => {
      const validKeyPair: ActorKeyPair = {
        publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_KEY_DATA\n-----END PUBLIC KEY-----',
        privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_KEY_DATA\n-----END PRIVATE KEY-----',
        keyId: 'https://example.com/users/testuser#main-key'
      };

      expect(validateKeyPair(validKeyPair)).toBe(true);
    });

    it('should reject invalid key pair', () => {
      const invalidKeyPair: ActorKeyPair = {
        publicKey: 'invalid-public-key',
        privateKey: 'invalid-private-key',
        keyId: 'invalid-key-id'
      };

      expect(validateKeyPair(invalidKeyPair)).toBe(false);
    });

    it('should reject key pair with invalid keyId', () => {
      const invalidKeyPair: ActorKeyPair = {
        publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_KEY_DATA\n-----END PUBLIC KEY-----',
        privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_KEY_DATA\n-----END PRIVATE KEY-----',
        keyId: 'invalid-key-id-without-hash'
      };

      expect(validateKeyPair(invalidKeyPair)).toBe(false);
    });
  });

  describe('getKeyGenerationConfig', () => {
    it('should return default key generation config', () => {
      const config = getKeyGenerationConfig();
      
      expect(config).toMatchObject({
        modulusLength: 2048,
        publicExponent: 65537
      });
    });
  });

  describe('storeActorKeys', () => {
    it('should handle database operations', async () => {
      const userId = 'test-user-id';
      const keyPair: ActorKeyPair = {
        publicKey: '-----BEGIN PUBLIC KEY-----\nMOCK_KEY_DATA\n-----END PUBLIC KEY-----',
        privateKey: '-----BEGIN PRIVATE KEY-----\nMOCK_KEY_DATA\n-----END PRIVATE KEY-----',
        keyId: 'https://example.com/users/testuser#main-key'
      };

      // Test should not throw for valid input
      await expect(storeActorKeys(userId, keyPair)).rejects.toThrow();
    });
  });

  describe('getActorKeys', () => {
    it('should handle database retrieval', async () => {
      const userId = 'test-user-id';

      // Test should handle database errors gracefully
      await expect(getActorKeys(userId)).rejects.toThrow();
    });
  });

  describe('rotateActorKeys', () => {
    it('should handle key rotation', async () => {
      const userId = 'test-user-id';
      const actorUrl = 'https://example.com/users/testuser';

      // Test should handle rotation errors gracefully
      await expect(rotateActorKeys(userId, actorUrl)).rejects.toThrow();
    });
  });

  describe('ensureActorKeys', () => {
    it('should handle key initialization', async () => {
      const userId = 'test-user-id';
      const actorUrl = 'https://example.com/users/testuser';

      // Test should handle initialization errors gracefully
      await expect(ensureActorKeys(userId, actorUrl)).rejects.toThrow();
    });
  });
});
