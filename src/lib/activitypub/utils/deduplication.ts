/**
 * Deduplication and idempotency utilities for ActivityPub operations.
 * Prevents duplicate processing of activities and ensures idempotent operations.
 */

import { db } from '$lib/server/db';
import { activityProcessing } from '$lib/server/db/schema';
import { eq, and, lte, or } from 'drizzle-orm';
import { getFederationConfig } from '$lib/activitypub/config/federation';
import { ActivityPubLogs } from './logger';
import type { Activity } from '$lib/activitypub/types';
import crypto from 'crypto';

/**
 * Processing status types
 */
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'duplicate';

/**
 * Deduplication result
 */
export interface DeduplicationResult {
  isDuplicate: boolean;
  processingId?: string;
  existingStatus?: ProcessingStatus;
  existingResult?: any;
  shouldProcess: boolean;
}

/**
 * Idempotency key generation options
 */
export interface IdempotencyOptions {
  includeTimestamp?: boolean;
  includeSource?: boolean;
  customSalt?: string;
}

/**
 * Processing record data
 */
export interface ProcessingRecord {
  id: string;
  activityUri: string;
  activityType: string;
  status: ProcessingStatus;
  idempotencyKey: string;
  startedAt?: Date;
  completedAt?: Date;
  errorMessage?: string;
  receivedAt: Date;
  sourceInbox?: string;
  httpSignatureValid?: boolean;
  result?: any;
}

/**
 * Generate idempotency key for an activity
 */
export function generateIdempotencyKey(
  activity: Activity,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): string {
  const components: string[] = [];
  
  // Always include activity ID/URI
  if (activity.id) {
    components.push(activity.id);
  }
  
  // Include activity type
  components.push(activity.type);
  
  // Include actor
  if (activity.actor) {
    const actorUri = typeof activity.actor === 'string' ? activity.actor : activity.actor.id;
    if (actorUri) {
      components.push(actorUri);
    }
  }
  
  // Include object if present
  if (activity.object) {
    const objectUri = typeof activity.object === 'string' ? activity.object : activity.object.id;
    if (objectUri) {
      components.push(objectUri);
    }
  }
  
  // Include source inbox if specified
  if (options.includeSource && sourceInbox) {
    components.push(sourceInbox);
  }
  
  // Include timestamp if specified (for time-sensitive operations)
  if (options.includeTimestamp && activity.published) {
    components.push(activity.published);
  }
  
  // Include custom salt if provided
  if (options.customSalt) {
    components.push(options.customSalt);
  }
  
  // Create hash of all components
  const content = components.join('|');
  return crypto.createHash('sha256').update(content).digest('hex');
}

/**
 * Check if activity is duplicate and get existing processing info
 */
export async function checkActivityDuplication(
  activity: Activity,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<DeduplicationResult> {
  try {
    const activityUri = activity.id;
    if (!activityUri) {
      throw new Error('Activity must have an ID for deduplication');
    }
    
    const idempotencyKey = generateIdempotencyKey(activity, sourceInbox, options);
    
    // Check for existing processing record by activity URI
    const existingByUri = await db
      .select()
      .from(activityProcessing)
      .where(eq(activityProcessing.activityUri, activityUri))
      .limit(1);
    
    if (existingByUri.length > 0) {
      const existing = existingByUri[0];
      
      ActivityPubLogs.federation.activityDuplicate(
        activityUri,
        activity.type,
        existing.status
      );
      
      return {
        isDuplicate: true,
        processingId: existing.id,
        existingStatus: existing.status as ProcessingStatus,
        existingResult: existing.result,
        shouldProcess: false
      };
    }
    
    // Check for existing processing record by idempotency key
    const existingByKey = await db
      .select()
      .from(activityProcessing)
      .where(eq(activityProcessing.idempotencyKey, idempotencyKey))
      .limit(1);
    
    if (existingByKey.length > 0) {
      const existing = existingByKey[0];
      
      ActivityPubLogs.federation.activityDuplicate(
        activityUri,
        activity.type,
        existing.status
      );
      
      return {
        isDuplicate: true,
        processingId: existing.id,
        existingStatus: existing.status as ProcessingStatus,
        existingResult: existing.result,
        shouldProcess: false
      };
    }
    
    // No duplicate found
    return {
      isDuplicate: false,
      shouldProcess: true
    };
    
  } catch (error) {
    ActivityPubLogs.federation.deduplicationError(
      activity.id || 'unknown',
      error instanceof Error ? error.message : 'Unknown error'
    );
    
    // On error, allow processing to continue
    return {
      isDuplicate: false,
      shouldProcess: true
    };
  }
}

/**
 * Create new processing record
 */
export async function createProcessingRecord(
  activity: Activity,
  sourceInbox?: string,
  options: IdempotencyOptions = {}
): Promise<string> {
  const activityUri = activity.id;
  if (!activityUri) {
    throw new Error('Activity must have an ID');
  }
  
  const idempotencyKey = generateIdempotencyKey(activity, sourceInbox, options);
  
  const record = {
    activityUri,
    activityType: activity.type,
    status: 'pending' as ProcessingStatus,
    idempotencyKey,
    receivedAt: new Date(),
    sourceInbox,
    httpSignatureValid: null
  };
  
  const result = await db
    .insert(activityProcessing)
    .values(record)
    .returning({ id: activityProcessing.id });
  
  if (result.length === 0) {
    throw new Error('Failed to create processing record');
  }
  
  const processingId = result[0].id;
  
  ActivityPubLogs.federation.processingRecordCreated(
    processingId,
    activityUri,
    activity.type
  );
  
  return processingId;
}

/**
 * Update processing record status
 */
export async function updateProcessingStatus(
  processingId: string,
  status: ProcessingStatus,
  result?: any,
  errorMessage?: string
): Promise<void> {
  const updateData: any = {
    status
  };
  
  if (status === 'processing' && !updateData.startedAt) {
    updateData.startedAt = new Date();
  }
  
  if (status === 'completed' || status === 'failed') {
    updateData.completedAt = new Date();
  }
  
  if (result !== undefined) {
    updateData.result = result;
  }
  
  if (errorMessage) {
    updateData.errorMessage = errorMessage;
  }
  
  await db
    .update(activityProcessing)
    .set(updateData)
    .where(eq(activityProcessing.id, processingId));
  
  ActivityPubLogs.federation.processingStatusUpdated(
    processingId,
    status,
    errorMessage
  );
}

/**
 * Get processing record by ID
 */
export async function getProcessingRecord(processingId: string): Promise<ProcessingRecord | null> {
  const result = await db
    .select()
    .from(activityProcessing)
    .where(eq(activityProcessing.id, processingId))
    .limit(1);
  
  if (result.length === 0) {
    return null;
  }
  
  const record = result[0];
  return {
    id: record.id,
    activityUri: record.activityUri,
    activityType: record.activityType,
    status: record.status as ProcessingStatus,
    idempotencyKey: record.idempotencyKey,
    startedAt: record.startedAt || undefined,
    completedAt: record.completedAt || undefined,
    errorMessage: record.errorMessage || undefined,
    receivedAt: record.receivedAt,
    sourceInbox: record.sourceInbox || undefined,
    httpSignatureValid: record.httpSignatureValid || undefined,
    result: record.result
  };
}

/**
 * Clean up old processing records
 */
export async function cleanupOldProcessingRecords(
  olderThanHours: number = 24
): Promise<number> {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - olderThanHours);
  
  const result = await db
    .delete(activityProcessing)
    .where(
      and(
        or(
          eq(activityProcessing.status, 'completed'),
          eq(activityProcessing.status, 'failed'),
          eq(activityProcessing.status, 'duplicate')
        ),
        lte(activityProcessing.receivedAt, cutoffDate)
      )
    )
    .returning();
  
  ActivityPubLogs.federation.processingCleanupCompleted(result.length, olderThanHours);
  
  return result.length;
}

/**
 * Get processing statistics
 */
export async function getProcessingStats(): Promise<{
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  duplicate: number;
  total: number;
}> {
  const results = await db
    .select({
      status: activityProcessing.status,
      count: activityProcessing.id
    })
    .from(activityProcessing);
  
  const stats = {
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    duplicate: 0,
    total: results.length
  };
  
  for (const result of results) {
    const status = result.status as ProcessingStatus;
    if (status in stats) {
      stats[status]++;
    }
  }
  
  return stats;
}
