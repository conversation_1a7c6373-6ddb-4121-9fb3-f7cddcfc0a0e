/**
 * Middleware for integrating deduplication and idempotency with ActivityPub processing.
 * Provides wrapper functions for inbox and outbox operations.
 */

import { executeIdempotent, type IdempotentResult } from './idempotency';
import { checkActivityDuplication, createProcessingRecord, updateProcessingStatus } from './deduplication';
import { ActivityPubLogs } from './logger';
import type { Activity } from '$lib/activitypub/types';

/**
 * Middleware options for deduplication
 */
export interface DeduplicationMiddlewareOptions {
  enableDeduplication?: boolean;
  enableIdempotency?: boolean;
  includeTimestamp?: boolean;
  includeSource?: boolean;
  customSalt?: string;
}

/**
 * Default middleware options
 */
const DEFAULT_OPTIONS: DeduplicationMiddlewareOptions = {
  enableDeduplication: true,
  enableIdempotency: true,
  includeTimestamp: false,
  includeSource: true
};

/**
 * Wrap inbox activity processing with deduplication
 */
export async function withInboxDeduplication<T = any>(
  activity: Activity,
  processor: () => Promise<T>,
  sourceInbox?: string,
  options: DeduplicationMiddlewareOptions = {}
): Promise<{
  success: boolean;
  result?: T;
  isDuplicate: boolean;
  processingId: string;
  error?: string;
}> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (!opts.enableDeduplication && !opts.enableIdempotency) {
    // No deduplication, execute directly
    try {
      const result = await processor();
      return {
        success: true,
        result,
        isDuplicate: false,
        processingId: 'direct'
      };
    } catch (error) {
      return {
        success: false,
        isDuplicate: false,
        processingId: 'direct',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  if (opts.enableIdempotency) {
    // Use full idempotency
    const idempotentResult = await executeIdempotent(
      activity,
      processor,
      sourceInbox,
      {
        includeTimestamp: opts.includeTimestamp,
        includeSource: opts.includeSource,
        customSalt: opts.customSalt
      }
    );
    
    return {
      success: idempotentResult.success,
      result: idempotentResult.result,
      isDuplicate: idempotentResult.wasProcessed,
      processingId: idempotentResult.processingId,
      error: idempotentResult.error
    };
  }
  
  if (opts.enableDeduplication) {
    // Use basic deduplication only
    const duplicationCheck = await checkActivityDuplication(
      activity,
      sourceInbox,
      {
        includeTimestamp: opts.includeTimestamp,
        includeSource: opts.includeSource,
        customSalt: opts.customSalt
      }
    );
    
    if (duplicationCheck.isDuplicate) {
      return {
        success: duplicationCheck.existingStatus === 'completed',
        result: duplicationCheck.existingResult,
        isDuplicate: true,
        processingId: duplicationCheck.processingId!,
        error: duplicationCheck.existingStatus === 'failed' ? 'Previous processing failed' : undefined
      };
    }
    
    // Create processing record and execute
    const processingId = await createProcessingRecord(activity, sourceInbox, {
      includeTimestamp: opts.includeTimestamp,
      includeSource: opts.includeSource,
      customSalt: opts.customSalt
    });
    
    try {
      await updateProcessingStatus(processingId, 'processing');
      const result = await processor();
      await updateProcessingStatus(processingId, 'completed', result);
      
      return {
        success: true,
        result,
        isDuplicate: false,
        processingId
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await updateProcessingStatus(processingId, 'failed', undefined, errorMessage);
      
      return {
        success: false,
        isDuplicate: false,
        processingId,
        error: errorMessage
      };
    }
  }
  
  // Fallback (should not reach here)
  try {
    const result = await processor();
    return {
      success: true,
      result,
      isDuplicate: false,
      processingId: 'fallback'
    };
  } catch (error) {
    return {
      success: false,
      isDuplicate: false,
      processingId: 'fallback',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Wrap outbox activity processing with deduplication
 */
export async function withOutboxDeduplication<T = any>(
  activity: Activity,
  processor: () => Promise<T>,
  options: DeduplicationMiddlewareOptions = {}
): Promise<{
  success: boolean;
  result?: T;
  isDuplicate: boolean;
  processingId: string;
  error?: string;
}> {
  // For outbox, we typically don't include source inbox
  return withInboxDeduplication(activity, processor, undefined, options);
}

/**
 * Create deduplication middleware for specific activity types
 */
export function createActivityDeduplicationMiddleware(
  activityType: string,
  options: DeduplicationMiddlewareOptions = {}
) {
  return async <T = any>(
    activity: Activity,
    processor: () => Promise<T>,
    sourceInbox?: string
  ) => {
    if (activity.type !== activityType) {
      throw new Error(`Expected activity type ${activityType}, got ${activity.type}`);
    }
    
    return withInboxDeduplication(activity, processor, sourceInbox, {
      ...options,
      customSalt: `${activityType}_${options.customSalt || ''}`
    });
  };
}

/**
 * Batch deduplication for multiple activities
 */
export async function withBatchDeduplication<T = any>(
  activities: Array<{
    activity: Activity;
    processor: () => Promise<T>;
    sourceInbox?: string;
  }>,
  options: DeduplicationMiddlewareOptions = {}
): Promise<Array<{
  success: boolean;
  result?: T;
  isDuplicate: boolean;
  processingId: string;
  error?: string;
  activityId?: string;
}>> {
  const results = [];
  
  for (const { activity, processor, sourceInbox } of activities) {
    try {
      const result = await withInboxDeduplication(activity, processor, sourceInbox, options);
      results.push({
        ...result,
        activityId: activity.id
      });
    } catch (error) {
      results.push({
        success: false,
        isDuplicate: false,
        processingId: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        activityId: activity.id
      });
    }
  }
  
  return results;
}

/**
 * Check if activity should be processed (not duplicate)
 */
export async function shouldProcessActivity(
  activity: Activity,
  sourceInbox?: string,
  options: DeduplicationMiddlewareOptions = {}
): Promise<{
  shouldProcess: boolean;
  reason?: string;
  existingProcessingId?: string;
}> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  if (!opts.enableDeduplication) {
    return { shouldProcess: true };
  }
  
  const duplicationCheck = await checkActivityDuplication(
    activity,
    sourceInbox,
    {
      includeTimestamp: opts.includeTimestamp,
      includeSource: opts.includeSource,
      customSalt: opts.customSalt
    }
  );
  
  if (duplicationCheck.isDuplicate) {
    return {
      shouldProcess: false,
      reason: `Activity already processed with status: ${duplicationCheck.existingStatus}`,
      existingProcessingId: duplicationCheck.processingId
    };
  }
  
  return { shouldProcess: true };
}

/**
 * Pre-create processing record for activity
 */
export async function preCreateProcessingRecord(
  activity: Activity,
  sourceInbox?: string,
  options: DeduplicationMiddlewareOptions = {}
): Promise<string> {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  return createProcessingRecord(activity, sourceInbox, {
    includeTimestamp: opts.includeTimestamp,
    includeSource: opts.includeSource,
    customSalt: opts.customSalt
  });
}
