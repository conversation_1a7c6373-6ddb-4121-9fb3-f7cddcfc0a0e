/**
 * Utilities for efficient collection processing
 */

import { getCollectionTotalItems } from './object';
import type { EntityReference, Collection, OrderedCollection } from '$lib/activitypub/types';
import { isCollection, isCollectionType } from './types';

export interface CollectionCounts {
  replyCount: number;
  likeCount: number;
  shareCount: number;
}

export interface CollectionCountsResult {
  success: boolean;
  counts: CollectionCounts;
  errors: string[];
}

/**
 * Safely get collection total items, handling EntityReference types
 */
async function getCollectionTotalItemsSafe(ref: EntityReference): Promise<number> {
  try {
    // Handle URL references
    if (typeof ref === 'string' || ref instanceof URL) {
      return await getCollectionTotalItems(ref);
    }

    // Handle object references
    if (typeof ref === 'object' && ref !== null) {
      // Check if it's already a collection
      if (isCollection(ref)) {
        return await getCollectionTotalItems(ref as Collection | OrderedCollection);
      }

      // If it has an id, try to fetch it as a URL
      if ('id' in ref && ref.id) {
        const urlStr = typeof ref.id === 'string' ? ref.id : ref.id.toString();
        return await getCollectionTotalItems(new URL(urlStr));
      }
    }

    return 0;
  } catch (error) {
    console.warn('Failed to get collection total items:', error);
    return 0;
  }
}

/**
 * Get collection counts in parallel for better performance
 */
export async function getCollectionCounts(
  replies?: EntityReference,
  likes?: EntityReference,
  shares?: EntityReference,
  options: {
    timeout?: number;
    defaultValue?: number;
    continueOnError?: boolean;
  } = {}
): Promise<CollectionCountsResult> {
  const {
    timeout = 5000,
    defaultValue = 0,
    continueOnError = true
  } = options;

  const errors: string[] = [];
  const results = await Promise.allSettled([
    replies ? getCollectionTotalItemsSafe(replies) : Promise.resolve(defaultValue),
    likes ? getCollectionTotalItemsSafe(likes) : Promise.resolve(defaultValue),
    shares ? getCollectionTotalItemsSafe(shares) : Promise.resolve(defaultValue)
  ]);

  const counts: CollectionCounts = {
    replyCount: defaultValue,
    likeCount: defaultValue,
    shareCount: defaultValue
  };

  // Process replies count
  if (results[0].status === 'fulfilled') {
    counts.replyCount = results[0].value;
  } else {
    errors.push(`Failed to get reply count: ${results[0].reason}`);
    if (!continueOnError) {
      return { success: false, counts, errors };
    }
  }

  // Process likes count
  if (results[1].status === 'fulfilled') {
    counts.likeCount = results[1].value;
  } else {
    errors.push(`Failed to get like count: ${results[1].reason}`);
    if (!continueOnError) {
      return { success: false, counts, errors };
    }
  }

  // Process shares count
  if (results[2].status === 'fulfilled') {
    counts.shareCount = results[2].value;
  } else {
    errors.push(`Failed to get share count: ${results[2].reason}`);
    if (!continueOnError) {
      return { success: false, counts, errors };
    }
  }

  return {
    success: errors.length === 0,
    counts,
    errors
  };
}

/**
 * Get a single collection count with error handling
 */
export async function getSafeCollectionCount(
  collection?: EntityReference,
  options: {
    timeout?: number;
    defaultValue?: number;
  } = {}
): Promise<number> {
  const { timeout = 5000, defaultValue = 0 } = options;

  if (!collection) {
    return defaultValue;
  }

  try {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Collection count timeout')), timeout);
    });

    const countPromise = getCollectionTotalItemsSafe(collection);
    const count = await Promise.race([countPromise, timeoutPromise]);

    return typeof count === 'number' ? count : defaultValue;
  } catch (error) {
    console.warn('Failed to get collection count:', error);
    return defaultValue;
  }
}

/**
 * Batch process multiple collections
 */
export async function batchGetCollectionCounts(
  collections: Array<{
    id: string;
    collection?: EntityReference;
  }>,
  options: {
    batchSize?: number;
    timeout?: number;
    defaultValue?: number;
  } = {}
): Promise<Record<string, number>> {
  const {
    batchSize = 10,
    timeout = 5000,
    defaultValue = 0
  } = options;

  const results: Record<string, number> = {};

  // Process in batches to avoid overwhelming the server
  for (let i = 0; i < collections.length; i += batchSize) {
    const batch = collections.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async ({ id, collection }) => {
      const count = await getSafeCollectionCount(collection, { timeout, defaultValue });
      return { id, count };
    });

    try {
      const batchResults = await Promise.allSettled(batchPromises);
      
      for (const result of batchResults) {
        if (result.status === 'fulfilled') {
          results[result.value.id] = result.value.count;
        } else {
          console.warn('Batch collection count failed:', result.reason);
          // Find the corresponding collection ID and set default
          const failedIndex = batchResults.indexOf(result);
          if (failedIndex >= 0 && batch[failedIndex]) {
            results[batch[failedIndex].id] = defaultValue;
          }
        }
      }
    } catch (error) {
      console.warn('Batch processing failed:', error);
      // Set default values for the entire batch
      for (const { id } of batch) {
        results[id] = defaultValue;
      }
    }
  }

  return results;
}

/**
 * Collection processing statistics
 */
export interface CollectionStats {
  totalProcessed: number;
  successful: number;
  failed: number;
  averageTime: number;
  errors: string[];
}

/**
 * Process collections with detailed statistics
 */
export async function processCollectionsWithStats(
  collections: EntityReference[],
  options: {
    timeout?: number;
    defaultValue?: number;
  } = {}
): Promise<{
  counts: number[];
  stats: CollectionStats;
}> {
  const startTime = Date.now();
  const stats: CollectionStats = {
    totalProcessed: collections.length,
    successful: 0,
    failed: 0,
    averageTime: 0,
    errors: []
  };

  const counts: number[] = [];

  for (const collection of collections) {
    const itemStartTime = Date.now();
    
    try {
      const count = await getSafeCollectionCount(collection, options);
      counts.push(count);
      stats.successful++;
    } catch (error) {
      counts.push(options.defaultValue || 0);
      stats.failed++;
      stats.errors.push(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  const totalTime = Date.now() - startTime;
  stats.averageTime = stats.totalProcessed > 0 ? totalTime / stats.totalProcessed : 0;

  return { counts, stats };
}
