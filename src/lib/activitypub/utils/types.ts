/**
 * Type utilities for ActivityPub objects
 */

import {
  SUPPORTED_POST_TYPES,
  COLLECTION_TYPES,
  COLLECTION_PAGE_TYPES,
  ACTIVITY_TYPES
} from './constants';
import type { SupportedPostType } from './constants';
import type { CoreObject, Entity } from '$lib/activitypub/types';

/**
 * Safely extract the first type from an ActivityPub object
 */
export function getObjectType(object: CoreObject | Entity): string {
  if (!object.type) {
    return 'Unknown';
  }

  if (Array.isArray(object.type)) {
    return object.type[0] as string;
  }

  return object.type as string;
}

/**
 * Check if an object type is a supported post type
 */
export function isSupportedPostType(type: string): type is SupportedPostType {
  return SUPPORTED_POST_TYPES.includes(type as SupportedPostType);
}

/**
 * Check if an object is a supported post object
 */
export function isSupportedPostObject(object: CoreObject | Entity): boolean {
  const type = getObjectType(object);
  return isSupportedPostType(type);
}

/**
 * Check if an object type is a collection type
 */
export function isCollectionType(type: string): boolean {
  return COLLECTION_TYPES.includes(type as any);
}

/**
 * Check if an object is a collection
 */
export function isCollection(object: CoreObject | Entity): boolean {
  const type = getObjectType(object);
  return isCollectionType(type);
}

/**
 * Check if an object type is a collection page type
 */
export function isCollectionPageType(type: string): boolean {
  return COLLECTION_PAGE_TYPES.includes(type as any);
}

/**
 * Check if an object is a collection page
 */
export function isCollectionPage(object: CoreObject | Entity): boolean {
  const type = getObjectType(object);
  return isCollectionPageType(type);
}

/**
 * Check if an object type is an activity type
 */
export function isActivityType(type: string): boolean {
  return ACTIVITY_TYPES.includes(type as any);
}

/**
 * Check if an object is an activity
 */
export function isActivity(object: CoreObject | Entity): boolean {
  const type = getObjectType(object);
  return isActivityType(type);
}

/**
 * Validate that an object has required properties for its type
 */
export function validateObjectStructure(object: CoreObject | Entity): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!object.type) {
    errors.push('Object missing required "type" property');
  }

  if (!object.id) {
    errors.push('Object missing required "id" property');
  }

  const type = getObjectType(object);

  // Validate specific object types
  if (isSupportedPostType(type)) {
    if (!('content' in object) && !('name' in object)) {
      errors.push('Post object missing content or name');
    }

    if (!('attributedTo' in object)) {
      errors.push('Post object missing attributedTo');
    }
  }

  if (isActivity(object)) {
    if (!('actor' in object)) {
      errors.push('Activity missing actor');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Extract all types from an object (handles arrays)
 */
export function getAllTypes(object: CoreObject | Entity): string[] {
  if (!object.type) {
    return [];
  }

  if (Array.isArray(object.type)) {
    return object.type as string[];
  }

  return [object.type as string];
}

/**
 * Check if an object has a specific type (handles arrays)
 */
export function hasType(object: CoreObject | Entity, targetType: string): boolean {
  const types = getAllTypes(object);
  return types.includes(targetType);
}

// URL validation moved to utils/links.ts - import from there instead

/**
 * Result of validation operations
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}
