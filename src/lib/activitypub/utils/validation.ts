/**
 * Comprehensive ActivityPub object validation system
 * Provides full schema validation for all ActivityPub types
 */

import type {
  CoreObject,
  Entity,
  Activity,
  Actor,
  ExtendedObject,
  Collection,
  CollectionPage,
  Link,
  Mention,
  EntityReference,
  OrArray
} from '$lib/activitypub/types';
import { validateUrl } from './links';
import { logger } from './logger';

/**
 * Validation error with detailed context
 */
export interface ValidationError {
  path: string;
  message: string;
  severity: 'error' | 'warning';
  code?: string;
  expected?: any;
  actual?: any;
}

/**
 * Comprehensive validation result
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  metadata?: {
    type: string;
    validatedFields: string[];
    skippedFields: string[];
  };
}

/**
 * Field validation schema
 */
export interface FieldSchema {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'url' | 'date' | 'entity' | 'orarray';
  format?: 'url' | 'email' | 'date-time' | 'language-tag' | 'mime-type';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  enum?: any[];
  validator?: (value: any, context: ValidationContext) => ValidationError[];
  arrayOf?: FieldSchema;
  properties?: Record<string, FieldSchema>;
  // New property for OrArray validation
  orArrayOf?: FieldSchema;
}

/**
 * Object validation schema
 */
export interface ObjectSchema {
  type: string | string[];
  requiredFields: string[];
  optionalFields?: string[];
  fields: Record<string, FieldSchema>;
  customValidators?: Array<(obj: any, context: ValidationContext) => ValidationError[]>;
}

/**
 * Validation context
 */
export interface ValidationContext {
  path: string;
  rootObject: any;
  options: ValidationOptions;
}

/**
 * Validation options
 */
export interface ValidationOptions {
  strict?: boolean;
  allowUnknownFields?: boolean;
  validateUrls?: boolean;
  validateDates?: boolean;
  maxDepth?: number;
  currentDepth?: number;
}

/**
 * Default validation options
 */
export const DEFAULT_VALIDATION_OPTIONS: ValidationOptions = {
  strict: false,
  allowUnknownFields: true,
  validateUrls: true,
  validateDates: true,
  maxDepth: 10,
  currentDepth: 0
};

/**
 * Core ActivityPub schemas with proper OrArray support
 */
export const CORE_SCHEMAS: Record<string, ObjectSchema> = {
  // Base Object schema
  Object: {
    type: 'Object',
    requiredFields: ['type'],
    fields: {
      '@context': { type: 'orarray', orArrayOf: { type: 'string' } },
      type: { required: true, type: 'orarray', orArrayOf: { type: 'string' } },
      id: { type: 'url', format: 'url' },
      name: { type: 'string', maxLength: 500 },
      nameMap: { type: 'object' },
      summary: { type: 'string', maxLength: 5000 },
      summaryMap: { type: 'object' },
      content: { type: 'string', maxLength: 50000 },
      contentMap: { type: 'object' },
      url: { type: 'orarray', orArrayOf: { type: 'entity' } },
      attributedTo: { type: 'orarray', orArrayOf: { type: 'entity' } },
      audience: { type: 'orarray', orArrayOf: { type: 'entity' } },
      bcc: { type: 'orarray', orArrayOf: { type: 'entity' } },
      bto: { type: 'orarray', orArrayOf: { type: 'entity' } },
      cc: { type: 'orarray', orArrayOf: { type: 'entity' } },
      to: { type: 'orarray', orArrayOf: { type: 'entity' } },
      context: { type: 'orarray', orArrayOf: { type: 'entity' } },
      generator: { type: 'orarray', orArrayOf: { type: 'entity' } },
      icon: { type: 'orarray', orArrayOf: { type: 'entity' } },
      image: { type: 'orarray', orArrayOf: { type: 'entity' } },
      inReplyTo: { type: 'orarray', orArrayOf: { type: 'entity' } },
      location: { type: 'orarray', orArrayOf: { type: 'entity' } },
      preview: { type: 'orarray', orArrayOf: { type: 'entity' } },
      published: { type: 'date', format: 'date-time' },
      updated: { type: 'date', format: 'date-time' },
      replies: { type: 'entity' },
      startTime: { type: 'date', format: 'date-time' },
      endTime: { type: 'date', format: 'date-time' },
      tag: { type: 'orarray', orArrayOf: { type: 'entity' } },
      attachment: { type: 'orarray', orArrayOf: { type: 'entity' } },
      mediaType: { type: 'string', format: 'mime-type' },
      duration: { type: 'string' },
      source: { type: 'object' }
    }
  },

  // Link schema
  Link: {
    type: 'Link',
    requiredFields: ['type'],
    fields: {
      type: { required: true, enum: ['Link'] },
      href: { type: 'url', format: 'url' },
      rel: { type: 'string' },
      mediaType: { type: 'string', format: 'mime-type' },
      name: { type: 'string', maxLength: 500 },
      nameMap: { type: 'object' },
      hrefLang: { type: 'string', format: 'language-tag' },
      height: { type: 'number' },
      width: { type: 'number' },
      preview: { type: 'entity' }
    }
  },

  // Mention schema
  Mention: {
    type: 'Mention',
    requiredFields: ['type', 'href'],
    fields: {
      type: { required: true, enum: ['Mention'] },
      href: { required: true, type: 'url', format: 'url' },
      name: { type: 'string', maxLength: 500 }
    }
  }
};

/**
 * Extended Object schemas
 */
export const EXTENDED_SCHEMAS: Record<string, ObjectSchema> = {
  // Note schema
  Note: {
    type: 'Note',
    requiredFields: ['type'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Note'] },
      content: { type: 'string', maxLength: 50000 },
      contentMap: { type: 'object' }
    }
  },

  // Article schema
  Article: {
    type: 'Article',
    requiredFields: ['type'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Article'] },
      content: { type: 'string', maxLength: 100000 },
      contentMap: { type: 'object' }
    }
  },

  // Image schema
  Image: {
    type: 'Image',
    requiredFields: ['type', 'url'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Image'] },
      url: { required: true, type: 'url', format: 'url' },
      mediaType: { type: 'string', format: 'mime-type' },
      width: { type: 'number' },
      height: { type: 'number' }
    }
  },

  // Video schema
  Video: {
    type: 'Video',
    requiredFields: ['type', 'url'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Video'] },
      url: { required: true, type: 'url', format: 'url' },
      mediaType: { type: 'string', format: 'mime-type' },
      width: { type: 'number' },
      height: { type: 'number' },
      duration: { type: 'string' }
    }
  },

  // Audio schema
  Audio: {
    type: 'Audio',
    requiredFields: ['type', 'url'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Audio'] },
      url: { required: true, type: 'url', format: 'url' },
      mediaType: { type: 'string', format: 'mime-type' },
      duration: { type: 'string' }
    }
  },

  // Document schema
  Document: {
    type: 'Document',
    requiredFields: ['type', 'url'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Document'] },
      url: { required: true, type: 'url', format: 'url' },
      mediaType: { type: 'string', format: 'mime-type' }
    }
  },

  // Hashtag schema
  Hashtag: {
    type: 'Hashtag',
    requiredFields: ['type', 'name'],
    fields: {
      ...CORE_SCHEMAS.Object.fields,
      type: { required: true, enum: ['Hashtag'] },
      name: { required: true, type: 'string', pattern: /^#\w+$/ },
      href: { type: 'url', format: 'url' }
    }
  }
};

/**
 * Base actor fields
 */
const BASE_ACTOR_FIELDS = {
  ...CORE_SCHEMAS.Object.fields,
  id: { required: true, type: 'url', format: 'url' },
  inbox: { required: true, type: 'url', format: 'url' },
  outbox: { required: true, type: 'url', format: 'url' },
  following: { type: 'url', format: 'url' },
  followers: { type: 'url', format: 'url' },
  liked: { type: 'url', format: 'url' },
  preferredUsername: { type: 'string', maxLength: 100 },
  publicKey: { type: 'object' },
  manuallyApprovesFollowers: { type: 'boolean' },
  discoverable: { type: 'boolean' },
  endpoints: { type: 'object' }
} as const;

/**
 * Actor schemas
 */
export const ACTOR_SCHEMAS: Record<string, ObjectSchema> = {
  // Person schema
  Person: {
    type: 'Person',
    requiredFields: ['type', 'id', 'inbox', 'outbox'],
    fields: {
      ...BASE_ACTOR_FIELDS,
      type: { required: true, enum: ['Person'] }
    }
  },

  // Service schema
  Service: {
    type: 'Service',
    requiredFields: ['type', 'id', 'inbox', 'outbox'],
    fields: {
      ...BASE_ACTOR_FIELDS,
      type: { required: true, enum: ['Service'] }
    }
  },

  // Group schema
  Group: {
    type: 'Group',
    requiredFields: ['type', 'id', 'inbox', 'outbox'],
    fields: {
      ...BASE_ACTOR_FIELDS,
      type: { required: true, enum: ['Group'] }
    }
  },

  // Organization schema
  Organization: {
    type: 'Organization',
    requiredFields: ['type', 'id', 'inbox', 'outbox'],
    fields: {
      ...BASE_ACTOR_FIELDS,
      type: { required: true, enum: ['Organization'] }
    }
  },

  // Application schema
  Application: {
    type: 'Application',
    requiredFields: ['type', 'id', 'inbox', 'outbox'],
    fields: {
      ...BASE_ACTOR_FIELDS,
      type: { required: true, enum: ['Application'] }
    }
  }
};

/**
 * Get object type safely
 */
function getObjectType(obj: any): string {
  if (!obj || !obj.type) return 'Unknown';
  return Array.isArray(obj.type) ? obj.type[0] : obj.type;
}

/**
 * Enhanced field validation with OrArray support
 */
function validateField(
  value: any,
  schema: FieldSchema,
  context: ValidationContext
): ValidationError[] {
  const errors: ValidationError[] = [];
  const { path } = context;

  // Check if field is required
  if (schema.required && (value === undefined || value === null)) {
    errors.push({
      path,
      message: `Required field is missing`,
      severity: 'error',
      code: 'REQUIRED_FIELD_MISSING'
    });
    return errors;
  }

  // Skip validation if value is undefined/null and not required
  if (value === undefined || value === null) {
    return errors;
  }

  // OrArray validation
  if (schema.type === 'orarray' && schema.orArrayOf) {
    if (Array.isArray(value)) {
      // Validate each item in the array
      value.forEach((item, index) => {
        const itemContext = { ...context, path: `${path}[${index}]` };
        errors.push(...validateField(item, schema.orArrayOf!, itemContext));
      });
    } else {
      // Validate single value
      errors.push(...validateField(value, schema.orArrayOf, context));
    }
    return errors;
  }

  // Type validation
  if (schema.type) {
    const actualType = Array.isArray(value) ? 'array' : typeof value;

    if (schema.type === 'url') {
      if (typeof value !== 'string' && !(value instanceof URL)) {
        errors.push({
          path,
          message: `URL must be string or URL object`,
          severity: 'error',
          code: 'INVALID_URL_TYPE',
          actual: actualType
        });
      } else {
        const urlString = value instanceof URL ? value.toString() : value;
        const urlValidation = validateUrl(urlString, context.options.validateUrls ? {} : { allowHttp: true });
        if (!urlValidation.isValid) {
          errors.push({
            path,
            message: `Invalid URL: ${urlValidation.errors.join(', ')}`,
            severity: 'error',
            code: 'INVALID_URL_FORMAT'
          });
        }
      }
    } else if (schema.type === 'date') {
      if (typeof value !== 'string' && !(value instanceof Date)) {
        errors.push({
          path,
          message: `Date must be string or Date object`,
          severity: 'error',
          code: 'INVALID_DATE_TYPE',
          actual: actualType
        });
      }
    } else if (schema.type === 'entity') {
      // EntityReference validation - can be string, URL, or object with id
      if (typeof value !== 'string' && !(value instanceof URL) &&
          (typeof value !== 'object' || !value.id)) {
        errors.push({
          path,
          message: `Entity reference must be URL string or object with id`,
          severity: 'error',
          code: 'INVALID_ENTITY_REFERENCE',
          actual: actualType
        });
      }
    } else if (!['url', 'date', 'entity', 'orarray'].includes(schema.type) && schema.type !== actualType) {
      errors.push({
        path,
        message: `Type mismatch`,
        severity: 'error',
        code: 'TYPE_MISMATCH',
        expected: schema.type,
        actual: actualType
      });
    }
  }

  // Array validation
  if (schema.arrayOf && Array.isArray(value)) {
    value.forEach((item, index) => {
      const itemContext = { ...context, path: `${path}[${index}]` };
      errors.push(...validateField(item, schema.arrayOf!, itemContext));
    });
  }

  // String validation
  if (schema.type === 'string' && typeof value === 'string') {
    if (schema.minLength && value.length < schema.minLength) {
      errors.push({
        path,
        message: `String too short: ${value.length} < ${schema.minLength}`,
        severity: 'error',
        code: 'STRING_TOO_SHORT'
      });
    }

    if (schema.maxLength && value.length > schema.maxLength) {
      errors.push({
        path,
        message: `String too long: ${value.length} > ${schema.maxLength}`,
        severity: 'error',
        code: 'STRING_TOO_LONG'
      });
    }

    if (schema.pattern && !schema.pattern.test(value)) {
      errors.push({
        path,
        message: `String does not match pattern`,
        severity: 'error',
        code: 'PATTERN_MISMATCH'
      });
    }
  }

  // Enum validation
  if (schema.enum && !schema.enum.includes(value)) {
    errors.push({
      path,
      message: `Value not in allowed enum`,
      severity: 'error',
      code: 'ENUM_MISMATCH',
      expected: schema.enum,
      actual: value
    });
  }

  // Custom validator
  if (schema.validator) {
    errors.push(...schema.validator(value, context));
  }

  return errors;
}

/**
 * Base activity fields
 */
const BASE_ACTIVITY_FIELDS = {
  ...CORE_SCHEMAS.Object.fields,
  type: { required: true, type: 'string' },
  actor: { required: true, type: 'entity' },
  object: { type: 'entity' },
  target: { type: 'entity' },
  result: { type: 'entity' },
  origin: { type: 'entity' },
  instrument: { type: 'entity' }
} as const;

/**
 * Activity schemas
 */
export const ACTIVITY_SCHEMAS: Record<string, ObjectSchema> = {
  // Base Activity schema
  Activity: {
    type: 'Activity',
    requiredFields: ['type', 'actor'],
    fields: BASE_ACTIVITY_FIELDS
  },

  // Create Activity
  Create: {
    type: 'Create',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Create'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Update Activity
  Update: {
    type: 'Update',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Update'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Delete Activity
  Delete: {
    type: 'Delete',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Delete'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Follow Activity
  Follow: {
    type: 'Follow',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Follow'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Accept Activity
  Accept: {
    type: 'Accept',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Accept'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Reject Activity
  Reject: {
    type: 'Reject',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Reject'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Like Activity
  Like: {
    type: 'Like',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Like'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Announce Activity
  Announce: {
    type: 'Announce',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Announce'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Undo Activity
  Undo: {
    type: 'Undo',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Undo'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Block Activity
  Block: {
    type: 'Block',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Block'] },
      object: { required: true, type: 'entity' }
    }
  },

  // Flag Activity
  Flag: {
    type: 'Flag',
    requiredFields: ['type', 'actor', 'object'],
    fields: {
      ...BASE_ACTIVITY_FIELDS,
      type: { required: true, enum: ['Flag'] },
      object: { required: true, type: 'entity' }
    }
  }
};

/**
 * Base collection fields
 */
const BASE_COLLECTION_FIELDS = {
  ...CORE_SCHEMAS.Object.fields,
  totalItems: { type: 'number' },
  current: { type: 'entity' },
  first: { type: 'entity' },
  last: { type: 'entity' },
  items: { type: 'array' }
} as const;

/**
 * Collection schemas
 */
export const COLLECTION_SCHEMAS: Record<string, ObjectSchema> = {
  // Collection schema
  Collection: {
    type: 'Collection',
    requiredFields: ['type'],
    fields: {
      ...BASE_COLLECTION_FIELDS,
      type: { required: true, enum: ['Collection'] }
    }
  },

  // OrderedCollection schema
  OrderedCollection: {
    type: 'OrderedCollection',
    requiredFields: ['type'],
    fields: {
      ...BASE_COLLECTION_FIELDS,
      type: { required: true, enum: ['OrderedCollection'] },
      orderedItems: { type: 'array' }
    }
  },

  // CollectionPage schema
  CollectionPage: {
    type: 'CollectionPage',
    requiredFields: ['type', 'partOf'],
    fields: {
      ...BASE_COLLECTION_FIELDS,
      type: { required: true, enum: ['CollectionPage'] },
      partOf: { required: true, type: 'entity' },
      next: { type: 'entity' },
      prev: { type: 'entity' }
    }
  },

  // OrderedCollectionPage schema
  OrderedCollectionPage: {
    type: 'OrderedCollectionPage',
    requiredFields: ['type', 'partOf'],
    fields: {
      ...BASE_COLLECTION_FIELDS,
      type: { required: true, enum: ['OrderedCollectionPage'] },
      orderedItems: { type: 'array' },
      partOf: { required: true, type: 'entity' },
      next: { type: 'entity' },
      prev: { type: 'entity' },
      startIndex: { type: 'number' }
    }
  }
};

/**
 * All schemas combined
 */
export const ALL_SCHEMAS: Record<string, ObjectSchema> = {
  ...CORE_SCHEMAS,
  ...EXTENDED_SCHEMAS,
  ...ACTOR_SCHEMAS,
  ...ACTIVITY_SCHEMAS,
  ...COLLECTION_SCHEMAS
};

/**
 * Validate an object against its schema
 */
export function validateObject(
  obj: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const validatedFields: string[] = [];
  const skippedFields: string[] = [];

  // Check depth limit
  if (options.currentDepth && options.currentDepth >= (options.maxDepth || 10)) {
    warnings.push({
      path: '',
      message: 'Maximum validation depth reached',
      severity: 'warning',
      code: 'MAX_DEPTH_REACHED'
    });
    return { valid: true, errors, warnings };
  }

  // Basic object validation
  if (!obj || typeof obj !== 'object') {
    errors.push({
      path: '',
      message: 'Value must be an object',
      severity: 'error',
      code: 'NOT_OBJECT'
    });
    return { valid: false, errors, warnings };
  }

  const type = getObjectType(obj);
  const schema = ALL_SCHEMAS[type];

  if (!schema) {
    if (options.strict) {
      errors.push({
        path: 'type',
        message: `Unknown ActivityPub type: ${type}`,
        severity: 'error',
        code: 'UNKNOWN_TYPE',
        actual: type
      });
    } else {
      warnings.push({
        path: 'type',
        message: `Unknown ActivityPub type: ${type}`,
        severity: 'warning',
        code: 'UNKNOWN_TYPE',
        actual: type
      });
    }
    return { valid: errors.length === 0, errors, warnings };
  }

  const context: ValidationContext = {
    path: '',
    rootObject: obj,
    options: { ...options, currentDepth: (options.currentDepth || 0) + 1 }
  };

  // Validate required fields
  for (const fieldName of schema.requiredFields) {
    if (!(fieldName in obj)) {
      errors.push({
        path: fieldName,
        message: `Required field '${fieldName}' is missing`,
        severity: 'error',
        code: 'REQUIRED_FIELD_MISSING'
      });
    }
  }

  // Validate all fields
  for (const [fieldName, fieldSchema] of Object.entries(schema.fields)) {
    if (fieldName in obj) {
      const fieldContext = { ...context, path: fieldName };
      const fieldErrors = validateField(obj[fieldName], fieldSchema, fieldContext);

      errors.push(...fieldErrors.filter(e => e.severity === 'error'));
      warnings.push(...fieldErrors.filter(e => e.severity === 'warning'));
      validatedFields.push(fieldName);
    } else if (fieldSchema.required) {
      // Already handled in required fields check
    } else {
      skippedFields.push(fieldName);
    }
  }

  // Check for unknown fields
  if (!options.allowUnknownFields) {
    const knownFields = Object.keys(schema.fields);
    for (const fieldName of Object.keys(obj)) {
      if (!knownFields.includes(fieldName)) {
        warnings.push({
          path: fieldName,
          message: `Unknown field '${fieldName}'`,
          severity: 'warning',
          code: 'UNKNOWN_FIELD'
        });
      }
    }
  }

  // Run custom validators
  if (schema.customValidators) {
    for (const validator of schema.customValidators) {
      const customErrors = validator(obj, context);
      errors.push(...customErrors.filter(e => e.severity === 'error'));
      warnings.push(...customErrors.filter(e => e.severity === 'warning'));
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    metadata: {
      type,
      validatedFields,
      skippedFields
    }
  };
}

/**
 * Type guards with validation
 */

/**
 * Validate and assert that an object is a valid ActivityPub object
 */
export function isValidActivityPubObject(obj: any): obj is CoreObject {
  const result = validateObject(obj);
  if (!result.valid) {
    logger.warn('Invalid ActivityPub object received', {
      operation: 'validate_object_failed',
      metadata: { object: obj, errors: result.errors }
    });
  }
  return result.valid;
}

/**
 * Validate and assert that an object is a valid Activity
 */
export function isValidActivity(obj: any): obj is Activity {
  if (!obj || typeof obj !== 'object') return false;

  const type = getObjectType(obj);
  const activityTypes = Object.keys(ACTIVITY_SCHEMAS);

  if (!activityTypes.includes(type)) return false;

  const result = validateObject(obj);
  return result.valid;
}

/**
 * Validate and assert that an object is a valid Actor
 */
export function isValidActor(obj: any): obj is Actor {
  if (!obj || typeof obj !== 'object') return false;

  const type = getObjectType(obj);
  const actorTypes = Object.keys(ACTOR_SCHEMAS);

  if (!actorTypes.includes(type)) return false;

  const result = validateObject(obj);
  return result.valid;
}

/**
 * Validate and assert that an object is a valid Collection
 */
export function isValidCollection(obj: any): obj is Collection {
  if (!obj || typeof obj !== 'object') return false;

  const type = getObjectType(obj);
  const collectionTypes = Object.keys(COLLECTION_SCHEMAS);

  if (!collectionTypes.includes(type)) return false;

  const result = validateObject(obj);
  return result.valid;
}

/**
 * Specialized validation functions
 */

/**
 * Validate ActivityPub object with detailed error reporting
 */
export function validateActivityPubObject(
  obj: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  logger.debug('Validating ActivityPub object', {
    operation: 'validate_object',
    objectId: obj && obj.id ? obj.id.toString() : undefined
  });

  const result = validateObject(obj, options);

  if (!result.valid) {
    logger.warn('ActivityPub object validation failed', {
      operation: 'validate_object_failed',
      objectId: obj && obj.id ? obj.id.toString() : undefined
    });
  }

  return result;
}

/**
 * Validate Activity with activity-specific checks
 */
export function validateActivity(
  activity: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  const result = validateActivityPubObject(activity, options);

  if (!result.valid) return result;

  // Additional activity-specific validations
  const type = getObjectType(activity);
  const errors: ValidationError[] = [...result.errors];
  const warnings: ValidationError[] = [...result.warnings];

  // Check if activity type requires object
  const requiresObject = [
    'Create', 'Update', 'Delete', 'Follow', 'Accept', 'Reject',
    'Like', 'Announce', 'Undo', 'Block', 'Flag'
  ].includes(type);

  if (requiresObject && !activity.object) {
    errors.push({
      path: 'object',
      message: `Activity type '${type}' requires an object field`,
      severity: 'error',
      code: 'MISSING_REQUIRED_OBJECT'
    });
  }

  // Check if activity type requires target
  const requiresTarget = ['Add', 'Remove'].includes(type);

  if (requiresTarget && !activity.target) {
    errors.push({
      path: 'target',
      message: `Activity type '${type}' requires a target field`,
      severity: 'error',
      code: 'MISSING_REQUIRED_TARGET'
    });
  }

  return {
    ...result,
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate Actor with actor-specific checks
 */
export function validateActor(
  actor: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  const result = validateActivityPubObject(actor, options);

  if (!result.valid) return result;

  const errors: ValidationError[] = [...result.errors];
  const warnings: ValidationError[] = [...result.warnings];

  // Actor-specific validations
  if (actor.publicKey) {
    if (!actor.publicKey.id || !actor.publicKey.owner || !actor.publicKey.publicKeyPem) {
      errors.push({
        path: 'publicKey',
        message: 'Public key must have id, owner, and publicKeyPem fields',
        severity: 'error',
        code: 'INVALID_PUBLIC_KEY'
      });
    }
  }

  if (actor.endpoints) {
    if (typeof actor.endpoints !== 'object') {
      errors.push({
        path: 'endpoints',
        message: 'Endpoints must be an object',
        severity: 'error',
        code: 'INVALID_ENDPOINTS'
      });
    }
  }

  return {
    ...result,
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate Collection with collection-specific checks
 */
export function validateCollection(
  collection: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  const result = validateActivityPubObject(collection, options);

  if (!result.valid) return result;

  const errors: ValidationError[] = [...result.errors];
  const warnings: ValidationError[] = [...result.warnings];

  // Collection-specific validations
  if (collection.totalItems !== undefined) {
    if (typeof collection.totalItems !== 'number' || collection.totalItems < 0) {
      errors.push({
        path: 'totalItems',
        message: 'totalItems must be a non-negative number',
        severity: 'error',
        code: 'INVALID_TOTAL_ITEMS'
      });
    }
  }

  // Check items vs orderedItems consistency
  const type = getObjectType(collection);
  if (type === 'Collection' && collection.orderedItems) {
    warnings.push({
      path: 'orderedItems',
      message: 'Collection should use items, not orderedItems',
      severity: 'warning',
      code: 'INCONSISTENT_ITEMS_FIELD'
    });
  }

  if (type === 'OrderedCollection' && collection.items) {
    warnings.push({
      path: 'items',
      message: 'OrderedCollection should use orderedItems, not items',
      severity: 'warning',
      code: 'INCONSISTENT_ITEMS_FIELD'
    });
  }

  return {
    ...result,
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Batch validation functions
 */

/**
 * Validate multiple objects
 */
export function validateObjects(
  objects: any[],
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): { results: ValidationResult[]; summary: { valid: number; invalid: number; warnings: number } } {
  const results = objects.map(obj => validateActivityPubObject(obj, options));

  const summary = {
    valid: results.filter(r => r.valid).length,
    invalid: results.filter(r => !r.valid).length,
    warnings: results.reduce((sum, r) => sum + r.warnings.length, 0)
  };

  logger.info('Batch validation completed', {
    operation: 'batch_validation'
  });

  return { results, summary };
}

/**
 * Safe validation that doesn't throw
 */
export function validateSafely(
  obj: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): ValidationResult {
  try {
    return validateActivityPubObject(obj, options);
  } catch (error) {
    logger.error('Validation error', {
      operation: 'validation_exception'
    });

    return {
      valid: false,
      errors: [{
        path: '',
        message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
        code: 'VALIDATION_EXCEPTION'
      }],
      warnings: []
    };
  }
}

/**
 * Validation with type assertion
 */
export function validateAndAssert<T = CoreObject>(
  obj: any,
  options: ValidationOptions = DEFAULT_VALIDATION_OPTIONS
): T {
  const result = validateActivityPubObject(obj, options);

  if (!result.valid) {
    const errorMessages = result.errors.map(e => `${e.path}: ${e.message}`).join('; ');
    throw new Error(`Validation failed: ${errorMessages}`);
  }

  return obj as T;
}

/**
 * Utility function to validate OrArray fields specifically
 */
export function validateOrArrayField<T>(
  value: T | T[] | undefined,
  itemValidator: (item: T) => ValidationError[],
  fieldName: string,
  required = false
): ValidationError[] {
  const errors: ValidationError[] = [];

  if (required && (value === undefined || value === null)) {
    errors.push({
      path: fieldName,
      message: `Required field is missing`,
      severity: 'error',
      code: 'REQUIRED_FIELD_MISSING'
    });
    return errors;
  }

  if (value === undefined || value === null) {
    return errors;
  }

  if (Array.isArray(value)) {
    value.forEach((item, index) => {
      const itemErrors = itemValidator(item);
      errors.push(...itemErrors.map(error => ({
        ...error,
        path: `${fieldName}[${index}]${error.path ? '.' + error.path : ''}`
      })));
    });
  } else {
    const itemErrors = itemValidator(value);
    errors.push(...itemErrors.map(error => ({
      ...error,
      path: `${fieldName}${error.path ? '.' + error.path : ''}`
    })));
  }

  return errors;
}

/**
 * Create entity reference validator
 */
export function createEntityValidator(): (value: any) => ValidationError[] {
  return (value: any): ValidationError[] => {
    const errors: ValidationError[] = [];

    if (typeof value === 'string') {
      const urlValidation = validateUrl(value);
      if (!urlValidation.isValid) {
        errors.push({
          path: '',
          message: `Invalid entity URL: ${urlValidation.errors.join(', ')}`,
          severity: 'error',
          code: 'INVALID_ENTITY_URL'
        });
      }
    } else if (value instanceof URL) {
      const urlValidation = validateUrl(value.toString());
      if (!urlValidation.isValid) {
        errors.push({
          path: '',
          message: `Invalid entity URL: ${urlValidation.errors.join(', ')}`,
          severity: 'error',
          code: 'INVALID_ENTITY_URL'
        });
      }
    } else if (typeof value === 'object' && value !== null) {
      if (!value.id) {
        errors.push({
          path: '',
          message: 'Entity object must have an id field',
          severity: 'error',
          code: 'MISSING_ENTITY_ID'
        });
      } else {
        const urlValidation = validateUrl(value.id.toString());
        if (!urlValidation.isValid) {
          errors.push({
            path: 'id',
            message: `Invalid entity ID URL: ${urlValidation.errors.join(', ')}`,
            severity: 'error',
            code: 'INVALID_ENTITY_ID_URL'
          });
        }
      }
    } else {
      errors.push({
        path: '',
        message: 'Entity must be URL string, URL object, or object with id',
        severity: 'error',
        code: 'INVALID_ENTITY_TYPE'
      });
    }

    return errors;
  };
}

/**
 * Export validation utilities
 */
export const validation = {
  // Core validation
  validateObject,
  validateActivityPubObject,
  validateActivity,
  validateActor,
  validateCollection,

  // Type guards
  isValidActivityPubObject,
  isValidActivity,
  isValidActor,
  isValidCollection,

  // Batch operations
  validateObjects,
  validateSafely,
  validateAndAssert,

  // Schemas
  schemas: ALL_SCHEMAS,
  coreSchemas: CORE_SCHEMAS,
  extendedSchemas: EXTENDED_SCHEMAS,
  actorSchemas: ACTOR_SCHEMAS,
  activitySchemas: ACTIVITY_SCHEMAS,
  collectionSchemas: COLLECTION_SCHEMAS
};
