/**
 * Caching system for ActivityPub objects
 */

import { ActivityPubMetrics } from './metrics';
import { logger } from './logger';
import type { Entity, CoreObject } from '$lib/activitypub/types';

/**
 * Cache entry
 */
export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

/**
 * Cache statistics
 */
export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
  hitRate: number;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  maxSize: number;
  defaultTtl: number;
  maxEntries: number;
  cleanupInterval: number;
  enableMetrics: boolean;
}

/**
 * Eviction strategy
 */
export enum EvictionStrategy {
  LRU = 'lru',
  LFU = 'lfu',
  TTL = 'ttl',
  SIZE = 'size'
}

/**
 * In-memory cache implementation
 */
export class MemoryCache<T = any> {
  private entries = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    totalSize: 0,
    entryCount: 0,
    hitRate: 0
  };
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB
      defaultTtl: 3600000, // 1 hour
      maxEntries: 10000,
      cleanupInterval: 300000, // 5 minutes
      enableMetrics: true,
      ...config
    };

    this.startCleanup();
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.entries.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateMetrics('miss');
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.entries.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.updateStats();
      this.updateMetrics('miss');
      return null;
    }

    // Update access info
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hits++;
    this.updateMetrics('hit');
    this.updateStats();
    
    return entry.value;
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, ttl?: number): void {
    const size = this.calculateSize(value);
    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTtl,
      accessCount: 0,
      lastAccessed: Date.now(),
      size
    };

    // Check if we need to evict entries
    this.evictIfNeeded(size);

    // Remove existing entry if it exists
    if (this.entries.has(key)) {
      const oldEntry = this.entries.get(key)!;
      this.stats.totalSize -= oldEntry.size;
    }

    this.entries.set(key, entry);
    this.stats.sets++;
    this.stats.totalSize += size;
    this.updateStats();
    
    if (this.config.enableMetrics) {
      ActivityPubMetrics.objects.cacheHits('set');
    }
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    const entry = this.entries.get(key);
    if (!entry) return false;

    this.entries.delete(key);
    this.stats.deletes++;
    this.stats.totalSize -= entry.size;
    this.updateStats();
    
    return true;
  }

  /**
   * Clear all entries
   */
  clear(): void {
    this.entries.clear();
    this.stats.totalSize = 0;
    this.updateStats();
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.entries.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.entries.delete(key);
      this.stats.evictions++;
      this.updateStats();
      return false;
    }
    
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get all keys
   */
  keys(): string[] {
    return Array.from(this.entries.keys());
  }

  /**
   * Get cache size in bytes
   */
  size(): number {
    return this.stats.totalSize;
  }

  /**
   * Get number of entries
   */
  count(): number {
    return this.entries.size;
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.entries) {
      if (this.isExpired(entry)) {
        this.entries.delete(key);
        this.stats.totalSize -= entry.size;
        this.stats.evictions++;
        cleaned++;
      }
    }

    this.updateStats();
    
    if (cleaned > 0) {
      logger.debug('Cache cleanup completed', {
        operation: 'cache.cleanup',
        metadata: { cleaned, remaining: this.entries.size }
      });
    }

    return cleaned;
  }

  /**
   * Destroy cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }

  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private calculateSize(value: T): number {
    try {
      return JSON.stringify(value).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1000; // Default size for non-serializable objects
    }
  }

  private evictIfNeeded(newEntrySize: number): void {
    // Check entry count limit
    while (this.entries.size >= this.config.maxEntries) {
      this.evictLRU();
    }

    // Check size limit
    while (this.stats.totalSize + newEntrySize > this.config.maxSize) {
      this.evictLRU();
    }
  }

  private evictLRU(): void {
    let oldestEntry: CacheEntry<T> | null = null;
    let oldestKey = '';

    for (const [key, entry] of this.entries) {
      if (!oldestEntry || entry.lastAccessed < oldestEntry.lastAccessed) {
        oldestEntry = entry;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.entries.delete(oldestKey);
      this.stats.totalSize -= oldestEntry!.size;
      this.stats.evictions++;
    }
  }

  private updateStats(): void {
    this.stats.entryCount = this.entries.size;
    this.stats.hitRate = this.stats.hits + this.stats.misses > 0
      ? this.stats.hits / (this.stats.hits + this.stats.misses)
      : 0;
  }

  private updateMetrics(type: 'hit' | 'miss'): void {
    if (!this.config.enableMetrics) return;
    
    if (type === 'hit') {
      ActivityPubMetrics.objects.cacheHits('memory');
    } else {
      ActivityPubMetrics.objects.cacheMisses('memory');
    }
  }

  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }
}

/**
 * ActivityPub object cache
 */
export class ActivityPubCache {
  private actorCache: MemoryCache<Entity>;
  private postCache: MemoryCache<Entity>;
  private activityCache: MemoryCache<Entity>;
  private collectionCache: MemoryCache<Entity>;

  constructor() {
    this.actorCache = new MemoryCache<Entity>({
      maxSize: 50 * 1024 * 1024, // 50MB for actors
      defaultTtl: 3600000, // 1 hour
      maxEntries: 5000
    });

    this.postCache = new MemoryCache<Entity>({
      maxSize: 100 * 1024 * 1024, // 100MB for posts
      defaultTtl: 1800000, // 30 minutes
      maxEntries: 10000
    });

    this.activityCache = new MemoryCache<Entity>({
      maxSize: 25 * 1024 * 1024, // 25MB for activities
      defaultTtl: 600000, // 10 minutes
      maxEntries: 2500
    });

    this.collectionCache = new MemoryCache<Entity>({
      maxSize: 25 * 1024 * 1024, // 25MB for collections
      defaultTtl: 300000, // 5 minutes
      maxEntries: 1000
    });
  }

  /**
   * Cache an actor
   */
  cacheActor(id: string, actor: Entity): void {
    this.actorCache.set(id, actor);
    logger.debug('Actor cached', {
      operation: 'cache.actor.set',
      actorId: id
    });
  }

  /**
   * Get cached actor
   */
  getActor(id: string): Entity | null {
    const actor = this.actorCache.get(id);
    if (actor) {
      logger.debug('Actor cache hit', {
        operation: 'cache.actor.get',
        actorId: id
      });
    }
    return actor;
  }

  /**
   * Cache a post
   */
  cachePost(id: string, post: Entity): void {
    this.postCache.set(id, post);
    logger.debug('Post cached', {
      operation: 'cache.post.set',
      objectId: id
    });
  }

  /**
   * Get cached post
   */
  getPost(id: string): Entity | null {
    const post = this.postCache.get(id);
    if (post) {
      logger.debug('Post cache hit', {
        operation: 'cache.post.get',
        objectId: id
      });
    }
    return post;
  }

  /**
   * Cache an activity
   */
  cacheActivity(id: string, activity: Entity): void {
    this.activityCache.set(id, activity);
    logger.debug('Activity cached', {
      operation: 'cache.activity.set',
      objectId: id
    });
  }

  /**
   * Get cached activity
   */
  getActivity(id: string): Entity | null {
    const activity = this.activityCache.get(id);
    if (activity) {
      logger.debug('Activity cache hit', {
        operation: 'cache.activity.get',
        objectId: id
      });
    }
    return activity;
  }

  /**
   * Cache a collection
   */
  cacheCollection(id: string, collection: Entity): void {
    this.collectionCache.set(id, collection);
    logger.debug('Collection cached', {
      operation: 'cache.collection.set',
      objectId: id
    });
  }

  /**
   * Get cached collection
   */
  getCollection(id: string): Entity | null {
    const collection = this.collectionCache.get(id);
    if (collection) {
      logger.debug('Collection cache hit', {
        operation: 'cache.collection.get',
        objectId: id
      });
    }
    return collection;
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidate(pattern: string): void {
    const regex = new RegExp(pattern);
    
    [this.actorCache, this.postCache, this.activityCache, this.collectionCache].forEach(cache => {
      const keys = cache.keys();
      for (const key of keys) {
        if (regex.test(key)) {
          cache.delete(key);
        }
      }
    });

    logger.info('Cache invalidated', {
      operation: 'cache.invalidate',
      metadata: { pattern }
    });
  }

  /**
   * Get overall cache statistics
   */
  getStats(): Record<string, CacheStats> {
    return {
      actors: this.actorCache.getStats(),
      posts: this.postCache.getStats(),
      activities: this.activityCache.getStats(),
      collections: this.collectionCache.getStats()
    };
  }

  /**
   * Clear all caches
   */
  clear(): void {
    this.actorCache.clear();
    this.postCache.clear();
    this.activityCache.clear();
    this.collectionCache.clear();
    
    logger.info('All caches cleared', {
      operation: 'cache.clear'
    });
  }

  /**
   * Cleanup all caches
   */
  cleanup(): void {
    const cleaned = {
      actors: this.actorCache.cleanup(),
      posts: this.postCache.cleanup(),
      activities: this.activityCache.cleanup(),
      collections: this.collectionCache.cleanup()
    };

    logger.debug('Cache cleanup completed', {
      operation: 'cache.cleanup',
      metadata: cleaned
    });
  }

  /**
   * Destroy all caches
   */
  destroy(): void {
    this.actorCache.destroy();
    this.postCache.destroy();
    this.activityCache.destroy();
    this.collectionCache.destroy();
  }
}

/**
 * Global ActivityPub cache instance
 */
export const globalAPCache = new ActivityPubCache();
