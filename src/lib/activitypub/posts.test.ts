import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { deletePost } from './posts';
import { db } from '$lib/server/db';
import { post as postTable } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import {
  ActivityPubTestBase,
  testDataFactories,
  mockDb,
  commonMockImplementations
} from '../../test/activitypub-test-utils';

// Mock the schema
vi.mock('$lib/server/db/schema', () => ({
  post: {
    uri: 'uri'
  }
}));

describe('Post Deletion', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('deletePost', () => {
    it('should successfully delete an existing post', async () => {
      // Arrange
      const uri = 'https://example.com/posts/123';
      const mockPost = {
        id: 'post-id-123',
        uri,
        content: 'Test post content'
      };

      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockPost])
        })
      } as any);

      // Act
      const result = await deletePost(uri);

      // Assert
      expect(result).toBe(true);
      expect(db.query.post.findFirst).toHaveBeenCalledWith({
        where: eq(postTable.uri, uri)
      });
      expect(db.delete).toHaveBeenCalledWith(postTable);
    });

    it('should return false when post does not exist', async () => {
      // Arrange
      const uri = 'https://example.com/posts/nonexistent';
      
      vi.mocked(db.query.post.findFirst).mockResolvedValue(null);

      // Act
      const result = await deletePost(uri);

      // Assert
      expect(result).toBe(false);
      expect(db.query.post.findFirst).toHaveBeenCalledWith({
        where: eq(postTable.uri, uri)
      });
      expect(db.delete).not.toHaveBeenCalled();
    });

    it('should return false when deletion fails', async () => {
      // Arrange
      const uri = 'https://example.com/posts/123';
      const mockPost = {
        id: 'post-id-123',
        uri,
        content: 'Test post content'
      };

      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([]) // Empty array indicates no rows deleted
        })
      } as any);

      // Act
      const result = await deletePost(uri);

      // Assert
      expect(result).toBe(false);
      expect(db.query.post.findFirst).toHaveBeenCalledWith({
        where: eq(postTable.uri, uri)
      });
      expect(db.delete).toHaveBeenCalledWith(postTable);
    });

    it('should throw error when database operation fails', async () => {
      // Arrange
      const uri = 'https://example.com/posts/123';
      const mockPost = {
        id: 'post-id-123',
        uri,
        content: 'Test post content'
      };
      const dbError = new Error('Database connection failed');

      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockRejectedValue(dbError)
        })
      } as any);

      // Act & Assert
      await expect(deletePost(uri)).rejects.toThrow('Database connection failed');
      expect(db.query.post.findFirst).toHaveBeenCalledWith({
        where: eq(postTable.uri, uri)
      });
      expect(db.delete).toHaveBeenCalledWith(postTable);
    });

    it('should handle console logging correctly', async () => {
      // Arrange
      const uri = 'https://example.com/posts/123';
      const mockPost = {
        id: 'post-id-123',
        uri,
        content: 'Test post content'
      };

      vi.mocked(db.query.post.findFirst).mockResolvedValue(mockPost);
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockPost])
        })
      } as any);

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      await deletePost(uri);

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(`Successfully deleted post: ${uri}`);
      
      consoleSpy.mockRestore();
    });
  });
});
