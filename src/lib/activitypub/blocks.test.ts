import { describe, it, expect, vi, beforeEach } from 'vitest';
import { processBlock, isBlocked, getBlockedUsers, getBlockingUsers, removeBlock, isMutuallyBlocked } from './blocks';
import { createTestBlockActivity } from '../../test/activitypub-helpers';
import {
  ActivityPubTestBase,
  activityPubMocks,
  mockConfigurations,
  testDataFactories,
  mockDb,
  commonMockImplementations
} from '../../test/activitypub-test-utils';

// Mock dependencies using common utilities
vi.mock('./actors', () => ({
  discoverActor: vi.fn()
}));

vi.mock('$lib/activitypub/utils/links', () => ({
  getUrlFromAPObjectRequired: vi.fn(),
  getUrlFromAPObjectSafe: vi.fn()
}));

import { db } from '$lib/server/db';
import { discoverActor } from './actors';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';

describe('Block Processing', () => {
  beforeEach(() => {
    ActivityPubTestBase.setupMocks();
  });

  afterEach(() => {
    ActivityPubTestBase.cleanup();
  });

  describe('processBlock', () => {
    it('should process Block activity successfully', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');
      const mockBlocker = testDataFactories.createTestUser({
        id: 'blocker-id',
        uri: 'https://example.com/users/blocker'
      });
      const mockBlocked = testDataFactories.createTestUser({
        id: 'blocked-id',
        uri: 'https://example.com/users/blocked'
      });

      // Setup mocks using utilities
      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/blocker';
        if (field === 'object') return 'https://example.com/users/blocked';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockBlocker);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(mockBlocked);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null); // No existing block

      // Mock the insert chain properly
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'new-block-id' }])
        })
      } as any);

      // Act
      const result = await processBlock(activity);

      // Assert
      expect(result).toBe(true);
      expect(db.insert).toHaveBeenCalled();
      expect(db.query.user.findFirst).toHaveBeenCalled();
      expect(db.query.activity.findFirst).toHaveBeenCalled();
    });

    it('should return false if actor or object is missing', async () => {
      // Arrange
      const activity = { type: 'Block' } as any;

      // Act
      const result = await processBlock(activity);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false if blocker actor cannot be discovered', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/blocker';
        if (field === 'object') return 'https://example.com/users/blocked';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(null);

      // Act
      const result = await processBlock(activity);

      // Assert
      expect(result).toBe(false);
      expect(db.insert).not.toHaveBeenCalled();
    });

    it('should return true if block already exists', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');
      const mockBlocker = { id: 'blocker-id', uri: 'https://example.com/users/blocker' };
      const existingBlock = { id: 'existing-block-id' };

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/blocker';
        if (field === 'object') return 'https://example.com/users/blocked';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockBlocker);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(existingBlock);

      // Act
      const result = await processBlock(activity);

      // Assert
      expect(result).toBe(true);
      expect(db.insert).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');

      vi.mocked(getUrlFromAPObjectRequired).mockImplementation(() => {
        throw new Error('URL parsing failed');
      });

      // Act & Assert
      await expect(processBlock(activity)).rejects.toThrow('URL parsing failed');
    });
  });

  describe('isBlocked', () => {
    it('should return true if block exists', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';
      const existingBlock = { id: 'block-id' };

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(existingBlock);

      // Act
      const result = await isBlocked(blockerUri, blockedUri);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false if block does not exist', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null);

      // Act
      const result = await isBlocked(blockerUri, blockedUri);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle database errors', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.query.activity.findFirst).mockRejectedValue(new Error('Database error'));

      // Act
      const result = await isBlocked(blockerUri, blockedUri);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('getBlockedUsers', () => {
    it('should return list of blocked users', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockActivities = [
        { objectUri: 'https://example.com/users/blocked1' },
        { objectUri: 'https://example.com/users/blocked2' },
        { objectUri: null } // Should be filtered out
      ];

      vi.mocked(db.query.activity.findMany).mockResolvedValue(blockActivities);

      // Act
      const result = await getBlockedUsers(blockerUri);

      // Assert
      expect(result).toEqual([
        'https://example.com/users/blocked1',
        'https://example.com/users/blocked2'
      ]);
    });

    it('should handle database errors', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';

      vi.mocked(db.query.activity.findMany).mockRejectedValue(new Error('Database error'));

      // Act
      const result = await getBlockedUsers(blockerUri);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('removeBlock', () => {
    it('should remove block successfully', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{ id: 'updated-block-id' }])
          })
        })
      } as any);

      // Act
      const result = await removeBlock(blockerUri, blockedUri);

      // Assert
      expect(result).toBe(true);
      expect(db.update).toHaveBeenCalled();
    });

    it('should return false if block not found', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([]) // No rows updated
          })
        })
      } as any);

      // Act
      const result = await removeBlock(blockerUri, blockedUri);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.update).mockImplementation(() => {
        throw new Error('Database error');
      });

      // Act & Assert
      await expect(removeBlock(blockerUri, blockedUri)).rejects.toThrow('Database error');
    });
  });

  describe('Block Logic Implementation', () => {
    it('should remove follow relationships when blocking', async () => {
      // Arrange
      const activity = createTestBlockActivity('https://example.com/users/blocked');
      const mockBlocker = { id: 'blocker-id', uri: 'https://example.com/users/blocker' };
      const mockBlocked = { id: 'blocked-id', uri: 'https://example.com/users/blocked' };

      // Setup mocks
      vi.mocked(getUrlFromAPObjectRequired).mockImplementation((url, field) => {
        if (field === 'actor') return 'https://example.com/users/blocker';
        if (field === 'object') return 'https://example.com/users/blocked';
        return url as string;
      });

      vi.mocked(discoverActor).mockResolvedValue(mockBlocker);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(mockBlocked);
      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null);

      // Mock successful block creation
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'block-id' }])
        })
      } as any);

      // Mock follow deletion
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([
            { id: 'follow-1', followerUri: 'https://example.com/users/blocker', followingUri: 'https://example.com/users/blocked' }
          ])
        })
      } as any);

      // Act
      const result = await processBlock(activity);

      // Assert
      expect(result).toBe(true);
      expect(db.delete).toHaveBeenCalled(); // Follow relationships should be deleted
    });
  });

  describe('Block Status Checking', () => {
    it('should correctly identify when a user is blocked', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';
      const mockBlockActivity = {
        id: 'block-activity-id',
        type: 'Block',
        actorUri: blockerUri,
        objectUri: blockedUri,
        status: 'completed'
      };

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(mockBlockActivity);

      // Act
      const isBlockedResult = await isBlocked(blockerUri, blockedUri);

      // Assert
      expect(isBlockedResult).toBe(true);
    });

    it('should return false when no block exists', async () => {
      // Arrange
      const blockerUri = 'https://example.com/users/blocker';
      const blockedUri = 'https://example.com/users/blocked';

      vi.mocked(db.query.activity.findFirst).mockResolvedValue(null);

      // Act
      const isBlockedResult = await isBlocked(blockerUri, blockedUri);

      // Assert
      expect(isBlockedResult).toBe(false);
    });

    it('should detect mutual blocks correctly', async () => {
      // Arrange
      const user1Uri = 'https://example.com/users/user1';
      const user2Uri = 'https://example.com/users/user2';

      // Mock that user1 has blocked user2
      vi.mocked(db.query.activity.findFirst)
        .mockResolvedValueOnce({ id: 'block-1', type: 'Block', actorUri: user1Uri, objectUri: user2Uri, status: 'completed' })
        .mockResolvedValueOnce(null); // user2 hasn't blocked user1

      // Act
      const isMutualBlock = await isMutuallyBlocked(user1Uri, user2Uri);

      // Assert
      expect(isMutualBlock).toBe(true);
    });
  });
});
