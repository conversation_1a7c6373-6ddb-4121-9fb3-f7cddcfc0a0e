/**
 * ActivityPub outbox implementation for sending activities to remote servers.
 * Handles activity creation, recipient discovery, and delivery scheduling.
 */

import { db } from '$lib/server/db';
import { activity as activityTable, user as userTable } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { 
  scheduleDelivery, 
  scheduleMultipleDeliveries, 
  getActivityPriority,
  type DeliveryOptions 
} from './utils/delivery';
import { discoverActor } from './actors';
import { ActivityPubLogs } from './utils/logger';
import { ActivityPubMetrics } from './utils/metrics';
import type { Activity, Actor } from './types';

/**
 * Outbox activity creation options
 */
export interface OutboxActivityOptions {
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  delayMs?: number;
  maxRetries?: number;
  recipients?: string[]; // Override automatic recipient discovery
}

/**
 * Outbox activity result
 */
export interface OutboxActivityResult {
  activityId: string;
  deliveryIds: string[];
  recipientCount: number;
  errors: string[];
}

/**
 * Create and send an activity through the outbox
 */
export async function createAndSendActivity(
  userId: string,
  activity: Activity,
  options: OutboxActivityOptions = {}
): Promise<OutboxActivityResult> {
  const startTime = Date.now();
  
  try {
    // Get user information
    const user = await db
      .select()
      .from(userTable)
      .where(eq(userTable.id, userId))
      .limit(1);

    if (!user[0]) {
      throw new Error(`User not found: ${userId}`);
    }

    const userRecord = user[0];

    // Ensure activity has proper actor
    if (!activity.actor) {
      activity.actor = userRecord.uri;
    }

    // Store activity in database
    const activityRecord = {
      id: crypto.randomUUID(),
      type: activity.type,
      actorUri: activity.actor,
      objectUri: typeof activity.object === 'string' ? activity.object : activity.object?.id || null,
      targetUri: typeof activity.target === 'string' ? activity.target : activity.target?.id || null,
      activityPubObject: activity,
      status: 'pending' as const,
      direction: 'outbound' as const,
      createdAt: new Date(),
      processedAt: null
    };

    const insertResult = await db
      .insert(activityTable)
      .values(activityRecord)
      .returning();

    if (!insertResult[0]) {
      throw new Error('Failed to store activity');
    }

    const storedActivity = insertResult[0];

    ActivityPubLogs.outbox.activityCreated(
      storedActivity.id,
      activity.type,
      activity.actor
    );

    // Discover recipients
    const recipients = options.recipients 
      ? await discoverExplicitRecipients(options.recipients)
      : await discoverActivityRecipients(activity);

    if (recipients.length === 0) {
      ActivityPubLogs.outbox.noRecipientsFound(storedActivity.id, activity.type);
      
      // Mark activity as completed since there are no recipients
      await db
        .update(activityTable)
        .set({ 
          status: 'completed',
          processedAt: new Date()
        })
        .where(eq(activityTable.id, storedActivity.id));

      return {
        activityId: storedActivity.id,
        deliveryIds: [],
        recipientCount: 0,
        errors: []
      };
    }

    // Convert priority string to enum
    const priority = getActivityPriority(activity.type);

    // Schedule deliveries
    const deliveryOptions: DeliveryOptions = {
      priority,
      delayMs: options.delayMs,
      maxRetries: options.maxRetries
    };

    const deliveryIds = await scheduleMultipleDeliveries(
      storedActivity.id,
      recipients,
      deliveryOptions
    );

    // Update activity status
    await db
      .update(activityTable)
      .set({ 
        status: 'processing',
        processedAt: new Date()
      })
      .where(eq(activityTable.id, storedActivity.id));

    const duration = Date.now() - startTime;
    
    ActivityPubLogs.outbox.activityScheduled(
      storedActivity.id,
      recipients.length,
      deliveryIds.length,
      duration
    );

    ActivityPubMetrics.activities.outboxProcessed(activity.type);

    return {
      activityId: storedActivity.id,
      deliveryIds,
      recipientCount: recipients.length,
      errors: []
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const duration = Date.now() - startTime;
    
    ActivityPubLogs.outbox.activityFailed(
      activity.type,
      activity.actor || 'unknown',
      errorMessage,
      duration
    );

    ActivityPubMetrics.activities.outboxFailed(activity.type);

    throw error;
  }
}

/**
 * Discover recipients for an activity
 */
async function discoverActivityRecipients(
  activity: Activity
): Promise<Array<{ inbox: string; actor: string }>> {
  const recipients: Array<{ inbox: string; actor: string }> = [];
  const seenInboxes = new Set<string>();

  // Extract potential recipient URIs from activity
  const recipientUris = extractRecipientUris(activity);

  for (const uri of recipientUris) {
    try {
      const actor = await discoverActor(uri);
      
      if (actor && actor.inbox) {
        const inboxUrl = typeof actor.inbox === 'string' 
          ? actor.inbox 
          : actor.inbox.id;

        if (inboxUrl && !seenInboxes.has(inboxUrl)) {
          recipients.push({
            inbox: inboxUrl,
            actor: actor.uri
          });
          seenInboxes.add(inboxUrl);
        }
      }
    } catch (error) {
      ActivityPubLogs.outbox.recipientDiscoveryFailed(
        uri,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  return recipients;
}

/**
 * Discover explicit recipients by URI
 */
async function discoverExplicitRecipients(
  recipientUris: string[]
): Promise<Array<{ inbox: string; actor: string }>> {
  const recipients: Array<{ inbox: string; actor: string }> = [];

  for (const uri of recipientUris) {
    try {
      const actor = await discoverActor(uri);
      
      if (actor && actor.inbox) {
        const inboxUrl = typeof actor.inbox === 'string' 
          ? actor.inbox 
          : actor.inbox.id;

        if (inboxUrl) {
          recipients.push({
            inbox: inboxUrl,
            actor: actor.uri
          });
        }
      }
    } catch (error) {
      ActivityPubLogs.outbox.recipientDiscoveryFailed(
        uri,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  return recipients;
}

/**
 * Extract recipient URIs from activity
 */
function extractRecipientUris(activity: Activity): string[] {
  const uris: string[] = [];

  // Add recipients from 'to' field
  if (activity.to) {
    const toArray = Array.isArray(activity.to) ? activity.to : [activity.to];
    for (const to of toArray) {
      const uri = typeof to === 'string' ? to : to.id;
      if (uri && !uri.includes('#Public')) {
        uris.push(uri);
      }
    }
  }

  // Add recipients from 'cc' field
  if (activity.cc) {
    const ccArray = Array.isArray(activity.cc) ? activity.cc : [activity.cc];
    for (const cc of ccArray) {
      const uri = typeof cc === 'string' ? cc : cc.id;
      if (uri && !uri.includes('#Public')) {
        uris.push(uri);
      }
    }
  }

  // For Follow activities, add the object as recipient
  if (activity.type === 'Follow' && activity.object) {
    const objectUri = typeof activity.object === 'string' 
      ? activity.object 
      : activity.object.id;
    if (objectUri) {
      uris.push(objectUri);
    }
  }

  // For activities targeting specific actors, add the object as recipient
  if (['Accept', 'Reject', 'Block', 'Undo'].includes(activity.type) && activity.object) {
    const objectUri = typeof activity.object === 'string' 
      ? activity.object 
      : activity.object.id;
    if (objectUri) {
      uris.push(objectUri);
    }
  }

  // Remove duplicates
  return [...new Set(uris)];
}

/**
 * Send a simple activity (convenience function)
 */
export async function sendActivity(
  userId: string,
  activityType: string,
  object: string | object,
  options: OutboxActivityOptions = {}
): Promise<OutboxActivityResult> {
  // Get user to set actor
  const user = await db
    .select()
    .from(userTable)
    .where(eq(userTable.id, userId))
    .limit(1);

  if (!user[0]) {
    throw new Error(`User not found: ${userId}`);
  }

  const activity: Activity = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: activityType,
    id: `${user[0].uri}/activities/${crypto.randomUUID()}`,
    actor: user[0].uri,
    object,
    published: new Date().toISOString()
  };

  return createAndSendActivity(userId, activity, options);
}
