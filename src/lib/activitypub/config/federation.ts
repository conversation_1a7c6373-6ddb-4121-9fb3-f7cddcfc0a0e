/**
 * ActivityPub federation configuration.
 * This file contains actual configuration values, not types.
 */

import { env } from '$env/dynamic/private';
import type { FederationConfig } from '../types/config';

// Default configuration values
export const DEFAULT_FEDERATION_CONFIG: FederationConfig = {
  // Server identity - will be overridden by environment variables
  domain: env.DOMAIN || 'localhost',
  baseUrl: env.BASE_URL || 'http://localhost:5173',
  
  // Rate limiting
  rateLimits: {
    inbox: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: parseInt(env.INBOX_RATE_LIMIT || '100'),
    },
    outbox: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: parseInt(env.OUTBOX_RATE_LIMIT || '50'),
    },
    discovery: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: parseInt(env.DISCOVERY_RATE_LIMIT || '20'),
    },
  },
  
  // HTTP Signature settings
  httpSignature: {
    algorithm: env.HTTP_SIGNATURE_ALGORITHM || 'rsa-sha256',
    headers: (env.HTTP_SIGNATURE_HEADERS || '(request-target),host,date,digest').split(','),
    clockSkewTolerance: parseInt(env.HTTP_SIGNATURE_CLOCK_SKEW || '300'), // 5 minutes
  },
  
  // Delivery settings
  delivery: {
    maxRetries: parseInt(env.DELIVERY_MAX_RETRIES || '5'),
    retryDelays: [1000, 5000, 15000, 60000, 300000], // 1s, 5s, 15s, 1m, 5m
    timeout: parseInt(env.DELIVERY_TIMEOUT || '30000'), // 30 seconds
    concurrency: parseInt(env.DELIVERY_CONCURRENCY || '10'),
  },
  
  // Processing settings
  processing: {
    maxConcurrentActivities: parseInt(env.PROCESSING_MAX_CONCURRENT || '20'),
    activityTimeout: parseInt(env.PROCESSING_TIMEOUT || '30000'), // 30 seconds
    deduplicationWindow: parseInt(env.PROCESSING_DEDUP_WINDOW || '300000'), // 5 minutes
  },
  
  // Security settings
  security: {
    requireHttpSignature: env.REQUIRE_HTTP_SIGNATURE !== 'false',
    allowedActorTypes: (env.ALLOWED_ACTOR_TYPES || 'Person,Service,Application').split(','),
    blockedDomains: env.BLOCKED_DOMAINS ? env.BLOCKED_DOMAINS.split(',') : [],
    allowedDomains: env.ALLOWED_DOMAINS ? env.ALLOWED_DOMAINS.split(',') : undefined,
    maxActivitySize: parseInt(env.MAX_ACTIVITY_SIZE || '1048576'), // 1MB
    maxAttachmentSize: parseInt(env.MAX_ATTACHMENT_SIZE || '10485760'), // 10MB
  },
  
  // Feature flags
  features: {
    enableInbox: env.ENABLE_INBOX !== 'false',
    enableOutbox: env.ENABLE_OUTBOX !== 'false',
    enableWebfinger: env.ENABLE_WEBFINGER !== 'false',
    enableNodeinfo: env.ENABLE_NODEINFO !== 'false',
    enableSharedInbox: env.ENABLE_SHARED_INBOX !== 'false',
  },
};

/**
 * Get the current federation configuration.
 * This function merges default values with environment variables.
 */
export function getFederationConfig(): FederationConfig {
  return DEFAULT_FEDERATION_CONFIG;
}

/**
 * Validate federation configuration.
 */
export function validateFederationConfig(config: FederationConfig): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate required fields
  if (!config.domain) {
    errors.push('Domain is required');
  }
  
  if (!config.baseUrl) {
    errors.push('Base URL is required');
  }
  
  // Validate URL format
  if (config.baseUrl) {
    try {
      new URL(config.baseUrl);
    } catch {
      errors.push('Base URL must be a valid URL');
    }
  }
  
  // Validate rate limits
  if (config.rateLimits.inbox.maxRequests <= 0) {
    errors.push('Inbox rate limit must be positive');
  }
  
  // Validate security settings
  if (config.security.maxActivitySize <= 0) {
    errors.push('Max activity size must be positive');
  }
  
  // Warnings
  if (!config.security.requireHttpSignature) {
    warnings.push('HTTP signature verification is disabled - this reduces security');
  }
  
  if (config.security.allowedDomains && config.security.allowedDomains.length === 0) {
    warnings.push('Allowed domains list is empty - no federation will be possible');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}
