import type { CoreObject } from '$lib/activitypub/types';
import { db } from '$lib/server/db';
import { emoji } from '$lib/server/db/schema';
import { getUrlFromAPObjectSafe } from '$lib/activitypub/utils/links';
import { and, eq } from 'drizzle-orm';
import { escapeHtml, isSecureUrl } from '$lib/activitypub/utils/security';
import { getImageSize } from './utils/content';

export async function importEmojis(object: CoreObject) {
  const domain = object.id?.host;

  if (!domain) {
    return;
  }

  if (object.tag) {
    if (!Array.isArray(object.tag)) {
      object.tag = [object.tag];
    }
    await Promise.all(object.tag.filter(tag => 'type' in tag)
      .map(async tag => {
        // Мощнейше тайпгардим!
        if (tag.type !== 'Emoji'
          || !('icon' in tag)
          || !('name' in tag)
          || !tag.name
          || !tag.icon
          || Array.isArray(tag.icon)
          || !('url' in tag.icon)
          || !tag.icon.url
        ) {
          return false;
        }

        const url = getUrlFromAPObjectSafe(tag.icon);

        if (!url) {
          return false;
        }

        const code = tag.name.replace(/^:/g, '').replace(/:$/g, '');
        const imageSizes = await getImageSize(url);
        let size = imageSizes.width;
        if (imageSizes.height > imageSizes.width) {
          size = imageSizes.height;
        }

        const existingEmoji = await db.query.emoji.findFirst({
          where: and(
            eq(emoji.code, code),
            eq(emoji.domain, domain)
          )
        });

        if (existingEmoji) {
          if (existingEmoji.url !== url.toString()) {
            await db.update(emoji).set({ url, size }).where(eq(emoji.id, existingEmoji.id));
          }
          return false;
        }

        return db.insert(emoji).values({
          code,
          url,
          domain,
          size
        })
      })
      .filter(Boolean)
    );
  }
}

/**
 * Creates safe HTML for emoji with validation and escaping.
 *
 * @param url - The emoji image URL
 * @param code - The emoji code (e.g., "smile")
 * @returns Safe HTML string or fallback text
 */
function createSafeEmojiHtml(url: string, code: string, size: number = 0): string {
  if (size === 0 || size > 100) {
    size = 100;
  }


  // Validate URL for security
  if (!isSecureUrl(url)) {
    return `:${code}:`; // Return original code if URL is unsafe
  }

  const safeUrl = escapeHtml(url);
  const safeCode = escapeHtml(code);

  return `<span class="emoji" style="--emoji-width: ${size}px;"><img src="${safeUrl}" alt="${safeCode}" loading="lazy" /></span>`;
}

export async function replaceEmojis(string: string, domain: string) {
  const emojis = await db.select().from(emoji).where(eq(emoji.domain, domain));
  const emojiMap: Map<string, [string, number]> = new Map(emojis.map(emoji => [emoji.code, [emoji.url, emoji.size ?? 0]]));
  return string.replace(/:([a-zA-Z0-9_\-+]+):/g, (match, code) => {
    const info = emojiMap.get(code);
    if (info) {
      return createSafeEmojiHtml(info[0], code, info[1]);
    }
    return match;
  });
}
