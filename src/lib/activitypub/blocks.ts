import type { Block } from '$lib/activitypub/types';
import { activity as activityTable, user as userTable, follow as followTable } from '$lib/server/db/schema';
import { db } from '$lib/server/db';
import { eq, and, or } from 'drizzle-orm';
import { getUrlFromAPObjectRequired } from '$lib/activitypub/utils/links';
import { processActivityBase } from '$lib/activitypub/utils/activities';

/**
 * Process a Block activity from ActivityPub
 *
 * This function handles incoming Block activities and implements comprehensive blocking logic:
 * 1. Creates a block record in the database
 * 2. Removes existing follow relationships between the users
 * 3. Sets up infrastructure for hiding posts and preventing future interactions
 *
 * @param activity - The Block activity to process
 * @returns Promise<boolean> - true if the block was processed successfully, false otherwise
 */
export async function processBlock(activity: Block): Promise<boolean> {
  return processActivityBase(activity, {
    requiredFields: ['actor', 'object'],
    extractUrls: (activity) => ({
      actorUri: getUrlFromAPObjectRequired(activity.actor, 'actor'),
      objectUri: getUrlFromAPObjectRequired(activity.object, 'object')
    }),
    processActivity: async (activity, actors) => {
      const blockerUri = actors.actorUri;
      const blockedUri = actors.objectUri;
      const blocker = actors.actor;

      // Check if the blocked user exists (could be local or remote)
      let blockedUser = await db.query.user.findFirst({
        where: eq(userTable.uri, blockedUri)
      });

      // If blocked user is not known locally, try to discover them
      if (!blockedUser) {
        try {
          blockedUser = await discoverActor(blockedUri);
        } catch (error) {
          console.warn(`Failed to discover blocked actor: ${blockedUri}`, error);
          // Continue anyway - we can still record the block
        }
      }

      // Check if block activity already exists
      const existingActivity = await db.query.activity.findFirst({
        where: and(
          eq(activityTable.type, 'Block'),
          eq(activityTable.actorUri, blockerUri),
          eq(activityTable.objectUri, blockedUri)
        )
      });

      if (existingActivity) {
        console.log(`Block already exists: ${blockerUri} -> ${blockedUri}`);
        return true;
      }

      // Create activity record for the block
      const activityRecord: typeof activityTable.$inferInsert = {
        uri: activity.id?.toString() || `${blockerUri}/blocks/${Date.now()}`,
        type: 'Block',
        activityPubObject: activity,
        actorId: blocker.id,
        actorUri: blockerUri,
        objectUri: blockedUri,
        direction: 'inbound',
        status: 'completed',
        published: activity.published ? new Date(activity.published) : new Date(),
        localUserId: blockedUser?.id || null
      };

      const result = await db.insert(activityTable).values(activityRecord).returning();

      if (result.length > 0) {
        console.log(`Created block: ${blockerUri} -> ${blockedUri}`);

        // Implement additional block logic:
        await implementBlockLogic(blockerUri, blockedUri, blocker, blockedUser);

        return true;
      } else {
        console.warn(`Failed to create block record: ${blockerUri} -> ${blockedUri}`);
        return false;
      }
    },
    activityName: 'Block'
  });
}

/**
 * Implement additional block logic after creating the block record
 */
async function implementBlockLogic(
  blockerUri: string,
  blockedUri: string,
  blocker: any,
  blockedUser: any
): Promise<void> {
  try {
    // 1. Remove existing follow relationships between the two users
    await removeFollowRelationships(blockerUri, blockedUri);

    // 2. Hide posts from blocked user (implemented via query filtering)
    // Note: Post hiding is implemented at the query level in post retrieval functions
    // No direct database changes needed here as we'll filter based on block relationships

    console.log(`Completed block logic for: ${blockerUri} -> ${blockedUri}`);
  } catch (error) {
    console.error('Error implementing block logic:', error);
    // Don't throw here to avoid breaking the main block creation
    // The block record is already created, this is just cleanup
  }
}

/**
 * Remove all follow relationships between blocker and blocked user
 */
async function removeFollowRelationships(blockerUri: string, blockedUri: string): Promise<void> {
  try {
    // Remove follows in both directions:
    // 1. Blocker following blocked user
    // 2. Blocked user following blocker
    const deleteResult = await db.delete(followTable)
      .where(
        or(
          // Blocker follows blocked user
          and(
            eq(followTable.followerUri, blockerUri),
            eq(followTable.followingUri, blockedUri)
          ),
          // Blocked user follows blocker
          and(
            eq(followTable.followerUri, blockedUri),
            eq(followTable.followingUri, blockerUri)
          )
        )
      )
      .returning();

    if (deleteResult.length > 0) {
      console.log(`Removed ${deleteResult.length} follow relationship(s) between ${blockerUri} and ${blockedUri}`);
    } else {
      console.log(`No follow relationships found between ${blockerUri} and ${blockedUri}`);
    }
  } catch (error) {
    console.error('Error removing follow relationships:', error);
    throw error;
  }
}

/**
 * Check if there's a mutual block between two users (either direction)
 */
export async function isMutuallyBlocked(userUri1: string, userUri2: string): Promise<boolean> {
  try {
    const [block1, block2] = await Promise.all([
      isBlocked(userUri1, userUri2),
      isBlocked(userUri2, userUri1)
    ]);

    return block1 || block2;
  } catch (error) {
    console.error('Error checking mutual block status:', error);
    return false; // Default to not blocked on error
  }
}

/**
 * Check if a user is blocked by another user
 */
export async function isBlocked(blockerUri: string, blockedUri: string): Promise<boolean> {
  try {
    const blockActivity = await db.query.activity.findFirst({
      where: and(
        eq(activityTable.type, 'Block'),
        eq(activityTable.actorUri, blockerUri),
        eq(activityTable.objectUri, blockedUri),
        eq(activityTable.status, 'completed')
      )
    });

    return !!blockActivity;
  } catch (error) {
    console.error('Error checking block status:', error);
    return false;
  }
}

/**
 * Get all users blocked by a specific user
 */
export async function getBlockedUsers(blockerUri: string): Promise<string[]> {
  try {
    const blockActivities = await db.query.activity.findMany({
      where: and(
        eq(activityTable.type, 'Block'),
        eq(activityTable.actorUri, blockerUri),
        eq(activityTable.status, 'completed')
      )
    });

    return blockActivities
      .map(activity => activity.objectUri)
      .filter((uri): uri is string => uri !== null);
  } catch (error) {
    console.error('Error getting blocked users:', error);
    return [];
  }
}

/**
 * Get all users who have blocked a specific user
 */
export async function getBlockingUsers(blockedUri: string): Promise<string[]> {
  try {
    const blockActivities = await db.query.activity.findMany({
      where: and(
        eq(activityTable.type, 'Block'),
        eq(activityTable.objectUri, blockedUri),
        eq(activityTable.status, 'completed')
      )
    });

    return blockActivities
      .map(activity => activity.actorUri)
      .filter((uri): uri is string => uri !== null);
  } catch (error) {
    console.error('Error getting blocking users:', error);
    return [];
  }
}

/**
 * Remove a block (for Undo Block activities)
 */
export async function removeBlock(blockerUri: string, blockedUri: string): Promise<boolean> {
  try {
    const result = await db.update(activityTable)
      .set({ status: 'undone' })
      .where(and(
        eq(activityTable.type, 'Block'),
        eq(activityTable.actorUri, blockerUri),
        eq(activityTable.objectUri, blockedUri),
        eq(activityTable.status, 'completed')
      ))
      .returning();

    if (result.length > 0) {
      console.log(`Removed block: ${blockerUri} -> ${blockedUri}`);
      return true;
    } else {
      console.warn(`Block not found for removal: ${blockerUri} -> ${blockedUri}`);
      return false;
    }
  } catch (error) {
    console.error('Error removing block:', error);
    throw error;
  }
}
