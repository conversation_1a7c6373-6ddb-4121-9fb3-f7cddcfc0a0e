import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { processFollow, getFollow, updateFollowStatus, deleteFollow, processAccept } from './follows';
import { db } from '$lib/server/db';
import { follow as followTable, user as userTable } from '$lib/server/db/schema';
import { discoverActor } from '$lib/activitypub/actors';
import { eq, and } from 'drizzle-orm';
import {
  ActivityPubTestBase,
  activityPubMocks,
  mockConfigurations,
  testDataFactories,
  mockDb,
  commonMockImplementations
} from '../../test/activitypub-test-utils';

// Mock the database
vi.mock('$lib/server/db', () => ({
  db: {
    query: {
      user: {
        findFirst: vi.fn()
      },
      follow: {
        findFirst: vi.fn()
      }
    },
    insert: vi.fn().mockReturnValue({
      values: vi.fn().mockReturnValue({
        returning: vi.fn()
      })
    }),
    update: vi.fn().mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn()
        })
      })
    }),
    delete: vi.fn().mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn()
      })
    })
  }
}));

// Mock the actors module
vi.mock('./actors', () => ({
  discoverActor: vi.fn()
}));

// Mock the schema
vi.mock('$lib/server/db/schema', () => ({
  follow: {
    id: 'id',
    followerUri: 'followerUri',
    followingUri: 'followingUri'
  },
  user: {
    uri: 'uri'
  }
}));

describe('Follow Processing', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('processFollow', () => {
    const mockActivity = {
      type: 'Follow',
      id: 'https://example.com/activities/follow-1',
      actor: 'https://remote.example.com/users/alice',
      object: 'https://local.example.com/users/bob',
      published: '2024-01-01T00:00:00Z'
    };

    const mockFollower = {
      id: 'follower-id-123',
      uri: 'https://remote.example.com/users/alice',
      username: 'alice'
    };

    const mockFollowingUser = {
      id: 'following-id-456',
      uri: 'https://local.example.com/users/bob',
      username: 'bob',
      manuallyApprovedFollowers: false
    };

    it('should successfully create a new follow record', async () => {
      // Arrange
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(mockFollowingUser);
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(null);
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'new-follow-id' }])
        })
      } as any);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(true);
      expect(discoverActor).toHaveBeenCalledWith('https://remote.example.com/users/alice');
      expect(db.query.user.findFirst).toHaveBeenCalledWith({
        where: eq(userTable.uri, 'https://local.example.com/users/bob')
      });
      expect(db.insert).toHaveBeenCalledWith(followTable);
    });

    it('should auto-accept follow when user does not require manual approval', async () => {
      // Arrange
      const autoAcceptUser = { ...mockFollowingUser, manuallyApprovedFollowers: false };
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(autoAcceptUser);
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(null);
      
      let capturedFollowRecord: any;
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockImplementation((record) => {
          capturedFollowRecord = record;
          return {
            returning: vi.fn().mockResolvedValue([{ id: 'new-follow-id' }])
          };
        })
      } as any);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(true);
      expect(capturedFollowRecord.status).toBe('accepted');
      expect(capturedFollowRecord.acceptedAt).toBeInstanceOf(Date);
    });

    it('should require approval when user has manual approval enabled', async () => {
      // Arrange
      const manualApprovalUser = { ...mockFollowingUser, manuallyApprovedFollowers: true };
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(manualApprovalUser);
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(null);
      
      let capturedFollowRecord: any;
      vi.mocked(db.insert).mockReturnValue({
        values: vi.fn().mockImplementation((record) => {
          capturedFollowRecord = record;
          return {
            returning: vi.fn().mockResolvedValue([{ id: 'new-follow-id' }])
          };
        })
      } as any);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(true);
      expect(capturedFollowRecord.status).toBe('pending');
      expect(capturedFollowRecord.acceptedAt).toBeNull();
    });

    it('should return false when follower actor cannot be discovered', async () => {
      // Arrange
      vi.mocked(discoverActor).mockResolvedValue(null);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(false);
      expect(discoverActor).toHaveBeenCalledWith('https://remote.example.com/users/alice');
    });

    it('should return false when target user is not found locally', async () => {
      // Arrange
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(null);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(false);
      expect(db.query.user.findFirst).toHaveBeenCalledWith({
        where: eq(userTable.uri, 'https://local.example.com/users/bob')
      });
    });

    it('should update existing rejected follow to pending', async () => {
      // Arrange
      const existingFollow = {
        id: 'existing-follow-id',
        status: 'rejected',
        followActivityUri: null
      };
      
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(mockFollowingUser);
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(existingFollow);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([existingFollow])
          })
        })
      } as any);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(true);
      expect(db.update).toHaveBeenCalledWith(followTable);
    });

    it('should return true for existing active follow', async () => {
      // Arrange
      const existingFollow = {
        id: 'existing-follow-id',
        status: 'accepted',
        followActivityUri: 'https://example.com/activities/old-follow'
      };
      
      vi.mocked(discoverActor).mockResolvedValue(mockFollower);
      vi.mocked(db.query.user.findFirst).mockResolvedValue(mockFollowingUser);
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(existingFollow);

      // Act
      const result = await processFollow(mockActivity);

      // Assert
      expect(result).toBe(true);
      expect(db.insert).not.toHaveBeenCalled();
      expect(db.update).not.toHaveBeenCalled();
    });

    it('should handle missing actor or object', async () => {
      // Arrange
      const invalidActivity = { ...mockActivity, actor: null };

      // Act
      const result = await processFollow(invalidActivity);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('updateFollowStatus', () => {
    it('should successfully update follow status to accepted', async () => {
      // Arrange
      const followId = 'follow-id-123';
      const acceptActivityUri = 'https://example.com/activities/accept-1';
      
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{ id: followId }])
          })
        })
      } as any);

      // Act
      const result = await updateFollowStatus(followId, 'accepted', acceptActivityUri);

      // Assert
      expect(result).toBe(true);
      expect(db.update).toHaveBeenCalledWith(followTable);
    });

    it('should successfully update follow status to rejected', async () => {
      // Arrange
      const followId = 'follow-id-123';
      
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{ id: followId }])
          })
        })
      } as any);

      // Act
      const result = await updateFollowStatus(followId, 'rejected');

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('deleteFollow', () => {
    it('should successfully delete a follow record', async () => {
      // Arrange
      const followerUri = 'https://remote.example.com/users/alice';
      const followingUri = 'https://local.example.com/users/bob';
      
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{ id: 'deleted-follow-id' }])
        })
      } as any);

      // Act
      const result = await deleteFollow(followerUri, followingUri);

      // Assert
      expect(result).toBe(true);
      expect(db.delete).toHaveBeenCalledWith(followTable);
    });

    it('should return false when follow record not found', async () => {
      // Arrange
      const followerUri = 'https://remote.example.com/users/alice';
      const followingUri = 'https://local.example.com/users/bob';
      
      vi.mocked(db.delete).mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([])
        })
      } as any);

      // Act
      const result = await deleteFollow(followerUri, followingUri);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('processAccept', () => {
    const mockAcceptActivity = {
      type: 'Accept',
      id: 'https://local.example.com/activities/accept-1',
      actor: 'https://local.example.com/users/bob',
      object: 'https://remote.example.com/activities/follow-1',
      published: '2024-01-01T00:00:00Z'
    };

    const mockFollowRecord = {
      id: 'follow-id-123',
      followerUri: 'https://remote.example.com/users/alice',
      followingUri: 'https://local.example.com/users/bob',
      status: 'pending',
      followActivityUri: 'https://remote.example.com/activities/follow-1'
    };

    it('should successfully accept a follow request', async () => {
      // Arrange
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(mockFollowRecord);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([mockFollowRecord])
          })
        })
      } as any);

      // Act
      const result = await processAccept(mockAcceptActivity);

      // Assert
      expect(result).toBe(true);
      expect(db.query.follow.findFirst).toHaveBeenCalledWith({
        where: eq(followTable.followActivityUri, 'https://remote.example.com/activities/follow-1')
      });
      expect(db.update).toHaveBeenCalledWith(followTable);
    });

    it('should return false when follow record not found', async () => {
      // Arrange
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(null);

      // Act
      const result = await processAccept(mockAcceptActivity);

      // Assert
      expect(result).toBe(false);
      expect(db.query.follow.findFirst).toHaveBeenCalledWith({
        where: eq(followTable.followActivityUri, 'https://remote.example.com/activities/follow-1')
      });
      expect(db.update).not.toHaveBeenCalled();
    });

    it('should return false when acceptor does not match following user', async () => {
      // Arrange
      const mismatchedFollowRecord = {
        ...mockFollowRecord,
        followingUri: 'https://local.example.com/users/charlie' // Different user
      };
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(mismatchedFollowRecord);

      // Act
      const result = await processAccept(mockAcceptActivity);

      // Assert
      expect(result).toBe(false);
      expect(db.update).not.toHaveBeenCalled();
    });

    it('should return false when update fails', async () => {
      // Arrange
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(mockFollowRecord);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([]) // Empty array indicates failure
          })
        })
      } as any);

      // Act
      const result = await processAccept(mockAcceptActivity);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle missing actor or object', async () => {
      // Arrange
      const invalidActivity = { ...mockAcceptActivity, actor: null };

      // Act
      const result = await processAccept(invalidActivity);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle console logging correctly', async () => {
      // Arrange
      vi.mocked(db.query.follow.findFirst).mockResolvedValue(mockFollowRecord);
      vi.mocked(db.update).mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([mockFollowRecord])
          })
        })
      } as any);

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      await processAccept(mockAcceptActivity);

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        `Accepted follow: ${mockFollowRecord.followerUri} -> ${mockFollowRecord.followingUri}`
      );

      consoleSpy.mockRestore();
    });
  });
});
