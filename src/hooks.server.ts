import { type <PERSON>le, json, type RequestEvent } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { user } from '$lib/server/db/schema';
import { and, eq, isNull } from 'drizzle-orm';
import { getFederationConfig } from '$lib/activitypub/config/federation';

export const handle: Handle = async ({ event, resolve }) => {
  const userProfilePageRegEx = /^\/users\/([A-Za-z_-]+(@[A-Za-z_.-]+)?)$/gm
  if (userProfilePageRegEx.test(event.url.pathname)) {
    const acceptHeader = event.request.headers.get('accept') || '';
    const isActivityPubRequest =
      acceptHeader.includes('application/activity+json') ||
      acceptHeader.includes('application/ld+json');
    if (isActivityPubRequest) {
      return await respondWithUserActivityPub(event)
    }
  }

  return resolve(event);
};

async function respondWithUserActivityPub(event: RequestEvent) {
  try {
    const username = event.params.username;

    if (!username) {
      return new Response('User not found', { status: 404 });
    }

    // Get user from database
    const userResult = await db
      .select()
      .from(user)
      .where(
        and(
          eq(user.username, username),
          isNull(user.domain) // только локальные пользователи
        )
      )
      .limit(1);

    if (!userResult[0]) {
      return new Response('User not found', { status: 404 });
    }

    const account = userResult[0];
    const federationConfig = getFederationConfig();

    // Динамически генерируем URI и эндпоинты
    const userUri = `${federationConfig.baseUrl}/users/${username}`;
    const userUrl = userUri;
    const inbox = `${userUri}/inbox`;
    const outbox = `${userUri}/outbox`;

    // Определяем тип актора
    const actorType = username === 'service' ? 'Service' : 'Person';

    // Build ActivityPub Actor object
    const actor = {
      '@context': ['https://www.w3.org/ns/activitystreams', 'https://w3id.org/security/v1'],
      id: userUri,
      type: actorType,
      preferredUsername: account.username,
      name: account.showName,
      summary:
        account.summary ||
        (username === 'service' ? 'Service account for federation requests' : ''),
      inbox: inbox,
      outbox: outbox,
      url: userUrl,
      manuallyApprovesFollowers: username === 'service',
      discoverable: username !== 'service',
      indexable: username !== 'service',
      publicKey: {
        id: `${userUri}#main-key`,
        owner: userUri,
        publicKeyPem: account.publicKey
      }
    };

    // Добавляем дополнительные поля для обычных пользователей
    if (username !== 'service') {
      if (account.avatar) {
        actor.icon = {
          type: 'Image',
          mediaType: 'image/jpeg', // можно определять динамически
          url: account.avatar
        };
      }

      if (account.banner) {
        actor.image = {
          type: 'Image',
          mediaType: 'image/jpeg', // можно определять динамически
          url: account.banner
        };
      }
    }

    return json(actor, {
      headers: {
        'Content-Type': 'application/activity+json; charset=utf-8',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  } catch (error) {
    console.error('Error serving ActivityPub actor:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
