<script lang="ts">
  import Block from '$lib/components/design_system/Block.svelte';
  import FollowButton from '$lib/components/design_system/FollowButton.svelte';
  import UserAvatar from '$lib/components/design_system/UserAvatar.svelte';
  import UserHandle from '$lib/components/design_system/UserHandle.svelte';
  import UserName from '$lib/components/design_system/UserName.svelte';
  import Dropdown, { type DropdownAction } from '$lib/components/design_system/Dropdown.svelte';
  import { toastStore } from '$lib/components/design_system/Toasts/store';
  import { SquareArrowOutUpRight, ListPlus, EyeOff, Gavel, PenLine, Copy } from 'lucide-svelte';
  import Button from '$lib/components/design_system/Button.svelte';
  import { processContentForClient } from '$lib/utils/content-processing';
  import { currentUser } from '$lib/stores/auth';
  import type { PageProps } from './$types';
  import { PUBLIC_DOMAIN } from '$env/static/public';

  let {
    data
  }: PageProps = $props();

  let bannerBroken = $state(false);
  let user = $derived(data.user);
  const isAuthenticated = $derived($currentUser !== null);
  let actions = $derived.by(() => {
    const actions: Array<DropdownAction> = []

    if (!isAuthenticated) {
      return actions
    }

    if (user.domain) {
      actions.push({
        label: 'Открыть оригинал',
        icon: SquareArrowOutUpRight,
        action: () => {
          window.open(user.id, '_blank');
        }
      })
      actions.push({
        label: 'Копировать ссылку на оригинал',
        icon: Copy,
        action: copyUserName
      })
      actions.push('divider');
    }

    actions.push({
      label: 'Добавить в список',
      icon: ListPlus,
      action: () => {}
    })
    actions.push('divider');

    if (user.id !== $currentUser?.id) {
      actions.push({
        label: 'Скрыть',
        icon: EyeOff,
        action: () => {}
      })
      actions.push({
        label: 'Заблокировать',
        icon: Gavel,
        action: () => {}
      })
    } else {
      actions.push({
        label: 'Редактировать профиль',
        icon: PenLine,
        action: () => {}
      })
    }

    return actions
  })

  let summary = $derived(processContentForClient(user.summary, user.domain));

  function copyUserName() {
    const domain = user.domain ?? PUBLIC_DOMAIN
    navigator.clipboard.writeText(`@${user.username}@${domain}`);
    $toastStore.showToast('success', 'Имя пользователя скопировано');
  }

  function setBannerBroken() {
    bannerBroken = true;
  }
</script>

<Block>
  <img
    class="header-image"
    src={user.banner}
    alt="User header"
    onerror={setBannerBroken}
    class:broken={bannerBroken || !user.banner}
  />
  <div class="upper-row">
    <div class="user-info-container">
      <UserAvatar user={user} />
      <UserName showName={user.showName} />
      <div style="display: flex; align-items: start; gap: 8px;">
        <UserHandle
          username={user.username}
          domain={user.domain}
        />
        <Button variant="icon invisible" onclick={copyUserName} style='--button-size: 20px;'>
          <Copy/>
        </Button>
      </div>
    </div>
    {#if isAuthenticated}
      <div style="display: flex; gap: 8px; align-items: center;">
        {#if user.id !== $currentUser?.id}
          <FollowButton />
        {/if}
        {#if actions.length > 0}
          <Dropdown
            {actions}
          />
        {/if}
      </div>
    {/if}
  </div>
  <div class="summary">
    {@html summary}
  </div>
  <div class="additional-fields-row">
    {#each user.additionalFields as field (field.name + '_' + field.value)}
      <dl class="additional-field">
        <dt class="field-name">{@html field.name}</dt>
        <dd class="field-value">{@html field.value}</dd>
      </dl>
    {/each}
  </div>
</Block>

<style>
  .header-image {
    display: block;
    margin: -16px -16px 16px;
    width: calc(100% + 32px);
    aspect-ratio: 700/200;
    object-fit: cover;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    color: var(--layer-1-bg);
  }

  .header-image.broken {
    height: 100px;
  }

  .user-info-container {
    display: grid;
    grid-template-columns: 106px auto;
    margin-top: -45px;
    column-gap: 16px;
  }

  .user-info-container :global(.avatar) {
    grid-row: 1 / 3;
    border: 3px solid var(--layer-1-bg);
  }

  .user-info-container :global(.avatar .cat-ears::before),
  .user-info-container :global(.avatar .cat-ears::after) {
    box-shadow: 0 0 0 3px var(--layer-1-bg);
  }

  .user-info-container :global(.user-name) {
    align-self: end;
  }

  .upper-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .additional-fields-row {
    border-top: 1px solid var(--accent-color);
    padding-top: 16px;
    display: grid;
    grid-template-columns: auto auto;
    column-gap: 16px;
    row-gap: 8px;
  }

  .additional-field {
    display: grid;
    grid-column: 1/span 2;
    grid-template-columns: subgrid;
    margin: 0;
  }

  .field-name {
    justify-self: end;
    font-weight: bold;
  }

  .field-value {
    padding: 0;
    margin: 0;
  }
</style>
