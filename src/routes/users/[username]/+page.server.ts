import { discoverActor } from "$lib/activitypub/actors";
import { replaceEmojis } from "$lib/activitypub/emoji";
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from "./$types";
import { user } from '$lib/server/db/schema';
import { processContentForClient } from "$lib/utils/content-processing";

export const load: PageServerLoad = async ({ params }) => {

  let actor: typeof user.$inferSelect | null;

  try {
    actor = await discoverActor(params.username);
  } catch (e) {
    throw error(500, {
      message: e instanceof Error ? e.message : 'Unknown server error'
    })
  }

  if (!actor) {
    throw error(404, {
      message: 'User not found'
    })
  }

  const summary = await replaceEmojis(actor.summary ?? '', actor.domain ?? '');
  const showName = await replaceEmojis(actor.showName, actor.domain ?? '');

  const customFields = []

  if (actor.address) {
    customFields.push({
      name: 'Местоположение',
      value: actor.address
    })
  }

  if (actor.birthday) {
    customFields.push({
      name: 'День рождения',
      value: actor.birthday
    })
  }

  for (const field of actor.customFields ?? []) {
    const fieldName = await replaceEmojis(field.name, actor.domain ?? '');
    const fieldValue = await replaceEmojis(field.value, actor.domain ?? '');

    customFields.push({
      name: processContentForClient(fieldName),
      value: processContentForClient(fieldValue)
    })
  }

  let earColor = '#AAA';

  if (actor.isCat && actor.avatar) {
    const getAverageColor = (await import('fast-average-color-node')).getAverageColor;
    const color = await getAverageColor(actor.avatar);
    earColor = color.hex;
  }

  return {
    user: {
      id: actor.id,
      username: actor.username,
      domain: actor.domain,
      showName,
      summary,
      avatar: actor.avatar,
      banner: actor.banner,
      isCat: actor.isCat,
      earColor,
      additionalFields: customFields
    }
  };
};
