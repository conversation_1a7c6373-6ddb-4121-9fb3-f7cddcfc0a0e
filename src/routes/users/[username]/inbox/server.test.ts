import { describe, it, expect, vi, beforeEach } from 'vitest';
import { POST } from './+server.js';
import {
  createTestCreateActivity,
  createTestDeleteActivity,
  createMockInboxRequest,
  createTestSignature,
  mockValidationSuccess,
  mockValidationFailure
} from '../../../../../test/activitypub-helpers.js';

// Mock the dependencies
vi.mock('$lib/activitypub/utils/inbox-validation', () => ({
  validateInboxRequest: vi.fn(),
  createInboxProcessingContext: vi.fn()
}));

vi.mock('$lib/activitypub/activities', () => ({
  processActivityObject: vi.fn()
}));

vi.mock('$lib/activitypub/utils/logger', () => ({
  ActivityPubLogs: {
    federation: {
      incomingRequest: vi.fn(),
      incomingResponse: vi.fn(),
      incomingError: vi.fn()
    }
  }
}));

import { validateInboxRequest, createInboxProcessingContext } from '$lib/activitypub/utils/inbox-validation';
import { processActivityObject } from '$lib/activitypub/activities';
import { ActivityPubLogs } from '$lib/activitypub/utils/logger';

describe('Inbox Server Route', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /users/[username]/inbox', () => {
    it('should successfully process a valid Create activity', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity, {
        headers: { signature: createTestSignature() }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue({
        valid: true,
        activity,
        signature: mockValidationSuccess.signature
      });
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(activity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockResolvedValue(activity);

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(200);
      expect(await response.text()).toBe('OK');
      expect(validateInboxRequest).toHaveBeenCalledWith(
        'POST',
        url.toString(),
        expect.any(Object),
        JSON.stringify(activity),
        undefined
      );
      expect(processActivityObject).toHaveBeenCalledWith(activity);
      expect(ActivityPubLogs.federation.incomingRequest).toHaveBeenCalledWith('POST', url.toString());
      expect(ActivityPubLogs.federation.incomingResponse).toHaveBeenCalledWith('POST', url.toString(), 200);
    });

    it('should reject invalid requests with 400 status', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity);
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue(mockValidationFailure);

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(401);
      expect(await response.text()).toBe('Invalid signature');
      expect(ActivityPubLogs.federation.incomingError).toHaveBeenCalledWith(
        'POST',
        url.toString(),
        'Invalid signature'
      );
      expect(processActivityObject).not.toHaveBeenCalled();
    });

    it('should handle processing errors with 400 status for non-retryable errors', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity, {
        headers: { signature: createTestSignature() }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue(mockValidationSuccess);
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(activity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockRejectedValue(new Error('Processing failed'));

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(400); // Non-retryable error
      expect(await response.text()).toBe('Processing failed');
      expect(ActivityPubLogs.federation.incomingError).toHaveBeenCalledWith(
        'POST',
        url.toString(),
        'Processing failed'
      );
    });

    it('should handle unexpected errors with 500 status', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity);
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockRejectedValue(new Error('Unexpected error'));

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(500);
      expect(await response.text()).toBe('Internal server error');
      expect(ActivityPubLogs.federation.incomingError).toHaveBeenCalledWith(
        'POST',
        url.toString(),
        'Unexpected error'
      );
    });

    it('should extract source IP from headers', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity, {
        headers: { 
          signature: createTestSignature(),
          'x-forwarded-for': '***********'
        }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue(mockValidationSuccess);
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(activity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockResolvedValue(activity);

      // Act
      await POST({ request, url });

      // Assert
      expect(validateInboxRequest).toHaveBeenCalledWith(
        'POST',
        url.toString(),
        expect.objectContaining({
          'x-forwarded-for': '***********'
        }),
        JSON.stringify(activity),
        '***********'
      );
    });

    it('should handle different activity types', async () => {
      // Arrange
      const deleteActivity = createTestDeleteActivity('https://example.com/notes/1');
      const request = createMockInboxRequest(deleteActivity, {
        headers: { signature: createTestSignature() }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue({
        valid: true,
        activity: deleteActivity,
        signature: mockValidationSuccess.signature
      });
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity: deleteActivity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(deleteActivity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockResolvedValue(deleteActivity);

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(200);
      expect(processActivityObject).toHaveBeenCalledWith(deleteActivity);
    });

    it('should return 500 for retryable errors', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity, {
        headers: { signature: createTestSignature() }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue(mockValidationSuccess);
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(activity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockRejectedValue(new Error('Network timeout'));

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(500); // Retryable error
      expect(await response.text()).toBe('Network timeout');
    });

    it('should return 400 for non-retryable errors', async () => {
      // Arrange
      const activity = createTestCreateActivity();
      const request = createMockInboxRequest(activity, {
        headers: { signature: createTestSignature() }
      });
      const url = new URL('https://test.example.com/users/testuser/inbox');

      vi.mocked(validateInboxRequest).mockResolvedValue(mockValidationSuccess);
      vi.mocked(createInboxProcessingContext).mockReturnValue({
        activity,
        direction: 'inbound',
        signature: mockValidationSuccess.signature,
        rawActivity: JSON.stringify(activity),
        receivedAt: new Date()
      });
      vi.mocked(processActivityObject).mockRejectedValue(new Error('Invalid activity format'));

      // Act
      const response = await POST({ request, url });

      // Assert
      expect(response.status).toBe(400); // Non-retryable error
      expect(await response.text()).toBe('Invalid activity format');
    });
  });
});
