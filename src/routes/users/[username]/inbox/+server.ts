import { validateInboxRequest, createInboxProcessingContext } from '$lib/activitypub/utils/inbox-validation';
import { processActivityObject } from '$lib/activitypub/activities';
import { ActivityPubLogs } from '$lib/activitypub/utils/logger';
import type { ActivityProcessingResult, ActivityProcessingContext } from '$lib/activitypub/types/federation';
import type { Activity } from '$lib/activitypub/types';

/**
 * Process activity for inbox with proper result format
 */
async function processInboxActivity(activity: Activity, context: ActivityProcessingContext): Promise<ActivityProcessingResult> {
  try {
    // processActivityObject returns the processed activity and throws on error
    const processedActivity = await processActivityObject(activity);

    return {
      success: true,
      status: 'completed',
      activityId: processedActivity.id?.toString()
    };
  } catch (error) {
    // Determine if error is retryable based on error type
    const shouldRetry = isRetryableError(error);

    return {
      success: false,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      shouldRetry
    };
  }
}

/**
 * Determine if an error should trigger a retry
 */
function isRetryableError(error: unknown): boolean {
  if (!(error instanceof Error)) {
    return false;
  }

  // Network errors, timeouts, and temporary failures should be retried
  const retryablePatterns = [
    /network/i,
    /timeout/i,
    /connection/i,
    /temporary/i,
    /rate.?limit/i,
    /server.?error/i,
    /503/,
    /502/,
    /504/
  ];

  return retryablePatterns.some(pattern => pattern.test(error.message));
}

export async function POST({ request, url }: { request: Request; url: URL }) {
  console.log('Inbox request received');
  try {
    // Extract request details
    const method = request.method;
    const headers: Record<string, string> = {};

    request.headers.forEach((value, key) => {
      headers[key.toLowerCase()] = value;
    });

    const rawBody = await request.text();
    const sourceIp = headers['x-forwarded-for'] || headers['x-real-ip'];

    // Validate the incoming request
    const validation = await validateInboxRequest(
      method,
      url.toString(),
      headers,
      rawBody,
      sourceIp
    );

    console.log('Preparing to validate')

    if (!validation.valid) {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        validation.error || 'Validation failed'
      );

      return new Response(validation.error || 'Invalid request', {
        status: validation.statusCode || 400
      });
    }

    // Create processing context
    const context = createInboxProcessingContext(
      validation.activity!,
      validation.signature,
      rawBody,
      sourceIp
    );

    ActivityPubLogs.federation.incomingRequest(method, url.toString());

    // Process the activity
    const result = await processInboxActivity(validation.activity!, context);

    if (result.success) {
      ActivityPubLogs.federation.incomingResponse(method, url.toString(), 200);
      return new Response('OK', { status: 200 });
    } else {
      ActivityPubLogs.federation.incomingError(
        method,
        url.toString(),
        result.error || 'Processing failed'
      );

      return new Response(result.error || 'Processing failed', {
        status: result.shouldRetry ? 500 : 400
      });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    ActivityPubLogs.federation.incomingError(
      request.method,
      url.toString(),
      errorMessage
    );

    console.error('Inbox processing error:', error);

    return new Response('Internal server error', { status: 500 });
  }
}
