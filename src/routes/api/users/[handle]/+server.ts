import { discoverActor } from '$lib/activitypub/actors';
import { startBackfill } from '$lib/activitypub/backfill';

export async function GET({ params }) {
  const user = await discoverActor(params.handle);
  if (!user) {
    return new Response('Not found', { status: 404 });
  }
  // Не ждём результат бэкфилла, потому что мы за одну ниточку можем половину федивёрса вытянуть
  // noinspection ES6MissingAwait
  startBackfill(user);
  return new Response(JSON.stringify(user));
}
