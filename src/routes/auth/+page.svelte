<script lang="ts">
    import Block from '$lib/components/design_system/Block.svelte';
    import Button from '$lib/components/design_system/Button.svelte';
    import ContentWrapper from '$lib/components/design_system/ContentWrapper.svelte';
    import Header from '$lib/components/design_system/Header.svelte';
    import Input from '$lib/components/design_system/Input.svelte';
    import type { ActionData } from './$types';
    import { enhance } from '$app/forms';

    let { form }: { form: ActionData } = $props();

    let loading = $state(false);
</script>

<ContentWrapper style="display: flex; flex-direction: column; justify-content: center; min-height: 100vh; max-width: 400px">
    <Block style="display: flex; flex-direction: column;">
        <Header style="text-align: center">Войти в FireShark</Header>

        {#if form?.error}
            <div style="background: #fee; border: 1px solid #fcc; color: #c33; padding: 12px; border-radius: 8px; margin-bottom: 16px; text-align: center;">
                {form.error}
            </div>
        {/if}

        <form
            method="POST"
            style="display: flex; flex-direction: column; gap: 16px; align-items: end"
            use:enhance={() => {
                loading = true;
                return async ({ update }) => {
                    loading = false;
                    await update();
                };
            }}
        >
            <Input
                type="text"
                name="login"
                placeholder="Имя пользователя"
                style="width: 100%"
                value={form?.username || ''}
                disabled={loading}
                required
            />
            <Input
                type="password"
                name="password"
                placeholder="Пароль"
                style="width: 100%"
                disabled={loading}
                required
            />
            <Button type="submit" variant="primary" disabled={loading}>
                {loading ? 'Вход...' : 'Войти'}
            </Button>
        </form>
    </Block>
</ContentWrapper>