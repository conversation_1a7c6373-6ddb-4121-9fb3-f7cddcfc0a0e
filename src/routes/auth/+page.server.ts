import { fail, redirect, isRedirect } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { authenticateUser } from '$lib/server/auth';
import { loginUser, getCurrentUser } from '$lib/server/session';

export const load: PageServerLoad = async ({ cookies }) => {
  // Если пользователь уже авторизован, перенаправляем на главную
  const currentUser = await getCurrentUser(cookies);
  
  if (currentUser) {
    throw redirect(302, '/');
  }

  return {};
};

export const actions: Actions = {
  default: async ({ request, cookies }) => {
    const data = await request.formData();
    const username = data.get('login')?.toString();
    const password = data.get('password')?.toString();

    // Валидация входных данных
    if (!username || !password) {
      return fail(400, {
        error: 'Необходимо указать имя пользователя и пароль',
        username
      });
    }

    if (username.length < 3) {
      return fail(400, {
        error: 'Имя пользователя должно содержать минимум 3 символа',
        username
      });
    }

    if (password.length < 8) {
      return fail(400, {
        error: 'Пароль должен содержать минимум 8 символов',
        username
      });
    }

    try {
      // Аутентификация пользователя
      const user = await authenticateUser(username, password);

      if (!user) {
        return fail(401, {
          error: 'Неверное имя пользователя или пароль',
          username
        });
      }

      // Создание сессии
      loginUser(cookies, user.id);

      // Перенаправление на главную страницу
      throw redirect(302, '/');

    } catch (error) {
      // Если это redirect, пробрасываем его дальше
      if (isRedirect(error)) {
        throw error;
      }

      console.error('Ошибка при авторизации:', error);

      return fail(500, {
        error: 'Произошла ошибка при авторизации. Попробуйте позже.',
        username
      });
    }
  }
};
