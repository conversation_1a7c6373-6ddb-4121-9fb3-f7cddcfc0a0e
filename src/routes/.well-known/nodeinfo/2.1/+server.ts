import { json } from '@sveltejs/kit';
import { user } from '$lib/server/db/schema';
import { isNull, sql } from 'drizzle-orm';
import { db } from '$lib/server/db';

export async function GET() {
  const totalLocalUsersResults = await db.select({
    total: sql<number>`count(*)`
  }).from(user)
    .where(isNull(user.domain))
    .limit(1);

  const totalLocalUsers = totalLocalUsersResults[0]?.total ?? 0

  return json({
    version: '2.1',
    software: {
      name: 'FireShark',
      version: '2025.08.01',
      homepage: 'https://fireshark.social'
    },
    protocols: ['activitypub'],
    services: {
      inbound: [],
      outbound: []
    },
    openRegistration: false,
    usage: {
      users: {
        total: totalLocalUsers,
        activeMonth: totalLocalUsers,
        activeHalfyear: totalLocalUsers
      }
    },
    metadata: {
      nodeName: 'FireShark',
      nodeDescription: 'I eat firefish for lunch!',
      maintainer: {
        name: 'dotterian',
        email: '<EMAIL>'
      },
      langs: ['ru']
    }
  })
}
