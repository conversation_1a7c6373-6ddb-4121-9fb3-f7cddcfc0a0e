<script lang="ts">
  import { onMount, type Snippet } from 'svelte';
  import { initializeUser, currentUser } from '$lib/stores/auth';
  import type { LayoutData } from './$types';
  import ContentWrapper from '$lib/components/design_system/ContentWrapper.svelte';
  import ToastManager from '$lib/components/design_system/Toasts/index.svelte';

  let { data, children }: { data: LayoutData; children: Snippet } = $props();

  // Инициализация пользователя при загрузке
  onMount(() => {
    initializeUser(data.user);
  });

  // Обновление пользователя при изменении данных
  $effect(() => {
    initializeUser(data.user);
  });

  // Реактивная проверка авторизации
  const isAuthenticated = $derived($currentUser !== null);
</script>

<div class="layout-grid">
  {#if isAuthenticated}
    <div class="sidebar"></div>
  {:else}
    <div></div>
  {/if}
  <div class="main-content">
    <ContentWrapper>
      {@render children()}
    </ContentWrapper>
  </div>
  {#if isAuthenticated}
    <div class="sidebar sidebar-right"></div>
  {:else}
    <div></div>
  {/if}
</div>

<ToastManager />

<style>
  .layout-grid {
    display: grid;
    grid-template-columns: 250px 1fr 250px;
    height: 100vh;
  }

  .sidebar {
    background-color: var(--layer-1-bg);
  }

  .main-content {
    overflow-y: auto;
  }

  @media (width <= 1030px) {
    .layout-grid {
      grid-template-columns: 250px 1fr;
    }

    .sidebar-right {
      display: none;
    }
  }

  @media (width <= 768px) {
    .layout-grid {
      grid-template-columns: 1fr;
    }

    .sidebar {
      display: none;
    }
  }
</style>
