import type { Activity, Create, Note, Person } from '$lib/activitypub/types';

/**
 * Test data factories for ActivityPub objects
 */

export function createTestPerson(overrides: Partial<Person> = {}): Person {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Person',
    id: 'https://example.com/users/testuser',
    preferredUsername: 'testuser',
    name: 'Test User',
    inbox: new URL('https://example.com/users/testuser/inbox'),
    outbox: new URL('https://example.com/users/testuser/outbox'),
    followers: new URL('https://example.com/users/testuser/followers'),
    following: new URL('https://example.com/users/testuser/following'),
    publicKey: {
      id: 'https://example.com/users/testuser#main-key',
      owner: 'https://example.com/users/testuser',
      publicKeyPem: '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----<PERSON><PERSON> PUBLIC KEY-----'
    },
    ...overrides
  };
}

export function createTestNote(overrides: Partial<Note> = {}): Note {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Note',
    id: 'https://example.com/notes/1',
    content: 'This is a test note',
    attributedTo: 'https://example.com/users/testuser',
    published: new Date().toISOString(),
    to: ['https://www.w3.org/ns/activitystreams#Public'],
    cc: ['https://example.com/users/testuser/followers'],
    ...overrides
  };
}

export function createTestCreateActivity(overrides: Partial<Create> = {}): Create {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Create',
    id: 'https://example.com/activities/1',
    actor: 'https://example.com/users/testuser',
    object: createTestNote(),
    published: new Date().toISOString(),
    to: ['https://www.w3.org/ns/activitystreams#Public'],
    cc: ['https://example.com/users/testuser/followers'],
    ...overrides
  };
}

export function createTestDeleteActivity(objectId: string): Activity {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Delete',
    id: 'https://example.com/activities/delete-1',
    actor: 'https://example.com/users/testuser',
    object: objectId,
    published: new Date().toISOString()
  };
}

export function createTestFollowActivity(target: string): Activity {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Follow',
    id: 'https://example.com/activities/follow-1',
    actor: 'https://example.com/users/testuser',
    object: target,
    published: new Date().toISOString()
  };
}

export function createTestAcceptActivity(followActivityUri: string): any {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Accept',
    id: 'https://example.com/activities/accept-1',
    actor: 'https://example.com/users/targetuser',
    object: followActivityUri,
    published: new Date().toISOString()
  };
}

export function createTestBlockActivity(blockedUserUri: string): any {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Block',
    id: 'https://example.com/activities/block-1',
    actor: 'https://example.com/users/blocker',
    object: blockedUserUri,
    published: new Date().toISOString()
  };
}

export function createTestAddActivity(objectUri: string, targetUri: string): any {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Add',
    id: 'https://example.com/activities/add-1',
    actor: 'https://example.com/users/actor',
    object: objectUri,
    target: targetUri,
    published: new Date().toISOString()
  };
}

export function createTestRemoveActivity(objectUri: string, targetUri: string): any {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Remove',
    id: 'https://example.com/activities/remove-1',
    actor: 'https://example.com/users/actor',
    object: objectUri,
    target: targetUri,
    published: new Date().toISOString()
  };
}

/**
 * Create a mock HTTP request for testing inbox
 */
export function createMockInboxRequest(activity: Activity, options: {
  method?: string;
  headers?: Record<string, string>;
  signature?: string;
} = {}): Request {
  const body = JSON.stringify(activity);
  const headers = new Headers({
    'content-type': 'application/activity+json',
    'user-agent': 'Test/1.0',
    ...options.headers
  });

  if (options.signature) {
    headers.set('signature', options.signature);
  }

  return new Request('https://test.example.com/users/testuser/inbox', {
    method: options.method || 'POST',
    headers,
    body
  });
}

/**
 * Create a valid HTTP signature for testing
 */
export function createTestSignature(keyId: string = 'https://example.com/users/testuser#main-key'): string {
  return `keyId="${keyId}",algorithm="rsa-sha256",headers="(request-target) host date digest",signature="test-signature"`;
}

/**
 * Mock successful validation response
 */
export const mockValidationSuccess = {
  valid: true,
  activity: createTestCreateActivity(),
  signature: {
    keyId: 'https://example.com/users/testuser#main-key',
    algorithm: 'rsa-sha256',
    headers: ['(request-target)', 'host', 'date', 'digest'],
    signature: 'test-signature'
  }
};

/**
 * Mock failed validation response
 */
export const mockValidationFailure = {
  valid: false,
  error: 'Invalid signature',
  statusCode: 401
};
