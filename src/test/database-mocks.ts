import { vi } from 'vitest';

/**
 * Common database mocks for ActivityPub tests
 * 
 * This module provides standardized database mocks that can be reused
 * across all ActivityPub test files to eliminate duplication.
 */

/**
 * Create a mock database query object with all common tables
 */
export function createMockDbQuery() {
  return {
    user: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    },
    activity: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    },
    post: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    },
    follow: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    },
    emoji: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    },
    notification: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    }
  };
}

/**
 * Create a mock database insert function with chainable methods
 */
export function createMockDbInsert() {
  return vi.fn().mockReturnValue({
    values: vi.fn().mockReturnValue({
      returning: vi.fn().mockResolvedValue([])
    }),
    returning: vi.fn().mockResolvedValue([])
  });
}

/**
 * Create a mock database update function with chainable methods
 */
export function createMockDbUpdate() {
  return vi.fn().mockReturnValue({
    set: vi.fn().mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([])
      })
    })
  });
}

/**
 * Create a mock database delete function with chainable methods
 */
export function createMockDbDelete() {
  return vi.fn().mockReturnValue({
    where: vi.fn().mockReturnValue({
      returning: vi.fn().mockResolvedValue([])
    })
  });
}

/**
 * Create a mock database select function with chainable methods
 */
export function createMockDbSelect() {
  return vi.fn().mockReturnValue({
    from: vi.fn().mockReturnValue({
      where: vi.fn().mockResolvedValue([])
    })
  });
}

/**
 * Create a complete database mock object
 */
export function createMockDb() {
  return {
    query: createMockDbQuery(),
    insert: createMockDbInsert(),
    update: createMockDbUpdate(),
    delete: createMockDbDelete(),
    select: createMockDbSelect()
  };
}

/**
 * Standard database mock for use in vi.mock()
 */
export const mockDb = createMockDb();

/**
 * Helper to reset all database mocks
 */
export function resetDbMocks() {
  vi.clearAllMocks();
  
  // Reset the mock implementations
  Object.values(mockDb.query).forEach(table => {
    if (typeof table === 'object') {
      Object.values(table).forEach(method => {
        if (vi.isMockFunction(method)) {
          method.mockReset();
        }
      });
    }
  });
  
  // Reset main db methods
  if (vi.isMockFunction(mockDb.insert)) mockDb.insert.mockReset();
  if (vi.isMockFunction(mockDb.update)) mockDb.update.mockReset();
  if (vi.isMockFunction(mockDb.delete)) mockDb.delete.mockReset();
  if (vi.isMockFunction(mockDb.select)) mockDb.select.mockReset();
}

/**
 * Common mock implementations for typical database operations
 */
export const commonMockImplementations = {
  /**
   * Mock successful insert operation
   */
  successfulInsert: (returnValue: any = { id: 'test-id' }) => {
    mockDb.insert.mockReturnValue({
      values: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([returnValue])
      }),
      returning: vi.fn().mockResolvedValue([returnValue])
    } as any);
  },

  /**
   * Mock successful update operation
   */
  successfulUpdate: (returnValue: any = { id: 'test-id' }) => {
    mockDb.update.mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([returnValue])
        })
      })
    } as any);
  },

  /**
   * Mock successful delete operation
   */
  successfulDelete: (returnValue: any = { id: 'test-id' }) => {
    mockDb.delete.mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([returnValue])
      })
    } as any);
  },

  /**
   * Mock failed operation (no rows affected)
   */
  failedOperation: () => {
    const emptyResult = { returning: vi.fn().mockResolvedValue([]) };
    
    mockDb.insert.mockReturnValue({
      values: vi.fn().mockReturnValue(emptyResult),
      returning: vi.fn().mockResolvedValue([])
    } as any);
    
    mockDb.update.mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue(emptyResult)
      })
    } as any);
    
    mockDb.delete.mockReturnValue({
      where: vi.fn().mockReturnValue(emptyResult)
    } as any);
  },

  /**
   * Mock finding existing record
   */
  findExisting: (table: keyof typeof mockDb.query, returnValue: any) => {
    const tableMock = mockDb.query[table] as any;
    if (tableMock?.findFirst) {
      tableMock.findFirst.mockResolvedValue(returnValue);
    }
  },

  /**
   * Mock not finding record
   */
  findNothing: (table: keyof typeof mockDb.query) => {
    const tableMock = mockDb.query[table] as any;
    if (tableMock?.findFirst) {
      tableMock.findFirst.mockResolvedValue(null);
    }
  }
};

/**
 * Database mock factory for vi.mock() calls
 */
export const createDbMock = () => ({
  db: mockDb
});
