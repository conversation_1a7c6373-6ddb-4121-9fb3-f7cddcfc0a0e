import { vi } from 'vitest';

// Mock environment variables
vi.mock('$env/dynamic/private', () => ({
  env: {
    DOMAIN: 'test.example.com',
    BASE_URL: 'https://test.example.com',
    REQUIRE_HTTP_SIGNATURE: 'false',
    INBOX_RATE_LIMIT: '100',
    OUTBOX_RATE_LIMIT: '50',
    DISCOVERY_RATE_LIMIT: '20',
    PROCESSING_MAX_CONCURRENT: '20',
    PROCESSING_TIMEOUT: '30000',
    PROCESSING_DEDUP_WINDOW: '300000',
    ALLOWED_ACTOR_TYPES: 'Person,Service,Application',
    BLOCKED_DOMAINS: '',
    ALLOWED_DOMAINS: '',
    MAX_ACTIVITY_SIZE: '1048576',
    MAX_ATTACHMENT_SIZE: '10485760',
    ENABLE_INBOX: 'true',
    ENABLE_OUTBOX: 'true',
    ENABLE_WEBFINGER: 'true',
    ENABLE_NODEINFO: 'true',
    ENABLE_SHARED_INBOX: 'true'
  }
}));

// Mock database using common mocks
vi.mock('$lib/server/db', () => ({
  db: {
    query: {
      user: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      },
      activity: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      },
      post: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      },
      follow: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      },
      emoji: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      },
      notification: {
        findFirst: vi.fn(),
        findMany: vi.fn()
      }
    },
    insert: vi.fn().mockReturnValue({
      values: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([])
      }),
      returning: vi.fn().mockResolvedValue([])
    }),
    update: vi.fn().mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([])
        })
      })
    }),
    delete: vi.fn().mockReturnValue({
      where: vi.fn().mockReturnValue({
        returning: vi.fn().mockResolvedValue([])
      })
    }),
    select: vi.fn().mockReturnValue({
      from: vi.fn().mockReturnValue({
        where: vi.fn().mockResolvedValue([])
      })
    })
  }
}));

// Global test utilities
global.fetch = vi.fn();
