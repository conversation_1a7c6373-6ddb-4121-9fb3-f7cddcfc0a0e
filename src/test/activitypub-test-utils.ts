import { vi } from 'vitest';
import { resetDbMocks, mockDb, commonMockImplementations } from './database-mocks';

/**
 * Common test utilities for ActivityPub tests
 * 
 * This module provides standardized test utilities, mocks, and helpers
 * that can be reused across all ActivityPub test files.
 */

/**
 * Common ActivityPub module mocks
 */
export const activityPubMocks = {
  /**
   * Mock for actors module
   */
  actors: () => ({
    discoverActor: vi.fn()
  }),

  /**
   * Mock for link/URL utilities
   */
  links: () => ({
    getUrlRequired: vi.fn(),
    getUrlFromAPObject: vi.fn(),
    getUrlSafe: vi.fn()
  }),

  /**
   * Mock for posts module
   */
  posts: () => ({
    importPost: vi.fn(),
    deletePost: vi.fn(),
    getVisibilityFromPost: vi.fn()
  }),

  /**
   * Mock for utils module
   */
  utils: () => ({
    fetchAndProcessAPObject: vi.fn(),
    isCreateActivity: vi.fn(),
    processActivitySafely: vi.fn().mockReturnValue({ success: true }),
    ActivityProcessors: {
      processCreate: vi.fn(),
      processDelete: vi.fn(),
      processFollow: vi.fn(),
      processAccept: vi.fn(),
      processBlock: vi.fn(),
      processAdd: vi.fn(),
      processRemove: vi.fn()
    },
    getActivitySummary: vi.fn().mockReturnValue({ type: 'Create' }),
    FetchError: class FetchError extends Error {
      constructor(message: string, public code?: string, public context?: any) {
        super(message);
      }
    },
    withErrorRecovery: vi.fn(),
    processActivityObjects: vi.fn()
  }),

  /**
   * Mock for types utilities
   */
  types: () => ({
    getObjectType: vi.fn()
  })
};

/**
 * Standard mock configurations for different test scenarios
 */
export const mockConfigurations = {
  /**
   * Setup mocks for successful actor discovery
   */
  successfulActorDiscovery: (actorData: any = { id: 'test-actor', uri: 'https://example.com/users/test' }) => {
    const actorsMock = activityPubMocks.actors();
    actorsMock.discoverActor.mockResolvedValue(actorData);
    return actorsMock;
  },

  /**
   * Setup mocks for failed actor discovery
   */
  failedActorDiscovery: () => {
    const actorsMock = activityPubMocks.actors();
    actorsMock.discoverActor.mockResolvedValue(null);
    return actorsMock;
  },

  /**
   * Setup mocks for successful URL extraction
   */
  successfulUrlExtraction: (url: string = 'https://example.com/test') => {
    const linksMock = activityPubMocks.links();
    linksMock.getUrlRequired.mockReturnValue(url);
    linksMock.getUrlFromAPObject.mockReturnValue(url);
    linksMock.getUrlSafe.mockReturnValue(url);
    return linksMock;
  },

  /**
   * Setup mocks for failed URL extraction
   */
  failedUrlExtraction: () => {
    const linksMock = activityPubMocks.links();
    linksMock.getUrlRequired.mockImplementation(() => {
      throw new Error('Invalid URL');
    });
    linksMock.getUrlFromAPObject.mockReturnValue(null);
    linksMock.getUrlSafe.mockReturnValue(null);
    return linksMock;
  }
};

/**
 * Test data factories for common ActivityPub objects
 */
export const testDataFactories = {
  /**
   * Create test user data
   */
  createTestUser: (overrides: any = {}) => ({
    id: 'test-user-id',
    uri: 'https://example.com/users/testuser',
    username: 'testuser',
    domain: 'example.com',
    manuallyApprovedFollowers: false,
    featured: 'https://example.com/users/testuser/featured',
    outbox: 'https://example.com/users/testuser/outbox',
    ...overrides
  }),

  /**
   * Create test activity data
   */
  createTestActivity: (overrides: any = {}) => ({
    id: 'test-activity-id',
    type: 'Create',
    actorUri: 'https://example.com/users/testuser',
    objectUri: 'https://example.com/posts/123',
    targetUri: null,
    status: 'completed',
    createdAt: new Date(),
    ...overrides
  }),

  /**
   * Create test follow data
   */
  createTestFollow: (overrides: any = {}) => ({
    id: 'test-follow-id',
    followerId: 'follower-id',
    followerUri: 'https://example.com/users/follower',
    followingId: 'following-id',
    followingUri: 'https://example.com/users/following',
    status: 'accepted',
    isLocal: false,
    requestedAt: new Date(),
    acceptedAt: new Date(),
    ...overrides
  }),

  /**
   * Create test post data
   */
  createTestPost: (overrides: any = {}) => ({
    id: 'test-post-id',
    uri: 'https://example.com/posts/123',
    content: 'Test post content',
    authorId: 'test-user-id',
    createdAt: new Date(),
    ...overrides
  })
};

/**
 * Common test setup and teardown utilities
 */
export class ActivityPubTestBase {
  /**
   * Setup common mocks before each test
   */
  static setupMocks() {
    resetDbMocks();
    
    // Setup default successful behaviors
    mockConfigurations.successfulActorDiscovery();
    mockConfigurations.successfulUrlExtraction();
  }

  /**
   * Cleanup after each test
   */
  static cleanup() {
    resetDbMocks();
    vi.clearAllMocks();
  }

  /**
   * Assert that database operations were called correctly
   */
  static assertDbOperations = {
    /**
     * Assert that insert was called with correct data
     */
    insertCalled: (expectedData?: any) => {
      expect(mockDb.insert).toHaveBeenCalled();
      if (expectedData) {
        expect(mockDb.insert).toHaveBeenCalledWith(expect.anything());
      }
    },

    /**
     * Assert that update was called with correct data
     */
    updateCalled: (expectedData?: any) => {
      expect(mockDb.update).toHaveBeenCalled();
      if (expectedData) {
        const updateCall = vi.mocked(mockDb.update).mock.calls[0];
        expect(updateCall).toBeDefined();
      }
    },

    /**
     * Assert that query was called for specific table
     */
    queryCalled: (table: keyof typeof mockDb.query, method: string = 'findFirst') => {
      const tableMock = mockDb.query[table] as any;
      expect(tableMock[method]).toHaveBeenCalled();
    },

    /**
     * Assert that no database operations were called
     */
    noDbOperations: () => {
      expect(mockDb.insert).not.toHaveBeenCalled();
      expect(mockDb.update).not.toHaveBeenCalled();
      expect(mockDb.delete).not.toHaveBeenCalled();
    }
  };
}

/**
 * Export commonly used test utilities
 */
export {
  resetDbMocks,
  mockDb,
  commonMockImplementations
};
