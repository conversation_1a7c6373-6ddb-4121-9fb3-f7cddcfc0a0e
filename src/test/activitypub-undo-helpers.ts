import type { Activity, Undo, Like, Announce, Follow, Block } from '$lib/activitypub/types';

/**
 * Helper functions for creating test Undo activities
 */

export function createTestUndoLikeActivity(actorUri: string, likeActivityUri: string): Undo {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-like-${Date.now()}`,
    actor: actor<PERSON><PERSON>,
    object: likeActivityUri,
    published: new Date().toISOString()
  };
}

export function createTestUndoLikeActivityEmbedded(actorUri: string, objectUri: string): Undo {
  const likeActivity: Like = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Like',
    id: `${actorUri}/activities/like-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };

  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-like-${Date.now()}`,
    actor: actor<PERSON><PERSON>,
    object: likeActivity,
    published: new Date().toISOString()
  };
}

export function createTestUndoAnnounceActivity(actorUri: string, announceActivityUri: string): Undo {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-announce-${Date.now()}`,
    actor: actorUri,
    object: announceActivityUri,
    published: new Date().toISOString()
  };
}

export function createTestUndoAnnounceActivityEmbedded(actorUri: string, objectUri: string): Undo {
  const announceActivity: Announce = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Announce',
    id: `${actorUri}/activities/announce-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };

  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-announce-${Date.now()}`,
    actor: actorUri,
    object: announceActivity,
    published: new Date().toISOString()
  };
}

export function createTestUndoFollowActivity(actorUri: string, followActivityUri: string): Undo {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-follow-${Date.now()}`,
    actor: actorUri,
    object: followActivityUri,
    published: new Date().toISOString()
  };
}

export function createTestUndoFollowActivityEmbedded(actorUri: string, objectUri: string): Undo {
  const followActivity: Follow = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Follow',
    id: `${actorUri}/activities/follow-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };

  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-follow-${Date.now()}`,
    actor: actorUri,
    object: followActivity,
    published: new Date().toISOString()
  };
}

export function createTestUndoBlockActivity(actorUri: string, blockActivityUri: string): Undo {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-block-${Date.now()}`,
    actor: actorUri,
    object: blockActivityUri,
    published: new Date().toISOString()
  };
}

export function createTestUndoBlockActivityEmbedded(actorUri: string, objectUri: string): Undo {
  const blockActivity: Block = {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Block',
    id: `${actorUri}/activities/block-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };

  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Undo',
    id: `${actorUri}/activities/undo-block-${Date.now()}`,
    actor: actorUri,
    object: blockActivity,
    published: new Date().toISOString()
  };
}

export function createTestLikeActivity(actorUri: string, objectUri: string): Like {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Like',
    id: `${actorUri}/activities/like-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };
}

export function createTestAnnounceActivity(actorUri: string, objectUri: string): Announce {
  return {
    '@context': 'https://www.w3.org/ns/activitystreams',
    type: 'Announce',
    id: `${actorUri}/activities/announce-${Date.now()}`,
    actor: actorUri,
    object: objectUri,
    published: new Date().toISOString()
  };
}
